import express from "express";
import { getAgreementDetailsRouter } from "./get-agreement-details/get-agreement-details";
import { getAgreementsForUserRouter } from "./get-agreements-for-user/get-agreements-for-user";
import { getEventLogStatisticsRouter } from "./get-event-log-statistics/get-event-log-statistics";
import { signedAgreementRouter } from "./signed-agreement/signed-agreement";

const router = express.Router();

router.use(getAgreementsForUserRouter);
router.use(signedAgreementRouter);
router.use(getAgreementDetailsRouter);
router.use(getEventLogStatisticsRouter);

export default router;
