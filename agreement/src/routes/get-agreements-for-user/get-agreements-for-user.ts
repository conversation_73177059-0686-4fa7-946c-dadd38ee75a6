import { currentUser, requireAuth, response<PERSON><PERSON><PERSON> } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { Agreement } from "../../models/agreement";
import { SignedAgreement } from "../../models/signed-agreement";

const router = express.Router();

router.get("/v1/agreements/mine",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "ListSignedAgreementsByUser";

            const userId = req.currentUser?.id as string;

            // Fetch all the agreements
            const agreements = await Agreement.find({}, { title: 1, type: 1, retired: 1 }).lean().exec();

            // Fetch user's signed agreements
            const usersAgreements = await SignedAgreement.find({ userId }, { agreementId: 1 }).lean().exec();
            const agreementIds = usersAgreements.map(agreement => String(agreement.agreementId));

            const finalResp = agreements.map(agreement => {
                const id = String(agreement._id);

                return {
                    id,
                    title: agreement.title,
                    type: agreement.type,
                    retired: agreement.retired,
                    signed: agreementIds.includes(String(agreement._id))
                };
            });

            res.sendResponse(finalResp, {});
        }
        catch (error) {
            console.error("Agreement.GetAgreementsForUser");
            console.error(error);
            next(error);
        }
    });

export { router as getAgreementsForUserRouter };
