import { currentUser, NotFoundCode, require<PERSON><PERSON>, ResourceNotFoundError, response<PERSON><PERSON>ler, validateRequest } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { param } from "express-validator";
import { Agreement } from "../../models/agreement";

const router = express.Router();

router.get("/v1/agreements/:agreementId",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    [
        param("agreementId")
            .isMongoId(),
    ],
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "GetAgreementDetails";

            const { agreementId } = req.params;

            const agreement = await Agreement.findById(agreementId);
            if (!agreement) {
                throw new ResourceNotFoundError(NotFoundCode.AGREEMENT_NOT_FOUND, "Agreement not found.");
            }

            res.sendResponse(agreement, {});
        }
        catch (error) {
            console.error("Agreement.GetAgreementDetails");
            console.error(error);
            next(error);
        }
    });

export { router as getAgreementDetailsRouter };
