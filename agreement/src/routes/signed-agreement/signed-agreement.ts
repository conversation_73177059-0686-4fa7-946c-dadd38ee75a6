import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError, responseHandler, TargetType,
    validateRequest,
} from "@moxfive-llc/common";
import { body } from "express-validator";
import { Agreement } from "../../models/agreement";
import { SignedAgreement } from "../../models/signed-agreement";
import { getUsersByIds } from "../../utils";
import { UserAgreementSignedPublisher } from "../../events/publishers/user-agreement-signed";
import { natsWrapper } from "../../nats-wrapper";

const router = express.Router();

router.post(
    "/v1/agreements/sign",
    responseHandler,
    currentUser,
    requireAuth,
    [
        body("agreementId")
            .exists().bail()
            .isMongoId().withMessage("Agreement Id must be valid.")
    ],
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "SignAgreement";

            const userId = req.currentUser?.id as string;
            const { agreementId }: { agreementId: string } = req.body;

            // Check whether the agreement is valid or not
            const agreementDetails = await Agreement.findById(String(agreementId)).lean().exec();
            if (!agreementDetails) {
                throw new ResourceNotFoundError(NotFoundCode.AGREEMENT_NOT_FOUND, "Agreement not found.");
            }

            // If agreement is already signed then ignore
            const alreadySigned = await SignedAgreement.findOne({ userId: String(userId), agreementId: String(agreementId) }).lean().exec();
            if (alreadySigned) {
                return res.sendResponse({
                    id: String(alreadySigned._id),
                    meta: {
                        message: "Agreement signed successfully.",
                    },
                }, {});
            }

            // Log user's request metadata
            const requestMetadata = {
                userAgent: req.headers["user-agent"] || "",
                ip: req.ip,
                referer: req.headers["referer"] || "",
                origin: req.hostname || "",
                acceptEncoding: typeof req.headers["accept-encoding"] === "string" ? req.headers["accept-encoding"] : (req.headers["accept-encoding"] || []).join(", "),
                contentType: req.headers["content-type"] || "",
                contentLength: req.headers["content-length"] || "",
                accept: req.headers["accept"] || ""
            };

            const signedAgreement = await SignedAgreement.build({
                userId: userId as string,
                agreementId,
                requestMetadata
            }).save();

            const userNameByIdMap = await getUsersByIds([userId as string]);

            // Publish an event
            const data = {
                id: String(signedAgreement._id),
                agreementId: agreementId,
                userId: userId,
                createdAt: signedAgreement.createdAt
            };

            await new UserAgreementSignedPublisher(natsWrapper.client).publish(data);

            return res.sendResponse({
                id: String(signedAgreement._id),
                meta: {
                    message: "Agreement signed successfully.",
                },
            }, {
                targets: [{
                    type: TargetType.AGREEMENT,
                    details: {
                        id: agreementId,
                        name: agreementDetails.title
                    }
                }],
                modifiedProperties: [{
                    target: TargetType.AGREEMENT,
                    propertyName: "userId",
                    oldValue: "",
                    newValue: JSON.stringify({
                        id: userId,
                        value: userNameByIdMap.get(String(userId))
                    })
                }, {
                    target: TargetType.AGREEMENT,
                    propertyName: "agreementId",
                    oldValue: "",
                    newValue: JSON.stringify({
                        id: agreementId,
                        value: agreementDetails.title
                    })
                }, {
                    target: TargetType.AGREEMENT,
                    propertyName: "requestMetadata",
                    oldValue: "",
                    newValue: JSON.stringify(requestMetadata)
                }]
            });
        }
        catch (error) {
            console.error("Agreement.SignedAgreement");
            console.error(error);
            next(error);
        }
    }
);

export { router as signedAgreementRouter };
