import { User } from "../models/user";

export const getUserName = ({
    firstName,
    lastName,
    displayName,
}: {
    firstName: string | null;
    lastName: string | null;
    displayName: string;
}) => {
    if (!firstName && !lastName) {
        return displayName;
    }
    let name = "";
    if (firstName) {
        name += firstName;
        if (lastName) {
            name += " ";
        }
    }
    if (lastName) {
        name += lastName;
    }
    return name;
};

export const getUsersByIds = async (usersIds: string[]) => {
    const userIds = new Set([...usersIds]);
    const modifiedByUsers = await User.find({ _id: [...userIds] }, { firstName: 1, lastName: 1, displayName: 1, policyIds: 1 }).lean().exec();
    const modifiedByUsersMap = new Map();
    modifiedByUsers.forEach(user =>
        modifiedByUsersMap.set(
            String(user._id),
            getUserName(
                {
                    firstName: user.firstName,
                    lastName: user.lastName,
                    displayName: user.displayName
                }
            )
        )
    );

    return modifiedByUsersMap;
};
