import { json, urlencoded } from "body-parser";
import express from "express";
import helmet from "helmet";
import cookieParser from "cookie-parser";
import hpp from "hpp";
import cors from "cors";

// routes
import routes from "./routes";
import { errorHandler } from "@moxfive-llc/common";
import { rateLimiter } from "./utils/rate-limitter";

const app = express();

export function addMiddleWaresToApp() {
// middleware which blocks requests when we're too busy
//     app.use(toobusyChecker);

    app.set("trust proxy", true);
    app.use(rateLimiter());
    app.use(helmet());
    app.use(json({
        limit: "20kb"
    }));
    app.use(urlencoded({
        extended: true,
        limit: "20kb"
    }));

    app.use(
        cors({
            credentials: true,
        }),
    );
    app.use(hpp());
    app.use(cookieParser(process.env.COOKIE_SECRET));

    app.use("/", routes);
    app.use(errorHandler);
}

export default app;
