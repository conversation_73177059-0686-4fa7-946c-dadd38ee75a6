import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class IOC extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }
    // CreateIOCComment	POST - /v1/incidents/:incidentId/ioc/:iocId/comments
    // CreateIOC	POST - /v1/incidents/:incidentId/ioc
    // UpdateIOCAnyComment	PUT - /v1/incidents/:incidentId/ioc/:iocId/allComments/:commentId - need action
    // UpdateIOCComment	PUT - /v1/incidents/:incidentId/ioc/:iocId/comments/:commentId - need action
    // UpdateIOCIndicatorDetail	PUT - /v1/incidents/:incidentId/ioc/:iocId/indicator
    // UpdateIOCRemediationAndContainmentActionsTakenDetail	PUT - /v1/incidents/:incidentId/ioc/:iocId/remediationAndContainment/actionsTaken
    // UpdateIOCRemediationAndContainmentDetail	PUT - /v1/incidents/:incidentId/ioc/:iocId/remediationAndContainment

    async createIOCComment(incidentId: string, iocId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/comments`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateIOCComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateIOCComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async createIOC(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateIOC",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateIOC",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIOCAnyComment(incidentId: string, iocId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/allComments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIOCAnyComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIOCAnyComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIOCComment(incidentId: string, iocId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/comments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIOCComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIOCComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIOCIndicatorDetail(incidentId: string, iocId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/indicator`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIOCIndicatorDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIOCIndicatorDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIOCRemediationAndContainmentActionsTakenDetail(incidentId: string, iocId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/remediationAndContainment/actionsTaken`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIOCRemediationAndContainmentActionsTakenDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIOCRemediationAndContainmentActionsTakenDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIOCRemediationAndContainmentDetail(incidentId: string, iocId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/ioc/${iocId}/remediationAndContainment`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIOCRemediationAndContainmentDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIOCRemediationAndContainmentDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
