import { ExternalServerError, InternalServerError, InvalidTokenError } from "@moxfive-llc/common";
import axios from "axios";
import { Secret, SignOptions, sign, decode } from "jsonwebtoken";
import { promisify } from "util";

import { User, UserDoc } from "../models/user";
import qs from "qs";
import { FeatureVersion } from "../models/feature-versions";

interface GetAccessTokenParams {
    id: string,
    email: string,
    organizationId: string,
    accessToken: string
}

export class Request {

    protected host: string;
    private readonly baseHeaders: { [key: string]: string } = {};
    constructor(token: string) {
        this.host = process.env.DOMAIN_URL || "";
        this.baseHeaders = {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`
        };
    }

    static async authenticate(): Promise<string> {
        try {
            let accessToken: string;
            let user: UserDoc | null = null;
            // If authentication version not found then throw an error
            const authenticationVersion = await FeatureVersion.findOne({ name: "authentication" }).lean().exec();
            if(!authenticationVersion) {
                throw new InternalServerError();
            }

            // If current version is v1 then use Microsoft Login otherwise use Auth0 Login
            if(authenticationVersion.currentVersion === "v1") {
                const authEndpoint = `https://login.microsoftonline.com/${process.env.TENANT_ID}/oauth2/v2.0/token`;
                const params = new URLSearchParams();
                params.append("grant_type", "client_credentials");
                params.append("client_id", String(process.env.SYSTEM_CLIENT_ID));
                params.append("client_secret", String(process.env.SYSTEM_CLIENT_SECRET));
                params.append("scope", "https://graph.microsoft.com/.default");
                const response = await axios.post(authEndpoint, params, { headers: { "Content-Type": "application/x-www-form-urlencoded" } });
                accessToken = response.data.access_token;
                const decodedToken: any = decode(accessToken);
                const { oid } = decodedToken;

                user = await User.findOne({ azureId: oid }).lean().exec();
            }
            else {
                const response = await axios.post(
                    `${process.env.AUTH0_DOMAIN}/oauth/token`,
                    qs.stringify({
                        grant_type: "password",
                        username: process.env.SYSTEM_CLIENT_ID,
                        password: process.env.SYSTEM_CLIENT_SECRET,
                        client_id: process.env.AUTH0_WEB_APP_CLIENT_ID,
                        client_secret: process.env.AUTH0_WEB_APP_CLIENT_SECRET,
                        scope: "openid email"
                    }),
                    {
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                        },
                    }
                );

                accessToken = response.data.id_token;
                const idTokenDetails: any = accessToken ? decode(accessToken) : null;
                if(!idTokenDetails) {
                    throw new InvalidTokenError();
                }

                user = await User.findOne({ email: idTokenDetails.email }).lean().exec();
            }

            if (!user) {
                throw new Error("User not found");
            }

            const { _id, email, organizationId } = user;
            const id = _id as string;
            return String(await promisify<GetAccessTokenParams, Secret, SignOptions>(sign)(
                { id, email, organizationId, accessToken }, `${process.env.JWT_SECRET}`,
                { expiresIn: 3600 - (5 * 60), algorithm: "HS256" }));
        }
        catch {
            throw new InvalidTokenError();
        }
    }

    async post<T>(url: string, data: any, headers?: any): Promise<T> {
        try {
            return (await axios.post<T>(url, data, { headers: { ...this.baseHeaders, ...headers } })).data;
        }
        catch (err: any) {
            if(axios.isAxiosError(err) && err.response && err.response.data) {
                return err.response.data;
            }
            throw new ExternalServerError(err.message);
        }
    }

    async put<T>(url: string, data: any, headers?: any): Promise<T> {
        try {
            return (await axios.put<T>(url, data, { headers: { ...this.baseHeaders, ...headers } })).data;
        }
        catch (err: any) {
            if(axios.isAxiosError(err) && err.response && err.response.data) {
                return err.response.data;
            }
            throw new ExternalServerError(err.message);
        }
    }
}
