import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class Task extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }

    // CreateTask	POST - /v2/incidents/:incidentId/projectStatus/tasks
    // CreateTaskComment	POST - /v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments
    // UpdateTaskAnyComment	PUT - /v2/incidents/:incidentId/projectStatus/tasks/:taskId/allComments/:commentId - need action
    // UpdateTaskComment	PUT - /v2/incidents/:incidentId/projectStatus/tasks/:taskId/comments/:commentId - need action
    // UpdateProjectTaskGeneralDetails	PUT - /v2/incidents/:incidentId/projectStatus/tasks/:taskId/general
    // UpdateProjectTaskStatusDetails	PUT - /v2/incidents/:incidentId/projectStatus/tasks/:taskId/status

    async createTask(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateTask",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateTask",
                status: "Success"
            };
        }
        catch (err: any) {
            console.info(err);
            return {
                name: "CreateTask",
                status: "Failed",
                error: "error from task action"
            };
        }
    }

    async createTaskComment(incidentId: string, taskId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks/${taskId}/comments`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateTaskComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateTaskComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateTaskAnyComment(incidentId: string, taskId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks/${taskId}/allComments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateTaskAnyComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateTaskAnyComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateTaskComment(incidentId: string, taskId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks/${taskId}/comments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateTaskComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateTaskComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateProjectTaskGeneralDetails(incidentId: string, taskId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks/${taskId}/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateProjectTaskGeneralDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateProjectTaskGeneralDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateProjectTaskStatusDetails(incidentId: string, taskId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/tasks/${taskId}/status`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateProjectTaskStatusDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateProjectTaskStatusDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
