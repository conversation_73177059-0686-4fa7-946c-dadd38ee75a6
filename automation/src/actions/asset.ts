import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class Asset extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }
    // AddAssetStatus	POST - /v1/incidents/:incidentId/assets/status
    // CreateAssetComment	POST - /v1/incidents/:incidentId/assets/:assetId/comments
    // CreateAsset	POST - /v1/incidents/:incidentId/assets
    // UpdateAssetDataPreservationDetail	PUT - /v1/incidents/:incidentId/assets/:assetId/dataPreservation
    // UpdateAssetDatesDetail	PUT - /v1/incidents/:incidentId/assets/:assetId/dates
    // UpdateAssetForensicsCompromisedStatus	PUT - /v1/incidents/:incidentId/assets/:assetId/forensics/compromisedStatus
    // UpdateAssetForensicsDetail	PUT - /v1/incidents/:incidentId/assets/:assetId/forensics
    // UpdateAssetHostDetail	PUT - /v1/incidents/:incidentId/assets/:assetId/host
    // UpdateAssetRecoveryDetail	PUT - /v1/incidents/:incidentId/assets/:assetId/recovery
    // UpdateAssetRecoveryStatus	PUT - /v1/incidents/:incidentId/assets/:assetId/recovery/status
    // UpdateAssetStatusOfLibrary	PUT - /v1/incidents/:incidentId/assets/status/:statusId
    // UpdateAssetStatuses	PUT - /v1/incidents/:incidentId/assets/statuses
    // UpdateAssets	PUT - /v1/incidents/:incidentId/assets-attribute

    async addAssetStatus(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/status`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "AddAssetStatus",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "AddAssetStatus",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async createAssetComment(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/comments`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateAssetComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateAssetComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async createAsset(incidentId: string, data: any, headers?: any) {
        const url = `${this.host}/v2/incidents/${incidentId}/assets`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateAsset",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateAsset",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetDataPreservationDetail(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/dataPreservation`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetDataPreservationDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetDataPreservationDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetDatesDetail(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/dates`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetDatesDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetDatesDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetForensicsCompromisedStatus(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/forensics/compromisedStatus`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetForensicsCompromisedStatus",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetForensicsCompromisedStatus",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetForensicsDetail(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/forensics`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetForensicsDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetForensicsDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetHostDetail(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/host`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetHostDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetHostDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetRecoveryDetail(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/recovery`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetRecoveryDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetRecoveryDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetRecoveryStatus(incidentId: string, assetId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/${assetId}/recovery/status`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetRecoveryStatus",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetRecoveryStatus",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetStatusOfLibrary(incidentId: string, statusId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/status/${statusId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetStatusOfLibrary",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetStatusOfLibrary",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssetStatuses(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets/statuses`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssetStatuses",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssetStatuses",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateAssets(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/assets-attribute`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateAssets",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateAssets",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
