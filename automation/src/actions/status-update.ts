import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class StatusUpdate extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }
    // CreateStatusUpdateComment	POST - /v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments
    // CreateStatusUpdate	POST - /v1/incidents/:incidentId/projectStatus/statusUpdates
    // UpdateStatusUpdateAnyComment	PUT - /v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/allComments/:commentId - need action
    // UpdateStatusUpdateComment	PUT - /v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/comments/:commentId - need action
    // UpdateProjectStatusUpdateGeneralDetails	PUT - /v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/general
    // UpdateProjectStatusUpdateStatusDetails	PUT - /v1/incidents/:incidentId/projectStatus/statusUpdates/:statusUpdatesId/status

    async createStatusUpdateComment(incidentId: string, statusUpdatesId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates/${statusUpdatesId}/comments`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateStatusUpdateComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateStatusUpdateComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async createStatusUpdate(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateStatusUpdate",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateStatusUpdate",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateStatusUpdateAnyComment(incidentId: string, statusUpdatesId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates/${statusUpdatesId}/allComments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateStatusUpdateAnyComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateStatusUpdateAnyComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateStatusUpdateComment(incidentId: string, statusUpdatesId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates/${statusUpdatesId}/comments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateStatusUpdateComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateStatusUpdateComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateProjectStatusUpdateGeneralDetails(incidentId: string, statusUpdatesId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates/${statusUpdatesId}/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateProjectStatusUpdateGeneralDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateProjectStatusUpdateGeneralDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateProjectStatusUpdateStatusDetails(incidentId: string, statusUpdatesId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/projectStatus/statusUpdates/${statusUpdatesId}/status`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateProjectStatusUpdateStatusDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateProjectStatusUpdateStatusDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
