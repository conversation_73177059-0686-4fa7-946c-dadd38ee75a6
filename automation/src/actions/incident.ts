import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class Incident extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }

    /*
     * UpdateIncidentProgressPercentage PUT - /v2/incidents/:incidentId/progressPercentage
     * UpdateIncidentStatus PUT - /v2/incidents/:incidentId/status
     *
     * UpdateInsuranceGeneralDetail	PUT - /v2/incidents/:incidentId/insurance/general
     * UpdateInsuranceClaimDetail	PUT - /v2/incidents/:incidentId/insurance/claim
     * UpdateInsurancePolicyDetail	PUT - /v2/incidents/:incidentId/insurance/policy
     *
     * UpdateEnvironmentGeneralDetail	PUT - /v2/incidents/:incidentId/environment/general
     * UpdateEnvironmentActiveDirectoryDetail	PUT - /v2/incidents/:incidentId/environment/activeDirectory
     * UpdateEnvironmentBackupsDetail	PUT - /v2/incidents/:incidentId/environment/backups
     * UpdateEnvironmentEmailDetail	PUT - /v2/incidents/:incidentId/environment/email
     * UpdateEnvironmentSolutionsDetail	PUT - /v2/incidents/:incidentId/environment/solutions
     *
     * UpdateIncidentGeneralDetail  PUT - /v2/incidents/:incidentId/incident/general
     * UpdateIncidentRansomwareDetail  PUT - /v2/incidents/:incidentId/incident/ransomware
     * UpdateIncidentExtortionDetail  PUT - /v2/incidents/:incidentId/incident/extortion
     * UpdateIncidentBusinessEmailCompromiseDetail  PUT - /v2/incidents/:incidentId/incident/businessEmailCompromise
     * UpdateIncidentInitialEntryPointDetails PUT - /v2/incidents/:incidentId/incident/initialEntryPointDetails
     */

    // General section
    async updateIncidentProgressPercentage(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/progressPercentage`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentProgressPercentage",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentProgressPercentage",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentStatus(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/status`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentStatus",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentStatus",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    // Insurance section
    async updateInsuranceGeneralDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/insurance/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateInsuranceGeneralDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateInsuranceGeneralDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateInsuranceClaimDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/insurance/claim`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateInsuranceClaimDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateInsuranceClaimDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateInsurancePolicyDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/insurance/policy`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateInsurancePolicyDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateInsurancePolicyDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    // Environment section
    async updateEnvironmentGeneralDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/environment/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateEnvironmentGeneralDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateEnvironmentGeneralDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateEnvironmentActiveDirectoryDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/environment/activeDirectory`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateEnvironmentActiveDirectoryDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateEnvironmentActiveDirectoryDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateEnvironmentBackupsDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/environment/backups`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateEnvironmentBackupsDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateEnvironmentBackupsDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateEnvironmentEmailDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/environment/email`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateEnvironmentEmailDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateEnvironmentEmailDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateEnvironmentSolutionsDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/environment/solutions`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateEnvironmentSolutionsDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateEnvironmentSolutionsDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    // Incident section
    async updateIncidentGeneralDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/incident/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentGeneralDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentGeneralDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentInitialEntryPointDetails(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/incident/initialEntryPointDetails`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentInitialEntryPointDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentInitialEntryPointDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentRansomwareDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/incident/ransomware`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentRansomwareDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentRansomwareDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentExtortionDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/incident/extortion`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentExtortionDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentExtortionDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentBusinessEmailCompromiseDetail(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/incident/businessEmailCompromise`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentBusinessEmailCompromiseDetail",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentBusinessEmailCompromiseDetail",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
