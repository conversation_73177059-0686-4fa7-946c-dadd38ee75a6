import { Request } from "./request";
import { ExternalServerError } from "@moxfive-llc/common";
import { ExecutedAction } from "../models/automate-logs";

export class IncidentTimeline extends Request {
    constructor(accessToken: string) {
        super(accessToken);
    }
    // CreateIncidentTimelineComment	POST - /v1/incidents/:incidentId/timeline/:timelineId/comments
    // CreateIncidentTimelineEntry	POST - /v1/incidents/:incidentId/timeline
    // UploadTimelineNegotiationAttachmentFiles	POST - /v1/incidents/:incidentId/timeline/:timelineId/upload/files - need action
    // UpdateIncidentTimelineAnyComment	PUT - /v1/incidents/:incidentId/timeline/:timelineId/allComments/:commentId - need action
    // UpdateIncidentTimelineComment	PUT - /v1/incidents/:incidentId/timeline/:timelineId/comments/:commentId - need action
    // UpdateTimelineForensicDetails	PUT - /v1/incidents/:incidentId/timeline/:timelineId/forensic
    // UpdateTimelineGeneralDetails	PUT - /v1/incidents/:incidentId/timeline/:timelineId/general
    // UpdateTimelineNegotiationDetails	PUT - /v1/incidents/:incidentId/timeline/:timelineId/negotiation

    async createIncidentTimelineComment(incidentId: string, timelineId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/comments`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateIncidentTimelineComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateIncidentTimelineComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async createIncidentTimelineEntry(incidentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline`;
        try {
            const response = await this.post<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "CreateIncidentTimelineEntry",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "CreateIncidentTimelineEntry",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentTimelineAnyComment(incidentId: string, timelineId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/allComments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentTimelineAnyComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentTimelineAnyComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateIncidentTimelineComment(incidentId: string, timelineId: string, commentId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/comments/${commentId}`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateIncidentTimelineComment",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateIncidentTimelineComment",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateTimelineForensicDetails(incidentId: string, timelineId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/forensic`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateTimelineForensicDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateTimelineForensicDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateTimelineGeneralDetails(incidentId: string, timelineId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/general`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateTimelineGeneralDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateTimelineGeneralDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

    async updateTimelineNegotiationDetails(incidentId: string, timelineId: string, data: any, headers?: any): Promise<ExecutedAction> {
        const url = `${this.host}/v2/incidents/${incidentId}/timeline/${timelineId}/negotiation`;
        try {
            const response = await this.put<any>(url, data, headers);
            if(response.code && response.title) {
                return {
                    name: "UpdateTimelineNegotiationDetails",
                    status: "Failed",
                    error: JSON.stringify(response),
                };
            }
            return {
                name: "UpdateTimelineNegotiationDetails",
                status: "Success"
            };
        }
        catch (err: any) {
            throw new ExternalServerError(err.message);
        }
    }

}
