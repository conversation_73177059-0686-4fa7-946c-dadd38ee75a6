import { Listener, Subjects, ResourceNotFoundError, NotFoundCode, AssetStatusUpdatedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { AssetStatusesLibrary } from "../../models/asset-statuses-library";

export class AssetStatusUpdatedListener extends Listener<AssetStatusUpdatedEvent> {
    subject: Subjects.AssetStatusUpdated = Subjects.AssetStatusUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: AssetStatusUpdatedEvent["data"], msg: JsMsg) {
        const statusLibrary = await AssetStatusesLibrary.findById(data.id).lean().exec();
        if(!statusLibrary) {
            throw new ResourceNotFoundError(NotFoundCode.ASSET_STATUS_NOT_FOUND, "Asset status not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if(statusLibrary.version >= data.version) {
            msg.ack();
            return;
        }
        await AssetStatusesLibrary.findByIdAndUpdate(data.id, {
            _id: data.id,
            name: data.name,
            version: data.version
        });
        msg.ack();
    }
}
