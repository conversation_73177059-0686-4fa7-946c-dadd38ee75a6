import { Listener, Subjects, IncidentFlexibleFieldUpdatedEvent, ResourceNotFoundError, NotFoundCode } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { IncidentFlexibleField } from "../../models/incident-flexible-fields";

export class IncidentFlexibleFieldUpdatedListener extends Listener<IncidentFlexibleFieldUpdatedEvent> {
    subject: Subjects.IncidentFlexibleFieldUpdated = Subjects.IncidentFlexibleFieldUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: IncidentFlexibleFieldUpdatedEvent["data"], msg: JsMsg) {
        const incidentFlexibleField = await IncidentFlexibleField.findById(data.id);
        if(!incidentFlexibleField) {
            throw new ResourceNotFoundError(NotFoundCode.FLEXIBLE_FIELDS_NOT_FOUND, "Incident flexible field not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if(incidentFlexibleField.version >= data.version) {
            msg.ack();
            return;
        }
        const { values } = data;
        incidentFlexibleField?.set({ values: values.map(item => ({
            _id: item.id,
            value: item.value
        })) });
        await IncidentFlexibleField.findByIdAndUpdate(data.id, {
            _id: data.id,
            values: incidentFlexibleField?.values,
            version: data.version
        });
        msg.ack();
    }
}
