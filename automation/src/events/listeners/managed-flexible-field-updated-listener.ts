import { Listener, Subjects, ResourceNotFoundError, NotFoundC<PERSON>, ManagedFlexibleFieldUpdatedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { ManagedFlexibleFields } from "../../models/managed-flexible-fields";

export class ManagedFlexibleFieldUpdatedListener extends Listener<ManagedFlexibleFieldUpdatedEvent> {
    subject: Subjects.ManagedFlexibleFieldUpdated = Subjects.ManagedFlexibleFieldUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: ManagedFlexibleFieldUpdatedEvent["data"], msg: JsMsg) {
        const managedFlexibleField = await ManagedFlexibleFields.findById(data.id).lean().exec();
        if(!managedFlexibleField) {
            throw new ResourceNotFoundError(NotFoundCode.FLEXIBLE_FIELDS_NOT_FOUND, "Managed flexible field not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if(managedFlexibleField && managedFlexibleField.version >= data.version) {
            msg.ack();
            return;
        }
        await ManagedFlexibleFields.findByIdAndUpdate(data.id, {
            _id: data.id,
            values: data.values,
            version: data.version
        }, { upsert: true });
        msg.ack();
    }
}
