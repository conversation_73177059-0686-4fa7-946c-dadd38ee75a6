import { Listener, Subjects, AssetStatusDeletedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { AssetStatusesLibrary } from "../../models/asset-statuses-library";

export class AssetStatusDeletedListener extends Listener<AssetStatusDeletedEvent> {
    subject: Subjects.AssetStatusDeleted = Subjects.AssetStatusDeleted;
    queueGroupName = queueGroupName;

    async onMessage(data: AssetStatusDeletedEvent["data"], msg: JsMsg) {
        const { id } = data;

        await AssetStatusesLibrary.findByIdAndDelete(id).lean().exec();
        msg.ack();
    }
}
