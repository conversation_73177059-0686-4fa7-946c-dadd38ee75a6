import { Listener, Subjects, AssetStatusAddedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { AssetStatusesLibrary } from "../../models/asset-statuses-library";

export class AssetStatusAddedListener extends Listener<AssetStatusAddedEvent> {
    subject: Subjects.AssetStatusAdded = Subjects.AssetStatusAdded;
    queueGroupName = queueGroupName;

    async onMessage(data: AssetStatusAddedEvent["data"], msg: JsMsg) {
        console.info("Asset status added data: " + JSON.stringify(data));
        await AssetStatusesLibrary.build(data).save();
        msg.ack();
    }
}
