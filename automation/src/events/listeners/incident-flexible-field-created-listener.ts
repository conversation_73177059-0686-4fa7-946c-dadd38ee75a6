import { Listener, Subjects, IncidentFlexibleFieldCreatedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { IncidentFlexibleField } from "../../models/incident-flexible-fields";

export class IncidentFlexibleFieldCreatedListener extends Listener<IncidentFlexibleFieldCreatedEvent> {
    subject: Subjects.IncidentFlexibleFieldCreated = Subjects.IncidentFlexibleFieldCreated;
    queueGroupName = queueGroupName;

    async onMessage(data: IncidentFlexibleFieldCreatedEvent["data"], msg: JsMsg) {
        await IncidentFlexibleField.build(data).save();
        msg.ack();
    }
}
