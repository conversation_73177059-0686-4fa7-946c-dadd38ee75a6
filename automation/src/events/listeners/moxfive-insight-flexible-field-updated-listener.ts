import { Listener, Subjects, MoxfiveInsightFlexibleFieldUpdatedEvent, ResourceNotFoundError, NotFoundCode } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { MoxfiveInsightFlexibleFields } from "../../enums/moxfive-insight-flexible-field.enum";
import { IncidentFlexibleField } from "../../models/incident-flexible-fields";

export class MoxfiveInsightFlexibleFieldUpdatedListener extends Listener<MoxfiveInsightFlexibleFieldUpdatedEvent> {
    subject: Subjects.MoxfiveInsightFlexibleFieldUpdated = Subjects.MoxfiveInsightFlexibleFieldUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: MoxfiveInsightFlexibleFieldUpdatedEvent["data"], msg: JsMsg) {
        // If flexible field event was received for field other than incident management environment then ignore it.
        const key = data.key;
        if (!key || !key.includes(MoxfiveInsightFlexibleFields.MoxfiveInsightOverviewName)) {
            msg.ack();
            return;
        }

        const flexibleField = await IncidentFlexibleField.findById(data.id).lean().exec();
        if(!flexibleField) {
            throw new ResourceNotFoundError(NotFoundCode.FLEXIBLE_FIELDS_NOT_FOUND, "Flexible field not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if(flexibleField.version >= data.version) {
            msg.ack();
            return;
        }

        await IncidentFlexibleField.findByIdAndUpdate(data.id, {
            _id: data.id,
            values: data.values.map(item => ({
                _id: item.id,
                value: item.value
            })),
            version: data.version
        });
        msg.ack();
    }
}
