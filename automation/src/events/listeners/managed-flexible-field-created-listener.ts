import { Listener, Subjects, ManagedFlexibleFieldCreatedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { ManagedFlexibleFields } from "../../models/managed-flexible-fields";

export class ManagedFlexibleFieldCreatedListener extends Listener<ManagedFlexibleFieldCreatedEvent> {
    subject: Subjects.ManagedFlexibleFieldCreated = Subjects.ManagedFlexibleFieldCreated;
    queueGroupName = queueGroupName;

    async onMessage(data: ManagedFlexibleFieldCreatedEvent["data"], msg: JsMsg) {
        await ManagedFlexibleFields.build(data).save();
        msg.ack();
    }
}
