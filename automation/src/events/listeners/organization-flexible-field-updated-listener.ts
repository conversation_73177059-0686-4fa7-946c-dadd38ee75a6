import { Listener, OrganizationFlexibleFieldUpdatedEvent, Subjects, ResourceNotFoundError, NotFoundCode } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { IncidentFlexibleField } from "../../models/incident-flexible-fields";

export class OrganizationFlexibleFieldUpdatedListener extends Listener<OrganizationFlexibleFieldUpdatedEvent> {
    subject: Subjects.OrganizationFlexibleFieldUpdated = Subjects.OrganizationFlexibleFieldUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: OrganizationFlexibleFieldUpdatedEvent["data"], msg: JsMsg) {
        const key = data.key;
        if (key?.includes("incidentManagement-environment")) {
            const incidentFlexibleField = await IncidentFlexibleField.findById(data.id).lean().exec();
            if (!incidentFlexibleField) {
                throw new ResourceNotFoundError(NotFoundCode.FLEXIBLE_FIELDS_NOT_FOUND, "Incident flexible field not found.");
            }
            incidentFlexibleField.values.push(
                {
                    _id: data.newValue.id,
                    value: data.newValue.value
                }
            );
            // If current version is greater than provided version then acknowledge the event directly
            if (incidentFlexibleField.version >= data.version) {
                msg.ack();
                return;
            }
            await IncidentFlexibleField.findByIdAndUpdate(data.id, {
                _id: data.id,
                values: incidentFlexibleField.values,
                version: data.version
            });
            msg.ack();
            return;
        }

        const organizationFlexibleField = await OrganizationFlexibleField.findById(data.id).lean().exec();
        if(!organizationFlexibleField) {
            throw new ResourceNotFoundError(NotFoundCode.FLEXIBLE_FIELDS_NOT_FOUND, "Organization flexible field not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if (organizationFlexibleField.version >= data.version) {
            msg.ack();
            return;
        }
        organizationFlexibleField.values.push(
            {
                _id: data.newValue.id,
                value: data.newValue.value
            }
        );
        await OrganizationFlexibleField.findByIdAndUpdate(data.id, {
            _id: data.id,
            values: organizationFlexibleField.values,
            version: data.version
        }, { upsert: true });
        msg.ack();
    }
}
