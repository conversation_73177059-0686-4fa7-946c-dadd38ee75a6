import { ModuleSectionFieldValueReferencesEnum } from "../enums/module-section-field-value-references.enum";
import { RuleTypesEnum } from "../enums/rule-types.enum";

export interface ActionFieldValueResp {
    _id?: string,
    id: string,
    name: string,
    key: string,
    moduleSectionId: string,
    // type: string | { name: string, constraints: Record<string, any> | null },
    type: string,
    flexibleFieldKey: string | null,
    valueReference: ModuleSectionFieldValueReferencesEnum | null,
    statusField: boolean,
    constraints: Record<string, any> | null,
    allowedRuleTypes: RuleTypesEnum[],
    values?: any,
    isPlaceholderField: boolean,
    sequence: number,
    supportPlaceholders: boolean,
    inheritPlaceholder: boolean
}
