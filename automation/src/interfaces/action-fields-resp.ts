import { FieldValueReferencesEnum } from "../enums/field-value-references.enum";
import { ValuesLibraryArr } from "../models/metadata-fields";

export interface ActionFieldsResp {
    id: string,
    _id?: string,
    name: string,
    key: string,
    description: string | null,
    placeholder: string | null,
    type: string,
    required: boolean,
    valueReference: FieldValueReferencesEnum,
    dynamicField: boolean,
    constraints: Record<string, any>,
    values: ValuesLibraryArr[] | null,
    sequence: number,
    dependencies: Record<string, string>,
    valuesLibrary?: ValuesLibraryArr[] | null,
    selectedValue?: any,
    valueType?: string | null,
    hideField?: boolean | null,
    supportPlaceholders?: boolean,
    inheritPlaceholder?: boolean
}
