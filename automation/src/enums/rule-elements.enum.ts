export enum RuleElementsEnum {
    triggerCommentAdded = "triggerCommentAdded",
    triggerCommentUpdated = "triggerCommentUpdated",
    triggerFieldValueChanged = "triggerFieldValueChanged",
    triggerNewRecordCreated = "triggerNewRecordCreated",
    triggerRecordAssigned = "triggerRecordAssigned",
    triggerRecordTransitioned = "triggerRecordTransitioned",
    triggerRecordUpdated = "triggerRecordUpdated",
    criteriaRecordField = "criteriaRecordField",
    criteriaUserCondition = "criteriaUserCondition",
    criteriaIfElseBlock = "criteriaIfElseBlock",
    actionCreateRecord = "actionCreateRecord",
    actionEditRecord = "actionEditRecord",
    actionSendEmail = "actionSendEmail",
}
