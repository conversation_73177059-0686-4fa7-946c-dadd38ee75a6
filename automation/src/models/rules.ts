import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { ProjectTypesEnum } from "../enums/project-types.enum";
import { RuleTypesEnum } from "../enums/rule-types.enum";
import { RuleElementTypesEnum } from "../enums/rule-element-types.enum";

interface IRuleWorkflowElement {
    id: string,
    type: RuleElementTypesEnum
}

interface IRuleTriggerValues {
    key: string,
    values: string[],
    valueIds?: string[]
}
interface IRuleTrigger {
    triggerType: string,
    selectedModule: string,
    triggerValues: IRuleTriggerValues[]
}

interface PublishedRule {
    versionId: string,
    sequence: number,
    workflow: IRuleWorkflowElement[],
    trigger: IRuleTrigger;
}

// An interface that describes the properties to define a new Rule
interface RulesAttrs {
    name: string,
    description?: string | null,
    type: string,
    enabled?: boolean,
    published?: boolean,
    projects: string[] | null,
    projectType: ProjectTypesEnum.INCIDENT | null,
    publishedVersion?: PublishedRule,
    createdBy: string | null,
    updatedBy: string | null,
    publishedOn: string | null,
    lastPublishedOn: string | null,
}

// An interface that describes the properties that  Rule Document has
interface RulesDoc extends mongoose.Document {
    id: string,
    name: string,
    description: string | null,
    type: RuleTypesEnum,
    enabled: boolean,
    published: boolean,
    publishedOn: string | null,
    lastPublishedOn: string | null,
    projects: string[] | null,
    projectType: ProjectTypesEnum.INCIDENT | null,
    publishedVersion: PublishedRule,
    createdBy: string | null,
    updatedBy: string | null,
    createdAt: string,
    updatedAt: string,
    version: number
}

// An interface that describes the properties that a Rule model has
interface RulesModel extends mongoose.Model<RulesDoc> {
    build(attrs: RulesAttrs): RulesDoc,
}

const ruleTriggerValuesSchema = new mongoose.Schema({
    key: {
        type: String,
        required: true
    },
    values: {
        type: [String],
        required: true
    },
    valueIds: {
        type: [String],
        default: []
    }
}, {
    _id: false,
    versionKey: false
});

const ruleTriggerSchema = new mongoose.Schema({
    triggerType: {
        type: String,
        required: true
    },
    selectedModule: {
        type: String,
        required: true
    },
    triggerValues: {
        type: [ruleTriggerValuesSchema],
        required: true
    }
}, {
    _id: false,
    versionKey: false
});

const ruleWorkflowElementSchema = new mongoose.Schema({
    id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
    },
    type: {
        type: String,
        required: true
    },
}, {
    _id: false,
    versionKey: false
});

const publishedVersionSchema = new mongoose.Schema({
    versionId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
    },
    sequence: {
        type: Number,
        required: true
    },
    workflow: {
        type: [ruleWorkflowElementSchema],
        required: true
    },
    trigger: {
        type: ruleTriggerSchema,
        required: true
    }
}, {
    _id: false,
    versionKey: false
});

const rulesSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true
        },
        description: {
            type: String,
            default: null
        },
        type: {
            type: String,
            required: true
        },
        enabled: {
            type: Boolean,
            required: true,
            default: true
        },
        published: {
            type: Boolean,
            default: false
        },
        publishedOn: {
            type: Date,
            default: null
        },
        lastPublishedOn: {
            type: Date,
            default: null
        },
        projects: {
            type: [mongoose.Schema.Types.ObjectId],
            default: []
        },
        projectType: {
            type: String,
            default: null
        },
        publishedVersion: {
            type: publishedVersionSchema,
            default: null
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        updatedAt: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

rulesSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

rulesSchema.set("versionKey", "version");
rulesSchema.plugin(updateIfCurrentPlugin);

rulesSchema.statics.build = (attrs: RulesAttrs) => {
    return new Rules(attrs);
};

const Rules = mongoose.model<RulesDoc, RulesModel>("Rule", rulesSchema);

export { Rules, RulesDoc, PublishedRule, IRuleTrigger, IRuleWorkflowElement };
