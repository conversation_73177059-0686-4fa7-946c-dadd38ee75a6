import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

interface TriggerValues {
    fieldId: string,
    key: string,
    value: any
}

// An interface that describes the properties to define a new Trigger
interface TriggersAttrs {
    metadataTriggerId: string,
    values: TriggerValues[],
    createdBy: string | null,
    updatedBy: string | null,
}

// An interface that describes the properties that  Trigger Document has
interface TriggersDoc extends mongoose.Document {
    id: string,
    metadataTriggerId: string,
    values: TriggerValues[],
    createdBy: string | null,
    updatedBy: string | null,
    createdAt: string,
    updatedAt: string,
    version: number
}

// An interface that describes the properties that a Trigger model has
interface TriggersModel extends mongoose.Model<TriggersDoc> {
    build(attrs: TriggersAttrs): TriggersDoc,
}

const triggerValuesSchema = new mongoose.Schema({
    fieldId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "MetadataFields",
        required: true
    },
    key: {
        type: String,
        required: true
    },
    value: {
        type: mongoose.Schema.Types.Mixed,
    },
}, {
    _id: false,
    versionKey: false
});

const triggersSchema = new mongoose.Schema(
    {
        metadataTriggerId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "MetadataTriggers",
            required: true
        },
        values: {
            type: [triggerValuesSchema],
            required: true
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        updatedAt: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

triggersSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

triggersSchema.set("versionKey", "version");
triggersSchema.plugin(updateIfCurrentPlugin);

triggersSchema.statics.build = (attrs: TriggersAttrs) => {
    return new Triggers(attrs);
};

const Triggers = mongoose.model<TriggersDoc, TriggersModel>("Triggers", triggersSchema);

export { Triggers, TriggersDoc, TriggerValues };
