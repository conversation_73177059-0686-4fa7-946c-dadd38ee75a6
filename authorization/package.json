{"name": "authorization", "version": "1.0.0", "description": "Microservice for handling user authorization, roles/rights and permissions", "main": "index.ts", "scripts": {"start:windows": "set NODE_ENV=development && ts-node-dev --poll src/index.ts", "start:ubuntu": "NODE_ENV=development ts-node-dev --poll src/index.ts", "start": "node build/index.js", "build": "tsc", "dev": "ts-node-dev --poll src/index.ts", "dev:local": "ts-node-dev --no-notify --transpile-only --poll src/index.ts", "test": "jest --coverage --watchAll --no-cache --testTimeout=30000", "test:ci": "jest", "lint": "eslint './src/**/*.ts'"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "setupFilesAfterEnv": ["./src/test/setup.ts"]}, "keywords": [], "author": "MOXFIVE", "license": "ISC", "dependencies": {"@moxfive-llc/common": "2.0.25", "@types/cookie-parser": "1.4.8", "@types/cors": "2.8.17", "@types/express": "5.0.0", "@types/hpp": "0.2.6", "@types/swagger-jsdoc": "6.0.4", "@types/swagger-ui-express": "4.1.7", "cookie-parser": "1.4.7", "cors": "2.8.5", "express": "4.21.2", "express-async-errors": "3.1.1", "express-validator": "7.2.1", "helmet": "8.0.0", "hpp": "0.2.3", "mongoose": "8.9.6", "mongoose-update-if-current": "1.4.0", "nats": "2.28.2", "swagger-jsdoc": "6.2.8", "swagger-ui-express": "5.0.1", "ts-node-dev": "2.0.0", "typescript": "5.7.3"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.20.0", "@types/jest": "29.5.14", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "8.24.1", "@typescript-eslint/parser": "8.24.1", "eslint": "9.20.1", "eslint-plugin-security": "3.0.1", "globals": "16.0.0", "jest": "29.7.0", "mongodb-memory-server": "10.1.3", "supertest": "7.0.0", "ts-jest": "29.2.5"}}