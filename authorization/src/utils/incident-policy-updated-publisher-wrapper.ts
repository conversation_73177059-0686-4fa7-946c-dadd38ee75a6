import { IncidentPolicyUpdatedPublisher } from "../events/publishers/incident-policy-updated";
import { IncidentDoc } from "../models/incident";
import { natsWrapper } from "../nats-wrapper";

export const incidentPolicyUpdatedPublisherWrapper = async (incident: IncidentDoc) => {
    const data = {
        id: String(incident.id),
        version: incident.version,
        policyIds: incident.policyIds
    };

    await new IncidentPolicyUpdatedPublisher(natsWrapper.client).publish(data);
};
