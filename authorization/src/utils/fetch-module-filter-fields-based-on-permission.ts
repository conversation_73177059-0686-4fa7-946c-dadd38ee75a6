/* eslint-disable no-await-in-loop */
import { fetchSectionFilterFields } from "./fetch-single-section-filter-fields";
import { FilterFields } from "../interfaces";
import { Organization } from "../models/organizations";
import { PolicyTypesEnum } from "../enums/policy-types.enum";
import { ApplicationPolicyModulesEnum } from "../enums/application-policy-modules.enum";

export const fetchModuleFilterFieldsBasedOnPermission = async ({ sections, assignedActions }:
  {sections: {path: any, action?: string}[], assignedActions: Set<string>}) => {
    let fields: FilterFields[] = [];
    sections.forEach(section => {
        if(!section.action || assignedActions.has(section.action)) {
            const sectionFields = fetchSectionFilterFields(section.path);
            fields = fields.concat(sectionFields);
        }
    });

    const formattedFields: FilterFields[] = [];

    for(const field of fields) {
        let values: {id: string, value: string}[] | null = null;
        // Fetch organizations
        if(field.organizations) {
            const organizations = await Organization.find().select("name").lean().exec();
            values = organizations.map(org => ({
                id: String(org._id),
                value: org.name
            }));
        }
        else if(field.policyTypes) {
            values = [
                {
                    id: PolicyTypesEnum.GLOBAL,
                    value: PolicyTypesEnum.GLOBAL,
                }, {
                    id: PolicyTypesEnum.APPLICATION,
                    value: PolicyTypesEnum.APPLICATION,
                }
            ];
        }
        else if(field.applicationPolicyModules) {
            values = [
                {
                    id: ApplicationPolicyModulesEnum.INCIDENT,
                    value: ApplicationPolicyModulesEnum.INCIDENT,
                }, {
                    id: ApplicationPolicyModulesEnum.RESILIENCE,
                    value: ApplicationPolicyModulesEnum.RESILIENCE,
                }
            ];
        }

        formattedFields.push({
            displayName: field.displayName || field.name,
            name: field.name,
            isFlexibleField: field.isFlexibleField,
            values,
            allowedFilters: field.allowedFilters,
            type: field.type,
            isBooleanField: field.isBooleanField,
            isQuickFilter: field.isQuickFilter
        });
    }

    return formattedFields;
};
