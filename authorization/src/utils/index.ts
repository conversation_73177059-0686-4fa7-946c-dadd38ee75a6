import mongoose from "mongoose";
import { SortQuery } from "../interfaces";
import { InsufficientPrivilagesError, InternalServerError } from "@moxfive-llc/common";
import { isMOXFIVEUser } from "./is-moxfive-user";
import { User } from "../models/users";

const hasProtocol = new RegExp("^([a-z]+://|//)", "i");
export const ACTIVE_FOCUS_VIEW_ACTION = "GetActiveFocusView";

export const fetchDefaultSortObject = (sort = ""): SortQuery => {
    if(sort) {
        return {};
    }
    return  { _id: 1 };
};

export function convertToObjectId(id: string) {
    return new mongoose.Types.ObjectId(id);
}

export const isValidMongoObjectId = (id: string) => {
    return mongoose.isObjectIdOrHexString(id);
};

export const intersectTwoObjects = (from: any, to: any) => {
    const result: any = {};
    for (const param in from) {
        if (to.hasOwnProperty(String(param)) && JSON.stringify(to[String(param)]) !== JSON.stringify(from[String(param)])) {
            result[String(param)] = to[String(param)];
        }
    }
    return result;
};

export const pickFromObject = (obj: any, fields: string[]) => {
    const result: any = {};
    for (const field of fields) {
        result[String(field)] = obj[String(field)] ?? null;
    }
    return result;
};

export const getUserName = ({ firstName, lastName, displayName } : {firstName: string | null, lastName: string | null, displayName: string}) => {
    if(!firstName && !lastName) {
        return displayName;
    }
    let name = "";
    if(firstName) {
        name += firstName;
        if(lastName) {
            name += " ";
        }
    }
    if(lastName) {
        name += lastName;
    }
    return name;
};

export const escapeRegExp = (s: string) => {
    return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

export const hasWhiteSpace = (str: string) => {
    return (/\s/).test(str);
};

export const insertWhiteSpaces = (str: string) => {
    let strWithSpaces = str;
    strWithSpaces = strWithSpaces.replace(/([a-z])([A-Z])/g, "$1 $2");
    strWithSpaces = strWithSpaces.replace(/([A-Z])([A-Z][a-z])/g, "$1 $2");
    return strWithSpaces;
};

const doesURIContainsProtocol = (str: string) => {
    return hasProtocol.test(str);
};

export const sanitizeURI = (URIString: string) => {
    let sanitizedString = URIString;
    if (doesURIContainsProtocol(sanitizedString)) {
        sanitizedString = sanitizedString.replace(/tt/, "xx");
        sanitizedString = sanitizedString.replace(/:/, "[:]");
    }

    sanitizedString = sanitizedString.replace(/\./g, "[.]");
    return sanitizedString;
};

export const sanitizeIP = (ipAddress: string) => {
    return ipAddress.replace(/\.(?=[^.]+$)/, "[.]");
};

export const sanitizeEmail = (email: string) => {
    return email.replace(/\./g, "[.]");
};

export const getUserInitials = ({
    firstName,
    lastName,
    displayName,
    email
}: {
    firstName: string | null,
    lastName: string | null,
    displayName: string,
    email: string,
}) => {
    const userInitials: string[] = [];

    // If firstname or last name is present then add first character of it in userInitials array
    if (firstName || lastName) {
        if (firstName) userInitials.push(firstName.charAt(0));
        if (lastName) userInitials.push(lastName.charAt(0));
    }

    // If displayName is present then add first character of it in userInitials
    else if (displayName) {
        userInitials.push(displayName.charAt(0));
    }
    // Otherwise add email first character in userInitials
    else {
        userInitials.push(email.charAt(0));
    }

    return userInitials.join("");
};

export const isSuperAdminAssigned = async (userId: string) => {
    const userDetails = await User.findById(userId, { policyIds: 1 }).lean().exec();
    return !!userDetails?.policyIds?.map(String).includes(process.env.SUPER_ADMIN_POLICY_ID || "");
};

export const checkMoxfiveUserOrSuperAdminPermissions = async (
    req: any,
    throwError = true
): Promise<boolean> => {
    if (!req.currentUser) {
        throw new InternalServerError();
    }

    // Check if user is member if not then Check user is Moxfive user or super admin policy assigned
    const [isMoxfiveUser, isSuperAdminUser] = await Promise.all([
        isMOXFIVEUser({ req }),
        isSuperAdminAssigned(req.currentUser.id)
    ]);

    // Check if the user has either of the required permissions
    if (!isSuperAdminUser && !isMoxfiveUser) {
        if (throwError) {
            throw new InsufficientPrivilagesError(); // ToDo: Throw error if throwError is true
        }
        else {
            return false; // Return false if the user lacks permissions and throwError is false
        }
    }
    return true; // Return true if the user has any of the required permissions
};

export const fieldRegex = /^[a-zA-Z0-9_]+$/;
