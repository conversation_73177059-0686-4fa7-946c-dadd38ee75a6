import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { Incident } from "../models/incident";
import { Policy } from "../models/policy";
import { Tracker } from "../models/tracker";

export const processApplicationPolicyIds = async (applicationPolicyIds: ApplicationPolicyIdObj[]) => {
    const incidentIds: string[] = [];
    const trackerIds: string[] = [];
    const policyIds: string[] = [];
    const idToNameMap = new Map();
    // const appPoliciesMap = new Map();

    applicationPolicyIds.forEach(appPolicy => {
        if (appPolicy.applicationType === "Incident") {
            incidentIds.push(appPolicy.applicationId);
        }
        else {
            trackerIds.push(appPolicy.applicationId);
        }
        policyIds.push(appPolicy.policyId);
    });

    if (incidentIds) {
        const incidentDetails = await Incident.find({ _id: { $in: incidentIds } }, { name: 1 }).lean().exec();
        incidentDetails.forEach(incident => idToNameMap.set(String(incident._id), incident.name));
    }

    if (trackerIds) {
        const trackerDetails = await Tracker.find({ _id: { $in: trackerIds } }, { name: 1 }).lean().exec();
        trackerDetails.forEach(tracker => idToNameMap.set(String(tracker._id), tracker.name));
    }

    const policyDetails = await Policy.find({ _id: { $in: policyIds } }, { name: 1 }).lean().exec();
    policyDetails.forEach(policy => idToNameMap.set(String(policy._id), policy.name));

    // const proccessedResp = applicationPolicyIds.map(appPolicy => ({
    //     applicationType: appPolicy.applicationType,
    //     applicationId: {
    //         id: String(appPolicy.applicationId),
    //         value: idToNameMap.get(String(appPolicy.applicationId))
    //     },
    //     policyId: {
    //         id: String(appPolicy.policyId),
    //         value: idToNameMap.get(String(appPolicy.policyId))
    //     }
    // }));

    return idToNameMap;

    // return proccessedResp;
};
