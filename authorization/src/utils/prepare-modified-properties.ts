import { TargetType } from "@moxfive-llc/common";
import { modifiedProperties } from "@moxfive-llc/common/build/interfaces/modified-properties";

export const prepareModifiedProperties = ({
    data,
    oldData,
    target
}: {
    data: any,
    oldData?: any,
    target: TargetType
}) => {
    const modifiedProperties: modifiedProperties[] = [];
    Object.keys(data).forEach(key => {
        const oldValue = oldData[String(key)] ?? "";
        const newValue = data[String(key)] ?? "";
        modifiedProperties.push({
            target,
            propertyName: key,
            oldValue: typeof oldValue === "string" ? oldValue : JSON.stringify(oldValue),
            newValue: typeof newValue === "string" ? newValue : JSON.stringify(newValue)
        });
    });

    return modifiedProperties;
};
