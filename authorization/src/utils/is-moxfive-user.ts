import { InsufficientPrivilagesError } from "@moxfive-llc/common";
import { Request } from "express";

export const isMOXFIVEUser = ({ req, throwError = false, organizationId = null }: { req: Request, throwError?: boolean, organizationId?: string | null }) => {
    if ((organizationId || req.currentUser?.organizationId) !== process.env.MOXFIVE_ID) {
        if(throwError) {
            throw new InsufficientPrivilagesError();
        }
        return false;
    }
    return true;
};
