import { typeAllowedFiltersMapping } from "./type-allowed-filters-mapping";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { FilterFields } from "../interfaces";

export const fetchSectionFilterFields = (path: any) => {
    const fields :FilterFields[] = [];

    if(Array.isArray(path)) {
        // Loop through all fields of that section
        path.forEach((field: any) => {
            if(field.filterable && field.type) {
                const isFlexibleField = Boolean(field.flexibleField);
                fields.push({
                    name: field.name,
                    displayName: field.displayName,
                    isFlexibleField: isFlexibleField,
                    key: isFlexibleField ? field.key : null,
                    allowedFilters: typeAllowedFiltersMapping[field.type as ValueTypesEnum] || [],
                    type: field.type,
                    organizations: field.organizations,
                    ignoreSanitize: field.ignoreSanitize,
                    policyTypes: field.policyTypes,
                    applicationPolicyModules: field.applicationPolicyModules,
                    isQuickFilter: !!field.isQuickFilter,
                    isBooleanField: !!field.isBooleanField,
                });
            }
        });
    }
    return fields;
};
