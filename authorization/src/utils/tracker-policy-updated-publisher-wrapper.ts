import { TrackerPolicyUpdatedPublisher } from "../events/publishers/tracker-policy-updated";
import { TrackerDoc } from "../models/tracker";
import { natsWrapper } from "../nats-wrapper";

export const trackerPolicyUpdatedPublisherWrapper = async (tracker: TrackerDoc) => {
    const data = {
        id: String(tracker.id),
        version: tracker.version,
        policyIds: tracker.policyIds
    };

    await new TrackerPolicyUpdatedPublisher(natsWrapper.client).publish(data);

};
