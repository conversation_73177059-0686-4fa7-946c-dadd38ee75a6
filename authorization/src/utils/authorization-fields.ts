import { ValueTypesEnum } from "../enums/value-types.enum";

export const authorizationFields = {
    policies: [
        { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
        {
            name: "type", displayName: "Type", filterable: true, type: ValueTypesEnum.DROPDOWN, ignoreSanitize: true, policyTypes: true, flexibleField: false, required: false,
            key: null, sortable: true
        },
        { name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.BOOLEAN, flexibleField: false, required: false, key: null, sortable: true },
    ],
    entitiesByPolicy: {
        global: [
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            {
                name: "email", displayName: "Email Address", filterable: true, type: ValueTypesEnum.EMAIL, ignoreSanitize: true, flexibleField: false, required: false, key: null,
                sortable: true
            },
            {
                name: "organizationId", displayName: "Organization", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, organizations: true,
                required: false, key: null
            },
        ],
        application: [
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: false, key: null, sortable: true },
            { name: "users", displayName: "Users", flexibleField: false, required: false, key: null },
            {
                name: "type", displayName: "Type", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, applicationPolicyModules: true, ignoreSanitize: true,
                required: false, key: null
            }
        ],
    },
    modifiedAt: [
        { name: "createdAt", displayName: "Created At", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        { name: "updatedAt", displayName: "Updated At", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
    ],
    modifiedBy: [
        { name: "createdBy", displayName: "Created By" },
        { name: "updatedBy", displayName: "Updated By" },
    ],
    listPolicies: {
        commonBase: [
            { name: "name", displayName: "Name", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: true, key: null, sortable: true },
            { name: "description", displayName: "Description", filterable: true, type: ValueTypesEnum.STRING, flexibleField: false, required: true, key: null, sortable: true },
            {
                name: "type", displayName: "Type", filterable: true, type: ValueTypesEnum.DROPDOWN, ignoreSanitize: true, policyTypes: true, flexibleField: false, required: false,
                key: null, sortable: true, isSpecialFiled: true, isQuickFilter: true
            },
            {
                name: "isEnabled", displayName: "Status (Active)", filterable: true, type: ValueTypesEnum.DROPDOWN, flexibleField: false, required: false,
                key: null, sortable: true, isBooleanField: true
            },
        ],
        modifiedAt: [
            { name: "createdAt", displayName: "Date Added", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
            { name: "updatedAt", displayName: "Date Updated", filterable: true, type: ValueTypesEnum.DATE, flexibleField: false, sortable: true },
        ],
        modifiedBy: [
            { name: "createdBy", displayName: "Created By", filterable: false, flexibleField: false, sortable: true, type: ValueTypesEnum.DROPDOWN, multiple: false },
            { name: "updatedBy", displayName: "Updated By", filterable: false, flexibleField: false, sortable: true, type: ValueTypesEnum.DROPDOWN, multiple: false },
        ],
    }
};
