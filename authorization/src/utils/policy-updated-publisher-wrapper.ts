import { PolicyUpdatedPublisher } from "../events/publishers/policy-updated-publisher";
import { PolicyDoc } from "../models/policy";
import { natsWrapper } from "../nats-wrapper";

export const policyUpdatedPublisherWrapper = async (policy: PolicyDoc) => {
    const data = {
        id: String(policy.id),
        name: policy.name,
        version: policy.version,
        isEnabled: policy.isEnabled,
        actionIds: (policy.actionIds.length && policy.actionIds.map(String) as string[]) || [],
        type: policy.type,
        createdAt: policy.createdAt
    };

    await new PolicyUpdatedPublisher(natsWrapper.client).publish(data);
};
