import { NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationEvent } from "@moxfive-llc/common";
import { natsWrapper } from "../nats-wrapper";
import {
    NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationPublisher
} from "../events/publishers/non-moxfive-user-with-moxfive-exclusive-permission-notification-publisher";

export const NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper = async (data: NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationEvent["data"]) => {
    await new NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationPublisher(natsWrapper.client).publish(data);
};
