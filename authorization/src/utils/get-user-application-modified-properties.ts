import { TargetType } from "@moxfive-llc/common";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { processApplicationPolicyIds } from "./process-application-policyIds-for-logs";

export const getUserApplicationModifiedProperties = async (modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[]) => {
    const modifiedProperties: any = [];
    let userApplicationIds: ApplicationPolicyIdObj[] = [];
    modifiedApplicationIds.forEach(item => {
        userApplicationIds = [...userApplicationIds, ...item.oldValue];
    });

    const idToNameMap = await processApplicationPolicyIds(userApplicationIds);
    modifiedApplicationIds.forEach(item => {
        modifiedProperties.push({
            target: TargetType.USER,
            propertyName: "applicationPolicyIds",
            oldValue: JSON.stringify(item.oldValue.map((appPolicy: ApplicationPolicyIdObj) => ({
                applicationType: appPolicy.applicationType,
                applicationId: {
                    id: String(appPolicy.applicationId),
                    value: idToNameMap.get(String(appPolicy.applicationId))
                },
                policyId: {
                    id: String(appPolicy.policyId),
                    value: idToNameMap.get(String(appPolicy.policyId))
                }
            }))),
            newValue: JSON.stringify(item.newValue.map((appPolicy: ApplicationPolicyIdObj) => ({
                applicationType: appPolicy.applicationType,
                applicationId: {
                    id: String(appPolicy.applicationId),
                    value: idToNameMap.get(String(appPolicy.applicationId))
                },
                policyId: {
                    id: String(appPolicy.policyId),
                    value: idToNameMap.get(String(appPolicy.policyId))
                }
            })))
        });
    });

    return modifiedProperties;
};
