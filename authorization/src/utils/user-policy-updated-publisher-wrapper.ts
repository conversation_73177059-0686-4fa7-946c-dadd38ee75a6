import { UserPolicyUpdatedPublisher } from "../events/publishers/user-policy-updated-publisher";
import { UserDoc } from "../models/users";
import { natsWrapper } from "../nats-wrapper";

export const userPolicyUpdatedPublisherWrapper = async (user: UserDoc) => {
    const data = {
        id: String(user.id),
        version: user.version,
        policyIds: user.policyIds,
        applicationPolicyIds: user.applicationPolicyIds
    };

    await new UserPolicyUpdatedPublisher(natsWrapper.client).publish(data);

};
