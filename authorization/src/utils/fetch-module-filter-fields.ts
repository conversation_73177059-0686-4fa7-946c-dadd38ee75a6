import { typeAllowedFiltersMapping } from "./type-allowed-filters-mapping";
import { ValueTypesEnum } from "../enums/value-types.enum";
import { FilterFields } from "../interfaces";

export const fetchModuleFilterFields = (path: any) => {
    const fields: FilterFields[] = [];
    if(typeof path === "object" && !Array.isArray(path) && path !== null) {
        // Loop through all sections of path
        for(const section in path) {
            if(path[String(section)]) {
                // Loop through all fields of that section
                path[String(section)].forEach((field: any) => {
                    if(field.filterable && field.type) {
                        const isFlexibleField = Boolean(field.flexibleField);
                        fields.push({
                            name: field.name,
                            displayName: field.displayName,
                            isFlexibleField: isFlexibleField,
                            key: isFlexibleField ? field.key : null,
                            allowedFilters: typeAllowedFiltersMapping[field.type as ValueTypesEnum] || [],
                            type: field.type,
                            organizations: field.organizations,
                            ignoreSanitize: field.ignoreSanitize,
                            policyTypes: field.policyTypes,
                            applicationPolicyModules: field.applicationPolicyModules
                        });
                    }
                });
            }
        }
    }
    return fields;
};
