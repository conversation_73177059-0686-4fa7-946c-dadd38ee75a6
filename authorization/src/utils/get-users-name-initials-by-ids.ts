import { getUserInitials, getUserName } from ".";
import { User } from "../models/users";

export const getUsersNameInitialsByIds = async (usersIds: string[]) => {
    const userIds = new Set([...usersIds]);
    const modifiedByUsers = await User.find({ _id: [...userIds] }, { firstName: 1, lastName: 1, displayName: 1, email: 1, policyIds: 1 }).lean().exec();
    const modifiedByUsersMap = new Map();
    modifiedByUsers.forEach(user => {
        modifiedByUsersMap.set(
            String(user._id),
            {
                name: getUserName(
                    {
                        firstName: user.firstName,
                        lastName: user.lastName,
                        displayName: user.displayName
                    }
                ),
                initials: getUserInitials(
                    {
                        firstName: user.firstName,
                        lastName: user.lastName,
                        displayName: user.displayName,
                        email: user.email
                    }
                ),
            }

        );
    });

    return modifiedByUsersMap;
};
