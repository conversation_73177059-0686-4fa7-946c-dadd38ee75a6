import mongoose from "mongoose";
import { ObjectId } from "bson";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

// An interface that describes the properties
// that are required to create a new OrganizationType
interface OrganizationTypeAttr {
    name: string;
}

// An interface that describes the properties that OrganizationType Type Document has
interface OrganizationTypeDoc extends mongoose.Document {
    _id: ObjectId;
    name : string;
}

// An interface that describes the properties
// that a OrganizationType Model has
interface OrganizationTypeModel extends mongoose.Model<OrganizationTypeDoc> {
    build(attrs: OrganizationTypeAttr): OrganizationTypeDoc;
}

const organizationTypeSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now,
        },
        updatedAt: {
            type: Date,
            default: Date.now,
        },
    }
);

organizationTypeSchema.set("versionKey", "version");
organizationTypeSchema.plugin(updateIfCurrentPlugin);

organizationTypeSchema.statics.build = (attrs: OrganizationTypeAttr) => {
    return new OrganizationType(attrs);
};

const OrganizationType = mongoose.model<OrganizationTypeDoc, OrganizationTypeModel>("OrganizationType", organizationTypeSchema);

export { OrganizationType, OrganizationTypeDoc };
