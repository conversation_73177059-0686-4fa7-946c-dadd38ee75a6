import mongoose from "mongoose";

// An interface that describes the properties that Service Document has
interface ServiceDoc extends mongoose.Document {
    id: string,
    name: string,
    displayName?: string,
    description: string,
    eligiblePolicyTypes: string[]
}

const ServiceSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true,
        },
        description: {
            type: String,
            required: false,
        },
        eligiblePolicyTypes: {
            type: [String],
            required: true
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const Service = mongoose.model<ServiceDoc>("Service", ServiceSchema);

export { Service, ServiceDoc };
