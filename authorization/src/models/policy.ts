import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";

// An interface that describes the properties that are required to create a new Policy
interface PolicyAttrs {
    name: string;
    description: string;
    isEnabled: boolean;
    actionIds: string[];
    type: "Global" | "Application";
    createdBy: string;
    updatedBy: string;
}

// An interface that describes the properties that a Policy Document has
interface PolicyDoc extends mongoose.Document {
    id: string;
    name: string;
    description: string;
    isEnabled: boolean;
    actionIds: string[];
    type: "Global" | "Application";
    createdBy: string;
    updatedBy: string;
    createdAt: string;
    updatedAt: string;
    version: number;
}

// An interface that describes the properties that a Policy Model has
interface PolicyModel extends mongoose.Model<PolicyDoc> {
  build(attrs: PolicyAttrs): PolicyDoc
}

const policySchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        description: {
            type: String
        },
        isEnabled: {
            type: Boolean,
            default: true,
            required: true
        },
        actionIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Action",
            required: true
        },
        type: {
            type: String,
            required: true
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "User"
        },
        updatedAt: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

policySchema.set("versionKey", "version");
policySchema.plugin(updateIfCurrentPlugin);

policySchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

policySchema.statics.build = (attrs: PolicyAttrs) => {
    return new Policy(attrs);
};

const Policy = mongoose.model<PolicyDoc, PolicyModel>("Policy", policySchema);

export { Policy, PolicyDoc, PolicyAttrs };
