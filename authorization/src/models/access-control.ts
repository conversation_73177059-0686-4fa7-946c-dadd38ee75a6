import mongoose from "mongoose";

// An interface that describes the properties that a AccessControl Document has
interface AccessControlDoc extends mongoose.Document {
  id: string,
  name: string,
  description: string,
}

const accessControlSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            unique: true,
            required: true
        },
        description: {
            type: String
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

const AccessControl = mongoose.model<AccessControlDoc>("AccessControl", accessControlSchema);

export { AccessControl, AccessControlDoc };
