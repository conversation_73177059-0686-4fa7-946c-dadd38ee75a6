import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    requireAuth,
    responseHand<PERSON>
} from "@moxfive-llc/common";
import { authorizationFields } from "../../utils/authorization-fields";
import { fetchModuleFilterFieldsBasedOnPermission } from "../../utils/fetch-module-filter-fields-based-on-permission";

const router = express.Router();

router.get("/v1/authorization/policies/facets",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check permission
            req.action = "ListPolicyFacets";

            // Step 2: Fetch filterable fields
            const fields = await fetchModuleFilterFieldsBasedOnPermission({
                sections: [
                    {
                        path: authorizationFields.policies,
                    },
                    {
                        path: authorizationFields.modifiedAt
                    }
                ],
                assignedActions: new Set()
            });

            // Step 3: Send response
            res.sendResponse(fields, {});
        }
        catch (error) {
            console.error("Incident.fetchPoliciesFilters");
            console.error(error);
            next(error);
        }
    });

export { router as fetchPoliciesFiltersRouter };
