import { createPolicy } from "./create-policy/create-policy";
import { getPolicies } from "./get-policies/get-policies";
import { updatePolicy } from "./update-policy/update-policy";
import { getServices } from "./get-services/get-services";
import { getActions } from "./get-actions/get-actions";
import { getPolicyDetails } from "./get-policy-details/get-policy-details";
import { togglePolicyStatus } from "./toggle-policy-status/toggle-policy-status";
import { deletePolicy } from "./delete-policy/delete-policy";
import { getServicesByPolicy } from "./get-services-by-policy/get-services-by-policy";
import { getActionsByServicesAndPolicy } from "./get-actions-by-policy-and-service/get-actions-by-policy-and-service";
import { listPolicyEntities } from "./list-entities-by-policy/list-entities-by-policy";
import { attachPolicyToEntities } from "./attach-policy-to-entities/attach-policy-to-entities";
import { detachPolicyFromEntities } from "./detach-policy-from-entities/detach-policy-from-entities";
import { getPoliciesForEntity } from "./get-policies-for-entity/get-policies-for-entity";
import { attachPoliciesToEntity } from "./attach-policies-to-entity/attach-policies-to-entity";
import { detachPoliciesFromEntity } from "./detach-policies-from-entity/detach-policies-from-entity";
import { attachPolicyToIncidentUsers } from "./attach-policy-to-incident-users/attach-policy-to-incident-users";
import { attachPolicyToResilienceUsers } from "./attach-policy-to-resilience-users/attach-policy-to-resilience-users";
import { fetchPoliciesFiltersRouter } from "./fetch-policies-filters/fetch-policies-filters";
import {
    fetchPlatformEntitiesForPolicyRouter
} from "./fetch-entities-by-policy-filters/fetch-entities-by-policy-filters";
import {
    attachPolicyToIncidentUsersVersionOnePointOne
} from "./v1.1/attach-policy-to-incident-users/attach-policy-to-incident-users";
import {
    attachPolicyToResilienceUsersVersionOnePointOne
} from "./v1.1/attach-policy-to-resilience-users/attach-policy-to-resilience-users";
import { getEventLogStatisticsRouter } from "./get-event-log-statistics/get-event-log-statistics";
import { listPoliciesV2Router } from "./v2/List-policies/list-policies";
