import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, InsufficientPrivilagesError, responseHandler } from "@moxfive-llc/common";

import { Service } from "../../models/service";

const router = express.Router();

router.get(
    "/v1/authorization/policies/services",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToListServicesForPolicy = await hasGlobalAction(req, "ListServices");
            if (!hasActionToListServicesForPolicy) {
                throw new InsufficientPrivilagesError();
            }

            const services = await Service.find();
            const finalResp = services.map(service => {
                const serviceObj = { ...service.toJSON() };
                const matchedWords = serviceObj.name.match(/[A-Z][a-z]+/g);
                serviceObj["displayName"] = (matchedWords && matchedWords.length) ? matchedWords.join(" ") : serviceObj.name;

                return serviceObj;
            });
            res.sendResponse(finalResp.filter(service => !!(service.eligiblePolicyTypes && service.eligiblePolicyTypes.length)), {});
        }
        catch (error) {
            console.error("Authorization.GetServices");
            console.error(error);
            next(error);
        }

    }
);

export { router as getServices };
