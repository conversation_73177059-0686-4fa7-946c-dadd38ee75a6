import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const updatePolicyValidation = [
    param("policyId")
        .isMongoId(),

    body("name")
        .optional()
        .isString().trim().blacklist("<>").isLength({ min: 1, max: 100 }).withMessage("Name must be max 100 characters long."),

    body("description")
        .optional()
        .isString().trim().blacklist("<>").isLength({ min: 1, max: 2000 }).withMessage("Description must be max 2000 characters long."),

    body("actionIds")
        .optional()
        .isArray({ min: 1 })
        .withMessage("Minimum 1 action is required to update a policy.").bail()
        .custom((actionIds: string[]) => {
            return actionIds.every(actionId => {
                return isValidMongoObjectId(actionId);
            });
        }).withMessage("These action ids are invalid.")
];
