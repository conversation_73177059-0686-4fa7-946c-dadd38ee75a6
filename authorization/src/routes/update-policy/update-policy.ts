/* eslint-disable max-statements */
/* eslint-disable max-depth */
import express, { Request, Response, NextFunction } from "express";
import {
    currentUser,
    requireAuth,
    hasGlobalAction,
    validateRequest,
    ResourceNotFoundError,
    NotFoundCode,
    InsufficientPrivilagesError,
    ResourceAlreadyExistBadRequestError,
    InvalidResourceIdBadRequestError,
    ActionsConflictsError,
    responseHandler,
    TargetType,
    NonMoxfiveUserWithMfExclusiveTypesEnum
} from "@moxfive-llc/common";

import { Policy } from "../../models/policy";
import {
    ACTIVE_FOCUS_VIEW_ACTION,
    escapeRegExp,
    getUserName,
    insertWhiteSpaces,
    intersectTwoObjects,
    pickFromObject
} from "../../utils";
import { updatePolicyValidation } from "./update-policy.validation";
import { Action } from "../../models/action";
import { policyUpdatedPublisherWrapper } from "../../utils/policy-updated-publisher-wrapper";
import { Service } from "../../models/service";
import { prepareModifiedProperties } from "../../utils/prepare-modified-properties";
import { User } from "../../models/users";
import { ActiveFocusViewRemovedPublisherWrapper } from "../../utils/active-focus-view-removed-wrapper";
import { queueGroupName } from "../../events/queue-group-name";
import {
    NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper
} from "../../utils/non-mf-user-with-mf-exclusive-permission-notification-publisher-wrapper";
import { getUsersNameInitialsByIds } from "../../utils/get-users-name-initials-by-ids";
import { Organization } from "../../models/organizations";

const router = express.Router();

router.put(
    "/v1/authorization/policies/:policyId",
    responseHandler,
    currentUser,
    requireAuth,
    updatePolicyValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToUpdatePolicy = await hasGlobalAction(req, "UpdatePolicy");
            if (!hasActionToUpdatePolicy) {
                throw new InsufficientPrivilagesError();
            }

            const { policyId } = req.params;

            const policy = await Policy.findById(policyId);
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            if (policy.name === "Super Admin") {
                throw new InsufficientPrivilagesError();
            }

            // Make actions map
            const actions = await Action.find({}).select("moxfiveExclusive").lean().exec();
            const actionsMap: Map<string, boolean> = new Map();
            actions.forEach(action => actionsMap.set(String(action._id), action.moxfiveExclusive));

            const policyData = pickFromObject(policy, ["name", "description", "actionIds"]);
            const updatedData = intersectTwoObjects(policyData, req.body);

            // For audit logs
            const data = { ...updatedData };
            const oldData = { ...policyData };
            const actionIdToNameMap = new Map();
            let actionWasPresent = false;
            let stillActionPresent = false;

            if (updatedData.name) {
                const nameRegex = { $regex: `^${escapeRegExp(updatedData.name)}$`, $options: "i" };
                const policyDetails = await Policy.findOne({ name: nameRegex, _id: { $ne: policyId } });
                if (policyDetails) {
                    throw new ResourceAlreadyExistBadRequestError("name", updatedData.name, "Policy name already in use.");
                }
            }

            if (updatedData.actionIds && updatedData.actionIds.length) {
                // Fetch all the action details (Old and new one)
                const updatedActions = [...updatedData.actionIds].map(String);
                const fetchedPermissions = await Action.find({ _id: { $in: [...new Set([...updatedActions, ...oldData.actionIds.map(String)])] }, requireAuthorization: true },
                    { _id: 1, name: 1, serviceId: 1 });

                // Filter out new actions
                const newActions = fetchedPermissions.filter(action => updatedActions.includes(String(action._id)));
                if (newActions.length !== updatedData.actionIds.length) {
                    throw new InvalidResourceIdBadRequestError([{ name: "actionIds", value: updatedData.actionIds, message: "This action ids are invalid." }]);
                }

                if (policy.type === "Application") {
                    // Prepare serviceIds
                    const serviceIds = new Set();
                    newActions.forEach((action) => {
                        serviceIds.add(action.serviceId);
                    });

                    // Fetch service details and filter out global services
                    const serviceDetails = await Service.find({ _id: { $in: [...serviceIds] } }, { eligiblePolicyTypes: 1 });
                    const globalService = serviceDetails.filter(service => !service.eligiblePolicyTypes.includes("Application")).map(service => String(service.id));

                    if (globalService && globalService.length) {
                        const invalidActions = newActions.filter(action => globalService.includes(String(action.serviceId)))
                            .map(action => insertWhiteSpaces(action.name)) as string[];

                        if(invalidActions.length) {
                            throw new ActionsConflictsError(invalidActions);
                        }
                    }
                }

                fetchedPermissions.forEach(action => actionIdToNameMap.set(String(action._id), action.name));
            }

            if (Object.keys(updatedData).length === 0) {
                return res.sendResponse({
                    meta: {
                        message: "Policy successfully updated."
                    }
                }, {});
            }

            Object.assign(policy, updatedData);
            policy.updatedBy = req.currentUser ? req.currentUser.id : "";
            await policy.save();
            await policyUpdatedPublisherWrapper(policy);

            if (data.actionIds) {
                // Check whether moxfive exclusive permission already present or not in the policy actionIds and if not then continue
                const isMoxfiveExclusiveActionPresent = oldData.actionIds.some((action: string) => actionsMap.get(String(action)));

                if(!isMoxfiveExclusiveActionPresent) {
                    // Check whether moxfive exclusive permission provided in updated data, if yes then continue
                    const isMoxfiveExclusiveActionProvided = data.actionIds.some((action: string) => actionsMap.get(String(action)));

                    if(isMoxfiveExclusiveActionProvided) {
                        const userAssignedWithMoxfiveExclusiveMap: Map<string, {
                            name: string,
                            organization: string
                        }> = new Map();
                        const organizationIds: string[] = [];

                        // Check policy type
                        if(policy.type === "Global") {
                            // Fetch users who have been assigned with this policy
                            const users = await User.find({ policyIds: policyId, organizationId: { $ne: process.env.MOXFIVE_ID } }).lean().exec();

                            // Loop through users and add entry in userAssignedWithMoxfiveExclusiveMap
                            users.forEach(user => {
                                userAssignedWithMoxfiveExclusiveMap.set(String(user._id), {
                                    name: getUserName({
                                        firstName: user.firstName,
                                        lastName: user.lastName,
                                        displayName: user.displayName
                                    }),
                                    organization: String(user.organizationId)
                                });

                                organizationIds.push(String(user.organizationId));
                            });
                        }
                        else if(policy.type === "Application") {
                            // Fetch users who have been assigned with this policy
                            const users = await User.find({ "applicationPolicyIds.policyId": policyId, organizationId: { $ne: process.env.MOXFIVE_ID } }).lean().exec();

                            // Loop through users and add entry in userAssignedWithMoxfiveExclusiveMap
                            users.forEach(user => {
                                userAssignedWithMoxfiveExclusiveMap.set(String(user._id), {
                                    name: getUserName({
                                        firstName: user.firstName,
                                        lastName: user.lastName,
                                        displayName: user.displayName
                                    }),
                                    organization: String(user.organizationId)
                                });

                                organizationIds.push(String(user.organizationId));
                            });
                        }

                        if(userAssignedWithMoxfiveExclusiveMap.size) {
                            // Fetch assigned users name and initials
                            let assignedUserMap = new Map();
                            if(req.currentUser?.id) {
                                assignedUserMap = await getUsersNameInitialsByIds([req.currentUser.id]);
                            }

                            // Fetch organizations name
                            const organizations = await Organization.find({ _id: { $in: organizationIds } }).select("name").lean().exec();
                            const organizationsMap: Map<string, string> = new Map();
                            organizations.forEach(organization => {
                                organizationsMap.set(String(organization._id), organization.name);
                            });

                            const eventData = {
                                recipientUserIds: Array.from(userAssignedWithMoxfiveExclusiveMap.keys()),
                                serviceName: queueGroupName,
                                assignedToUsers: Array.from(userAssignedWithMoxfiveExclusiveMap.values()).map(user => {
                                    return {
                                        name: user.name,
                                        organization: organizationsMap.get(String(user.organization)) || "",
                                    };
                                }),
                                assignedByName: assignedUserMap.get(req.currentUser?.id)?.name || "",
                                assignedByInitials: assignedUserMap.get(req.currentUser?.id)?.initials || "",
                                policies: [policy.name],
                                type: NonMoxfiveUserWithMfExclusiveTypesEnum.SINGLE_POLICY,
                            };

                            await NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper(eventData);
                        }
                    }

                }

                // Check whether the ActiveFocusView action got removed while updating the policy
                actionWasPresent = !!oldData.actionIds.find((actionId: string) => actionIdToNameMap.get(String(actionId)) === ACTIVE_FOCUS_VIEW_ACTION);
                stillActionPresent = !!data.actionIds.find((actionId: string) => actionIdToNameMap.get(String(actionId)) === ACTIVE_FOCUS_VIEW_ACTION);

                data.actionIds = data.actionIds.map((action: string) => ({
                    id: action,
                    value: insertWhiteSpaces(actionIdToNameMap.get(String(action)) ?? "")
                }));
                oldData.actionIds = oldData.actionIds.map((action: string) => ({
                    id: action,
                    value: insertWhiteSpaces(actionIdToNameMap.get(String(action)) ?? "")
                }));
            }

            const modifiedProperties = prepareModifiedProperties({ data, oldData, target: TargetType.POLICY });

            res.sendResponse({
                meta: {
                    message: "Policy successfully updated."
                }
            }, {
                targets: [{
                    type: TargetType.POLICY,
                    details: {
                        id: policyId,
                        name: policy.name
                    }
                }],
                modifiedProperties
            });

            // For focus view
            if (actionWasPresent && !stillActionPresent) {
                const query = policy.type === "Global"
                    ? { policyIds: policy._id }
                    : { "applicationPolicyIds.policyId": policy._id };

                const users = await User.find(query, { _id: 1 }).lean().exec();
                if (users && users.length) {
                    await ActiveFocusViewRemovedPublisherWrapper(users.map(user => String(user._id)));
                }
            }

        }
        catch (error) {
            console.error("Authorization.UpdatePolicy");
            console.error(error);
            next(error);
        }
    }
);

export { router as updatePolicy };
