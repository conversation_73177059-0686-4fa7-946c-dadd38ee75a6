// /**
//  * @swagger
//  * /v1/authorization/roles/{roleId}:
//  *   put:
//  *     summary: Update role.
//  *     description: this will update role.
//  *     tags:
//  *       - Roles
//  *     parameters:
//  *       - in: path
//  *         name: roleId
//  *         required: true
//  *         description: role id.
//  *         schema:
//  *           type : string
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *             $ref: "#/components/schemas/RoleAddEdit"
//  *     responses:
//  *       200: #response type
//  *         description: OK
//  *         content:
//  *           application/json:
//  *               example:
//  *                  {
//  *                    "id": "623c684a1f28c5b8defddb8a",
//  *                    "meta": {
//  *                        "message": "Role successfully updated."
//  *                    }
//  *                  }
//  *       401:
//  *         description: Not authorized
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Not authorized"} ] }
//  *       400:
//  *         description: Bad Request
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "There is no such organization exist", "field": "organizationTypeId" } ] }
//  *       404:
//  *         description: Not Found
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Role not exist." } ] }
//  */
