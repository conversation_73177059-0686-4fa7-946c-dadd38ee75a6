import express, { Request, Response, NextFunction } from "express";
import {
    currentUser,
    hasGlobalAction,
    requireAuth,
    validateRequest,
    InsufficientPrivilagesError,
    ResourceNotFoundError,
    NotFoundCode,
    SucceededPartially,
    BasicResourceValueUnacceptableConflictError,
    ConflictErrorCodes,
    responseHandler,
    TargetType,
    NonMoxfiveUserWithMfExclusiveTypesEnum,
    InternalServerError
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { entityObj } from "../../interfaces/entity-object";
import { attachPolicyToEntitiesValidation } from "./attach-policy-to-entities.validation";
import { Incident, IncidentDoc } from "../../models/incident";
import { incidentPolicyUpdatedPublisherWrapper } from "../../utils/incident-policy-updated-publisher-wrapper";
import { Tracker, TrackerDoc } from "../../models/tracker";
import { trackerPolicyUpdatedPublisherWrapper } from "../../utils/tracker-policy-updated-publisher-wrapper";
import { checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { Organization } from "../../models/organizations";
import {
    SuperAdminPolicyAssignedNotificationPublisher
} from "../../events/publishers/super-admin-policy-assigned-notification-publisher";
import { natsWrapper } from "../../nats-wrapper";
import { queueGroupName } from "../../events/queue-group-name";
import { getUsersNameInitialsByIds } from "../../utils/get-users-name-initials-by-ids";
import { Action } from "../../models/action";
import { isMOXFIVEUser } from "../../utils/is-moxfive-user";
import {
    NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper
} from "../../utils/non-mf-user-with-mf-exclusive-permission-notification-publisher-wrapper";

const router = express.Router();

router.put("/v1/authorization/policies/:policyId/entities/attach",
    responseHandler,
    currentUser,
    requireAuth,
    attachPolicyToEntitiesValidation,
    validateRequest,
    // eslint-disable-next-line max-statements
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { policyId } = req.params;
            const { entities } = req.body;

            // For audit logs
            const targets: any = [];
            const modifiedProperties: any = [];

            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "AttachPolicyToPlatformEntities");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            if (!req.currentUser) {
                throw new InternalServerError();
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            const failedUserEntities: string[] = [];
            const failedIncidentEntities: string[] = [];
            const failedResilienceEntities: string[] = [];
            const userMap = new Map<string, UserDoc>();
            const incidentMap = new Map<string, IncidentDoc>();
            const resilienceMap = new Map<string, TrackerDoc>();
            await Promise.all(entities.map(async (entity: entityObj) => {
                if (entity.type === "User") {
                    const user = await User.findById(entity.id);
                    if (user) {
                        // Set in user map to fetch later.
                        userMap.set(entity.id, user);

                        // Check for Non-Moxfive user.
                        if (!isMoxfiveUserOrSuperAdmin) {
                            // Can only assign permission to users of his organization only.
                            if (req.currentUser?.organizationId !== String(user.organizationId)) throw new InsufficientPrivilagesError();
                        }
                    }
                    else {
                        failedUserEntities.push(entity.id);
                    }
                }
                else if (entity.type === "Incident") {
                    const incident = await Incident.findById(entity.id);
                    if (incident) {
                        // Set in user map to fetch later.
                        incidentMap.set(entity.id, incident);

                        // Check for Non-Moxfive user.
                        if (!isMoxfiveUserOrSuperAdmin) {
                            const matcheduser = (incident.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                            if (!matcheduser) throw new InsufficientPrivilagesError();
                        }
                    }
                    else {
                        failedIncidentEntities.push(entity.id);
                    }
                }
                else if (entity.type === "Resilience") {
                    const resilience = await Tracker.findById(entity.id);
                    if (resilience) {
                        // Set in user map to fetch later.
                        resilienceMap.set(entity.id, resilience);

                        // Check for Non-Moxfive user.
                        if (!isMoxfiveUserOrSuperAdmin) {
                            const matcheduser = (resilience.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                            if (!matcheduser) throw new InsufficientPrivilagesError();
                        }
                    }
                    else {
                        failedResilienceEntities.push(entity.id);
                    }
                }
            }));

            const policy = await Policy.findById(policyId).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const isGlobalPolicy = policy.type === "Global";
            if (isGlobalPolicy) {
                const invalidEntities = entities.filter((entity: { id: string, type: string }) => entity.type !== "User");
                if (invalidEntities && invalidEntities.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_CONFLICT, "Global Type policy cannot attached to incidents/resiliences.");
                }
            }
            else {
                const invalidEntities = entities.filter((entity: { id: string, type: string }) => entity.type === "User");
                if (invalidEntities && invalidEntities.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.APPLICATION_TYPE_CONFLICT, "Application Type policy cannot attached to users.");
                }
            }

            // Make actions map
            const actions = await Action.find({}).select("moxfiveExclusive").lean().exec();
            const actionsMap: Map<string, boolean> = new Map();
            actions.forEach(action => actionsMap.set(String(action._id), action.moxfiveExclusive));

            const policyIdToNameMap = new Map();
            policyIdToNameMap.set(String(policy._id), policy.name);

            // Check whether super admin policy assigned and moxfive exclusive permission is assigned or not
            const isSuperAdminPolicyAssigned = policyId === process.env.SUPER_ADMIN_POLICY_ID;
            const isMoxfiveExclusivePermissionAssigned = !isSuperAdminPolicyAssigned && policy.actionIds.some(action => actionsMap.get(String(action)));

            const usersAssignedWithSuperAdminMap: Map<string, {
                name: string,
                organization: string
            }> = new Map();

            const userAssignedWithMoxfiveExclusiveMap: Map<string, {
                name: string,
                organization: string
            }> = new Map();
            const organizationIds: string[] = [];

            await Promise.all(entities.map(async (entity: entityObj) => {
                if (entity.type === "User") {
                    const user = userMap.get(entity.id);
                    if (user) {
                        const oldValue = [...user.policyIds.map(String)];
                        if (!oldValue.includes(policyId)) {
                            user.policyIds?.push(policyId);
                            await user.save();
                            await userPolicyUpdatedPublisherWrapper(user);

                            // If super admin policy assigned and add user in usersAssignedWithSuperAdminMap
                            if(isSuperAdminPolicyAssigned) {
                                usersAssignedWithSuperAdminMap.set(String(entity.id), {
                                    name: getUserName({
                                        firstName: user.firstName,
                                        lastName: user.lastName,
                                        displayName: user.displayName
                                    }),
                                    organization: String(user.organizationId)
                                });

                                organizationIds.push(String(user.organizationId));
                            }

                            // If moxfive exclusive permission assigned
                            if(isMoxfiveExclusivePermissionAssigned) {
                                // Check if user is moxfive user
                                const isMoxfiveUser = isMOXFIVEUser({
                                    req,
                                    throwError: false,
                                    organizationId: String(user.organizationId)
                                });

                                // If user is not moxfive user then add user in userAssignedWithMoxfiveExclusiveMap
                                // eslint-disable-next-line max-depth
                                if(!isMoxfiveUser) {
                                    userAssignedWithMoxfiveExclusiveMap.set(String(entity.id), {
                                        name: getUserName({
                                            firstName: user.firstName,
                                            lastName: user.lastName,
                                            displayName: user.displayName
                                        }),
                                        organization: String(user.organizationId)
                                    });

                                    organizationIds.push(String(user.organizationId));
                                }
                            }

                            // For audit logs
                            if (oldValue.length) {
                                const fetchedPolicies = await Policy.find({ _id: { $in: oldValue } }, { name: 1 }).lean().exec();
                                fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                            }

                            targets.push({
                                type: TargetType.USER,
                                details: {
                                    id: user.id,
                                    name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                                    email: user.email,
                                    azureId: ""
                                }
                            });
                            modifiedProperties.push({
                                target: TargetType.USER,
                                propertyName: "policyIds",
                                oldValue: JSON.stringify(oldValue.map(value => ({
                                    id: value,
                                    value: policyIdToNameMap.get(String(value))
                                }))),
                                newValue: JSON.stringify(user.policyIds.map(value => ({
                                    id: String(value),
                                    value: policyIdToNameMap.get(String(value))
                                })))
                            });
                        }
                    }
                }
                else if (entity.type === "Incident") {
                    const incident = incidentMap.get(entity.id);
                    if (incident) {
                        const oldValue = [...incident.policyIds.map(String)];
                        if (!oldValue.includes(policyId)) {
                            incident.policyIds.push(policyId);
                            await incident.save();
                            await incidentPolicyUpdatedPublisherWrapper(incident);

                            // For audit logs
                            if (oldValue.length) {
                                const fetchedPolicies = await Policy.find({ _id: { $in: oldValue } }, { name: 1 }).lean().exec();
                                fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                            }

                            targets.push({
                                type: TargetType.INCIDENT,
                                details: {
                                    id: incident.id,
                                    name: incident.name
                                }
                            });
                            modifiedProperties.push({
                                target: TargetType.INCIDENT,
                                propertyName: "policyIds",
                                oldValue: JSON.stringify(oldValue.map(value => ({
                                    id: value,
                                    value: policyIdToNameMap.get(String(value))
                                }))),
                                newValue: JSON.stringify(incident.policyIds.map(value => ({
                                    id: String(value),
                                    value: policyIdToNameMap.get(String(value))
                                })))
                            });
                        }
                    }
                }
                else if (entity.type === "Resilience") {
                    const resilience = resilienceMap.get(entity.id);
                    if (resilience) {
                        const oldValue = [...resilience.policyIds.map(String)];
                        if (!oldValue.includes(policyId)) {
                            resilience.policyIds.push(policyId);
                            await resilience.save();
                            await trackerPolicyUpdatedPublisherWrapper(resilience);

                            // For audit logs
                            if (oldValue.length) {
                                const fetchedPolicies = await Policy.find({ _id: { $in: oldValue } }, { name: 1 }).lean().exec();
                                fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                            }

                            targets.push({
                                type: TargetType.BUSINESSRESILIENCE,
                                details: {
                                    id: resilience.id,
                                    name: resilience.name
                                }
                            });
                            modifiedProperties.push({
                                target: TargetType.BUSINESSRESILIENCE,
                                propertyName: "policyIds",
                                oldValue: JSON.stringify(oldValue.map(value => ({
                                    id: value,
                                    value: policyIdToNameMap.get(String(value))
                                }))),
                                newValue: JSON.stringify(resilience.policyIds.map(value => ({
                                    id: String(value),
                                    value: policyIdToNameMap.get(String(value))
                                })))
                            });
                        }
                    }
                }
            }));

            if (failedUserEntities.length === entities.length || failedIncidentEntities.length === entities.length) {
                throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entities are not found in the list of eligible entities for policy.");
            }

            // If super admin policy assigned or moxfive exclusive permission assigned then publish SuperAdminPolicyAssignedNotification
            if(isSuperAdminPolicyAssigned || isMoxfiveExclusivePermissionAssigned) {
                // Prepare organizations map
                const organizations = await Organization.find({ _id: { $in: organizationIds } }).select("name").lean().exec();
                const organizationsMap: Map<string, string> = new Map();
                organizations.forEach(organization => {
                    organizationsMap.set(String(organization._id), organization.name);
                });

                // Fetch assigned name and initials
                let assignedUserMap = new Map();
                if(req.currentUser?.id) {
                    assignedUserMap = await getUsersNameInitialsByIds([req.currentUser.id]);
                }

                if(isSuperAdminPolicyAssigned) {
                    const data = {
                        recipientUserIds: Array.from(usersAssignedWithSuperAdminMap.keys()),
                        serviceName: queueGroupName,
                        assignedToUsers: Array.from(usersAssignedWithSuperAdminMap.values()).map(user => {
                            return {
                                name: user.name,
                                organization: organizationsMap.get(String(user.organization)) || ""
                            };
                        }),
                        assignedByName: assignedUserMap.get(req.currentUser?.id)?.name || "",
                        assignedByInitials: assignedUserMap.get(req.currentUser?.id)?.initials || ""
                    };

                    await new SuperAdminPolicyAssignedNotificationPublisher(natsWrapper.client).publish(data);
                }

                if(isMoxfiveExclusivePermissionAssigned && userAssignedWithMoxfiveExclusiveMap.size) {
                    const data = {
                        recipientUserIds: Array.from(userAssignedWithMoxfiveExclusiveMap.keys()),
                        serviceName: queueGroupName,
                        assignedToUsers: Array.from(userAssignedWithMoxfiveExclusiveMap.values()).map(user => {
                            return {
                                name: user.name,
                                organization: organizationsMap.get(String(user.organization)) || "",
                            };
                        }),
                        assignedByName: assignedUserMap.get(req.currentUser?.id)?.name || "",
                        assignedByInitials: assignedUserMap.get(req.currentUser?.id)?.initials || "",
                        policies: [policy.name],
                        type: NonMoxfiveUserWithMfExclusiveTypesEnum.SINGLE_POLICY,
                    };

                    await NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper(data);
                }

            }

            if (failedUserEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedUserEntities,
                        message: "Users not found."
                    }]
                }], "One or more users are failed to attached with the specified policy.");
            }

            if (failedIncidentEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedIncidentEntities,
                        message: "Incidents not found."
                    }]
                }], "One or more incidents are failed to attached with the specified policy.");
            }

            if (failedResilienceEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedResilienceEntities,
                        message: "Resiliences not found."
                    }]
                }], "One or more resiliences are failed to attached with the specified policy.");
            }

            res.sendResponse({
                meta: {
                    message: "Policy attached successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.AttachPolicyToEntities");
            console.error(error);
            next(error);
        }
    });

export { router as attachPolicyToEntities };
