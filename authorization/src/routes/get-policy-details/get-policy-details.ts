import express, { Request, Response, NextFunction } from "express";
import { currentUser, hasGlobalAction, requireAuth, validateRequest, ResourceNotFoundError, NotFoundCode, InsufficientPrivilagesError, responseHandler } from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User } from "../../models/users";
import { getPolicyDetailsValidation } from "./get-policy-details.validation";
import { getUserName } from "../../utils";

const router = express.Router();

router.get("/v1/authorization/policies/:policyId",
    responseHandler,
    currentUser,
    requireAuth,
    getPolicyDetailsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "GetPolicyDetail");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            const { policyId } = req.params;

            const policy = await Policy.findById(policyId).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const userIds = new Set([policy.createdBy, policy.updatedBy]);
            const modifiedByUsers = await User.find({ _id: [...userIds] }, { firstName: 1, lastName: 1, displayName: 1, policyIds: 1 }).lean().exec();
            const modifiedByUsersMap = new Map();
            modifiedByUsers.forEach(user =>
                modifiedByUsersMap.set(
                    String(user._id),
                    getUserName(
                        {
                            firstName: user.firstName,
                            lastName: user.lastName,
                            displayName: user.displayName
                        }
                    )
                )
            );

            res.sendResponse({
                id: policy._id,
                name: policy.name,
                description: policy.description,
                type: policy.type,
                isEnabled: policy.isEnabled,
                createdBy: modifiedByUsersMap.get(String(policy.createdBy)),
                updatedBy: modifiedByUsersMap.get(String(policy.updatedBy)),
                createdAt: policy.createdAt,
                updatedAt: policy.updatedAt
            }, {});
        }
        catch (error) {
            console.error("Authorization.GetPolicyDetails");
            console.error(error);
            next(error);
        }
    });

export { router as getPolicyDetails };
