// /**
//  * @swagger
//  * /v1/authorization/roles/{roleId}:
//  *   get:
//  *     summary: Get role detail.
//  *     description: this will send role detail.
//  *     tags:
//  *       - Roles
//  *     parameters:
//  *       - in: path
//  *         name: roleId
//  *         required: true
//  *         description: role id.
//  *         schema:
//  *           type : string
//  *     responses:
//  *       200: #response type
//  *         description: OK
//  *         content:
//  *           application/json:
//  *               example:
//  *                  {
//  *                  "roleName": "Super Admin",
//  *                  "isEnabled": true,
//  *                  "roleDescription": "This is a special role to assign with special users",
//  *                  "services": [
//  *                      {
//  *                      "id": "62300d0c6143c2e094c7a285",
//  *                      "serviceName": "User Management",
//  *                      "permissions": [
//  *                          {
//  *                          "permissionId": "62300d0d6143c2e094c7a28e",
//  *                          "access": "Write All",
//  *                          "selected": true
//  *                          },
//  *                          {
//  *                          "permissionId": "62300d0d6143c2e094c7a28d",
//  *                          "access": "Read All",
//  *                          "selected": false
//  *                          },
//  *                          {
//  *                          "permissionId": "62300d0d6143c2e094c7a28f",
//  *                          "access": "Create",
//  *                          "selected": false
//  *                          }
//  *                      ],
//  *                      "subServices": [
//  *                          {
//  *                          "id": "62300d0d6143c2e094c7a289",
//  *                          "serviceName": "Role",
//  *                          "parentId": "62300d0c6143c2e094c7a285",
//  *                          "permissions": [
//  *                              {
//  *                              "permissionId": "62300d0d6143c2e094c7a2b9",
//  *                              "access": "Read All",
//  *                              "selected": false
//  *                              },
//  *                              {
//  *                              "permissionId": "62300d0d6143c2e094c7a2ba",
//  *                              "access": "Write All",
//  *                              "selected": false
//  *                              },
//  *                              {
//  *                              "permissionId": "62300d0d6143c2e094c7a2bb",
//  *                              "access": "Create",
//  *                              "selected": false
//  *                              }
//  *                          ],
//  *                          "subServices": []
//  *                          }
//  *                      ]
//  *                      }
//  *                  ],
//  *                  "organizationType": "MOXFIVE",
//  *                  "createAt": "2022-03-24T09:34:23.848Z",
//  *                  "updatedAt": "2022-03-24T09:34:23.884Z"
//  *                  }
//  *       401:
//  *         description: Not authorized
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Not authorized"} ] }
//  *       404:
//  *         description: Not Found
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Role not exist." } ] }
//  */
