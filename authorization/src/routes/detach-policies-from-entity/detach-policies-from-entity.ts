/* eslint-disable max-statements */
import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, hasGlobalAction, requireAuth, validateRequest, InsufficientPrivilagesError, ResourceNotFoundError,
    NotFoundCode, SucceededPartially, BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, responseHandler, TargetType,
    InternalServerError
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { detachPoliciesFromEntityValidation } from "./detach-policies-from-entity.validation";
import { Incident } from "../../models/incident";
import { incidentPolicyUpdatedPublisherWrapper } from "../../utils/incident-policy-updated-publisher-wrapper";
import { Tracker } from "../../models/tracker";
import { trackerPolicyUpdatedPublisherWrapper } from "../../utils/tracker-policy-updated-publisher-wrapper";
import { ACTIVE_FOCUS_VIEW_ACTION, checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { getPolicyNameByIds } from "../../utils/get-policy-names-by-ids";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { getUserApplicationModifiedProperties } from "../../utils/get-user-application-modified-properties";
import { Action } from "../../models/action";
import { ActiveFocusViewRemovedPublisherWrapper } from "../../utils/active-focus-view-removed-wrapper";

const router = express.Router();

router.put("/v1/authorization/entities/:entityId/policies/detach",
    responseHandler,
    currentUser,
    requireAuth,
    detachPoliciesFromEntityValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { entityId } = req.params;
            const { type, policyIds } = req.body;
            const targets: any = [];
            const modifiedProperties: any = [];

            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "DetachPoliciesFromPlatformEntity");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            if (!req.currentUser) {
                throw new InternalServerError();
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            const policyDetails = await Policy.find({ _id: { $in: [...policyIds] } }, { _id: 1, type: 1, actionIds: 1 });
            if (!policyDetails || (policyDetails && !policyDetails.length)) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policies not found.");
            }

            // For Focus View, If ActiveFocusView action gets removed then publish an event
            let actionIds: string[] = [];
            policyDetails.forEach(policy => actionIds = actionIds.concat([...policy.actionIds]));
            const actions = await Action.find({ _id: { $in: [...new Set(actionIds)] }, name: ACTIVE_FOCUS_VIEW_ACTION }).lean().exec();
            const actionWasPresent = !!(actions && actions.length);

            const globalPolicies: string[] = [];
            const applicationPolicies: string[] = [];
            const inValidPolicyIds: string[] = [];
            const policiesIds = policyDetails.map(policy => String(policy.id));
            const modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[] = [];
            const modifiedPolicyIds: { oldValue: string[], newValue: string[] }[] = [];
            const userTargets: UserDoc[] = [];

            policyIds.forEach((policyId: string) => {
                if (policiesIds.includes(policyId)) {
                    const policy = policyDetails.find(policy => policy.id === policyId);
                    if (policy && policy.type === "Global") {
                        globalPolicies.push(policyId);
                    }
                    else {
                        applicationPolicies.push(policyId);
                    }
                }
                else {
                    inValidPolicyIds.push(policyId);
                }
            });

            if (type === "User") {
                const user = await User.findById(entityId);
                if (!user) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    // Can only assign permission to users of his organization only.
                    if (req.currentUser.organizationId !== String(user.organizationId)) throw new InsufficientPrivilagesError();
                }

                if (globalPolicies.length) {
                    const userPolicyIds = user.policyIds ? [...user.policyIds].map(String) : [];
                    const oldValue = [...userPolicyIds];
                    globalPolicies.forEach(policyId => {
                        const index = userPolicyIds.indexOf(policyId) as number;
                        if (index !== -1) {
                            userPolicyIds.splice(index, 1);
                        }
                    });

                    user.policyIds = [...userPolicyIds];
                    modifiedPolicyIds.push({
                        oldValue,
                        newValue: [...userPolicyIds]
                    });
                }

                if (applicationPolicies.length) {
                    const oldValue = [...user.applicationPolicyIds];
                    const userApplicationPolicies = [...user.applicationPolicyIds];
                    const userApplicationPolicyIds = userApplicationPolicies.map(policy => String(policy.policyId));
                    applicationPolicies.forEach((policyId) => {
                        const index = userApplicationPolicyIds.indexOf(policyId) as number;
                        if (index !== -1) {
                            userApplicationPolicies.splice(index, 1);
                            userApplicationPolicyIds.splice(index, 1);
                        }
                    });

                    user.applicationPolicyIds = [...userApplicationPolicies];
                    const newValue = [...userApplicationPolicies];
                    // For audit log
                    modifiedApplicationIds.push({
                        oldValue,
                        newValue
                    });
                }

                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);
                // For Focus View
                if (actionWasPresent) {
                    await ActiveFocusViewRemovedPublisherWrapper([entityId]);
                }

                // For audit logs
                userTargets.push(user);
            }
            else if (type === "Incident") {
                const incident = await Incident.findById(entityId);
                if (!incident) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    const matcheduser = (incident.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                    if (!matcheduser) throw new InsufficientPrivilagesError();
                }

                if (globalPolicies.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_DETACH_CONFLICT, "Global Type policy cannot be detached from incident.");
                }

                const policiesToBeDeleted: string[] = [];
                if (applicationPolicies.length) {
                    const incidentPolicyIds = [...incident.policyIds].map(String);
                    const oldValue = [...incidentPolicyIds];
                    applicationPolicies.forEach(policyId => {
                        const index = incidentPolicyIds.indexOf(policyId) as number;
                        if (index !== -1) {
                            incidentPolicyIds.splice(index, 1);
                            policiesToBeDeleted.push(policyId);
                        }
                    });

                    incident.policyIds = [...incidentPolicyIds];
                    await incident.save();
                    await incidentPolicyUpdatedPublisherWrapper(incident);

                    // For audit logs
                    const policyIdToNameMap = await getPolicyNameByIds(oldValue);
                    targets.push({
                        type: TargetType.INCIDENT,
                        details: {
                            id: incident.id,
                            name: incident.name
                        }
                    });

                    modifiedProperties.push({
                        target: TargetType.INCIDENT,
                        propertyName: "policyIds",
                        oldValue: JSON.stringify(oldValue.map((policyId: string) => ({
                            id: String(policyId),
                            value: policyIdToNameMap.get(String(policyId))
                        }))),
                        newValue: JSON.stringify(incident.policyIds.map((policyId: string) => ({
                            id: String(policyId),
                            value: policyIdToNameMap.get(String(policyId))
                        })))
                    });

                    const users = await User.find({ _id: { $in: [...incident.members] }, "applicationPolicyIds.policyId": { $in: [...policiesToBeDeleted] } });
                    await Promise.all(users.map(async (user) => {
                        const userApplicationPolicies = [...user.applicationPolicyIds];
                        const oldValue = [...user.applicationPolicyIds];
                        let index = -1;
                        policiesToBeDeleted.every(policyId => {
                            userApplicationPolicies.every((applicationPolicy, policyIndex) => {
                                if (String(applicationPolicy.policyId) === String(policyId)) {
                                    index = policyIndex;
                                    return false;
                                }
                            });
                            if (index !== -1) {
                                userApplicationPolicies.splice(index, 1);
                                return false;
                            }
                        });

                        user.applicationPolicyIds = [...userApplicationPolicies];
                        await user.save();
                        await userPolicyUpdatedPublisherWrapper(user);

                        userTargets.push(user);
                        modifiedApplicationIds.push({
                            oldValue,
                            newValue: user.applicationPolicyIds
                        });
                    }));
                }

            }
            else if (type === "Resilience") {
                const resilience = await Tracker.findById(entityId);
                if (!resilience) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    const matcheduser = (resilience.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                    if (!matcheduser) throw new InsufficientPrivilagesError();
                }

                if (globalPolicies.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_DETACH_CONFLICT, "Global Type policy cannot be detached from tracker.");
                }

                const policiesToBeDeleted: string[] = [];
                if (applicationPolicies.length) {
                    const resiliencePolicyIds = [...resilience.policyIds].map(String);
                    const oldValue = [...resiliencePolicyIds];
                    applicationPolicies.forEach(policyId => {
                        const index = resiliencePolicyIds.indexOf(policyId) as number;
                        if (index !== -1) {
                            resiliencePolicyIds.splice(index, 1);
                            policiesToBeDeleted.push(policyId);
                        }
                    });

                    resilience.policyIds = [...resiliencePolicyIds];
                    await resilience.save();
                    await trackerPolicyUpdatedPublisherWrapper(resilience);

                    // For audit logs
                    const policyIdToNameMap = await getPolicyNameByIds(oldValue);
                    targets.push({
                        type: TargetType.BUSINESSRESILIENCE,
                        details: {
                            id: resilience.id,
                            name: resilience.name
                        }
                    });

                    modifiedProperties.push({
                        target: TargetType.BUSINESSRESILIENCE,
                        propertyName: "policyIds",
                        oldValue: JSON.stringify(oldValue.map((policyId: string) => ({
                            id: String(policyId),
                            value: policyIdToNameMap.get(String(policyId))
                        }))),
                        newValue: JSON.stringify(resilience.policyIds.map((policyId: string) => ({
                            id: String(policyId),
                            value: policyIdToNameMap.get(String(policyId))
                        })))
                    });

                    const users = await User.find({ _id: { $in: [...resilience.members] }, "applicationPolicyIds.policyId": { $in: [...policiesToBeDeleted] } });
                    await Promise.all(users.map(async (user) => {
                        const userApplicationPolicies = [...user.applicationPolicyIds];
                        const oldValue = [...user.applicationPolicyIds];
                        let index = -1;
                        policiesToBeDeleted.every(policyId => {
                            userApplicationPolicies.every((applicationPolicy, policyIndex) => {
                                if (String(applicationPolicy.policyId) === String(policyId)) {
                                    index = policyIndex;
                                    return false;
                                }
                            });
                            if (index !== -1) {
                                userApplicationPolicies.splice(index, 1);
                                return false;
                            }
                        });

                        user.applicationPolicyIds = [...userApplicationPolicies];
                        await user.save();
                        await userPolicyUpdatedPublisherWrapper(user);

                        userTargets.push(user);
                        modifiedApplicationIds.push({
                            oldValue,
                            newValue: user.applicationPolicyIds
                        });
                    }));
                }
            }

            if (inValidPolicyIds.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: inValidPolicyIds,
                        message: "Policies not found."
                    }]
                }], "One or more policies are failed to detach from the specified user.");
            }

            // For audit logs - user target
            userTargets.forEach(user => {
                targets.push({
                    type: TargetType.USER,
                    details: {
                        id: user.id,
                        name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                        email: user.email,
                        azureId: ""
                    }
                });
            });

            // For audit logs - user policyIds
            let userPolicyIds: string[] = [];
            modifiedPolicyIds.forEach(item => {
                userPolicyIds = [...userPolicyIds, ...item.oldValue];
            });

            const policyIdToNameMap = await getPolicyNameByIds(userPolicyIds);
            modifiedPolicyIds.forEach(item => {
                modifiedProperties.push({
                    target: TargetType.USER,
                    propertyName: "policyIds",
                    oldValue: JSON.stringify(item.oldValue.map((policyId: string) => ({
                        id: String(policyId),
                        value: policyIdToNameMap.get(String(policyId))
                    }))),
                    newValue: JSON.stringify(item.newValue.map((policyId: string) => ({
                        id: String(policyId),
                        value: policyIdToNameMap.get(String(policyId))
                    })))
                });
            });

            // For audit logs - user applicationPolicyIds
            const newModifiedProperties = await getUserApplicationModifiedProperties(modifiedApplicationIds);

            res.sendResponse({
                meta: {
                    message: "Policies detached from entity successfully."
                }
            }, {
                targets,
                modifiedProperties: [
                    ...modifiedProperties,
                    ...newModifiedProperties
                ]
            });
        }
        catch (error) {
            console.error("Authorization.DetachPoliciesFromEntity");
            console.error(error);
            next(error);
        }
    });

export { router as detachPoliciesFromEntity };
