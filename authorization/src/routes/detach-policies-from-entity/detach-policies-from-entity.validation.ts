import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const detachPoliciesFromEntityValidation = [
    param("entityId")
        .isMongoId(),

    body("type")
        .exists().bail()
        .custom(value => {
            if (!["User", "Incident", "Resilience"].includes(value)) {
                throw new Error("Policy entity type can be any from this list only: User, Incident, Resilience.");
            }
            else {
                return true;
            }
        }),

    body("policyIds")
        .isArray({ min: 1 })
        .withMessage("No policy found to be detached from the specified entity.")
        .custom((policyIds: string[]) => {
            return policyIds.every(policyId => {
                return isValidMongoObjectId(policyId);
            });
        }).withMessage("This policy ids are invalid."),
];
