import express, { NextFunction, Request, Response } from "express";
import {
    createPagination, currentUser, getPage, hasGlobalAction, InsufficientPrivilages<PERSON><PERSON>r, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PageResponseObj, Pa<PERSON>ationE<PERSON>ty, requireAuth, responseHandler, validateRequest
} from "@moxfive-llc/common";
import { Policy } from "../../../models/policy";
import { SearchIndex } from "../../../enums/search-index";
import { listPoliciesValidation } from "./list-policies.validation";
import { authorizationFields } from "../../../utils/authorization-fields";
import { fetchModuleFilterFieldsBasedOnPermission } from "../../../utils/fetch-module-filter-fields-based-on-permission";
import { User } from "../../../models/users";
import { Incident } from "../../../models/incident";
import { Tracker } from "../../../models/tracker";

const { columnsMapping, filterFieldMapping } = ListAPIHelper.prepareColumnAndFilterFieldMappingsForModule(authorizationFields.listPolicies);
const facetQueryMapping = ListAPIHelper.prepareFacetQueryMappings(authorizationFields.listPolicies);
const sectionWiseFacets = ListAPIHelper.fetchFacetFieldsBySectionsForModule(authorizationFields.listPolicies);

columnsMapping.commonBase.type = "$type";
filterFieldMapping.commonBase.type = "type";
facetQueryMapping.type = { id: "$type", value: "$type" };

const router = express.Router();

router.get("/v2/authorization/policies",
    responseHandler,
    currentUser,
    requireAuth,
    listPoliciesValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check for the required permission
            const hasPermission = await hasGlobalAction(req, "ListPolicies");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Darshan Vesatiya",
            //     id: "633e77ffcd2d5af6a89904f0",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };

            const { search, filter, sort, skipToken, type } = req.query;

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            if (skipToken) {
                // TODO: Change pagination entity
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_POLICIES);
                return res.sendResponse(response ?? {}, {});
            }

            const sectionWiseActions: { name: string, path: any, action?: string }[] = [
                {
                    name: "commonBase",
                    path: authorizationFields.listPolicies.commonBase,
                    action: ""
                },
                {
                    name: "modifiedBy",
                    path: authorizationFields.listPolicies.modifiedBy,
                    action: ""
                },
                {
                    name: "modifiedAt",
                    path: authorizationFields.listPolicies.modifiedAt,
                    action: ""
                }
            ];
            const assignedActionsSet = new Set<string>();

            // Step 5: Fetch the fields by permissions assigned
            const { userColumns, userFacets, userFilters } = ListAPIHelper.prepareUserColumnsFiltersAndFacetsBasedOnPermissions({
                assignedActions: assignedActionsSet,
                sectionWiseActions,
                columnsMapping,
                filterFieldMapping,
                facets: sectionWiseFacets
            });

            // Step 6: Prepare the sort queries
            const sortingMetadata = ListAPIHelper.prepareUserSortQuery({
                path: authorizationFields.listPolicies,
                sort: sort as string, userColumns
            });

            const { sortProcessingStages } = sortingMetadata;
            let { atlasSort, regularSort } = sortingMetadata;

            // If atlas sort is applied on createdBy or updatedBy properties then move them to regular sort
            if (atlasSort && Object.keys(atlasSort).length && (atlasSort["createdBy.value"] || atlasSort["updatedBy.value"])) {
                regularSort = {
                    ...atlasSort,
                    ...regularSort
                };
                atlasSort = undefined;
            }

            // Step 7: Prepare filters based on the filter reqest
            const { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters } = ListAPIHelper.filterParserMust({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters } = ListAPIHelper.filterParserMustNot({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { blankFilters, blankAppliedFilters } = ListAPIHelper.filterParserBlank({ filter: filter as string, fieldMapping: userFilters });

            // Step 8: Prepare applied filters
            const appliedFilters = { ...mustAppliedFilters, ...mustNotAppliedFilters, ...blankAppliedFilters };

            let matchQuery: any = null;
            let facetMatchQuery: any = null;
            if (blankFilters.length || matchFilters.length || matchNotFilters.length) {
                matchQuery = { $and: [...blankFilters, ...matchFilters, ...matchNotFilters] };
            }
            if (blankFilters.length || facetMatchFilters.length || facetMatchNotFilters.length) {
                facetMatchQuery = { $and: [...blankFilters, ...facetMatchFilters, ...facetMatchNotFilters] };
            }

            const commonMust: any[] = [];

            if (search) {
                commonMust.push({
                    autocomplete: {
                        query: search,
                        path: "name",
                        tokenOrder: "sequential"
                    }
                });
            }

            if (type) {
                commonMust.push({
                    in: {
                        path: "type",
                        value: type
                    }
                });
            }

            const optionalQuery = [
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "createdBy",
                        "foreignField": "_id",
                        "as": "createdBy",
                        "pipeline": [
                            {
                                "$project": {
                                    "id": "$_id",
                                    "value": "$name",
                                    "_id": 0
                                }
                            }
                        ]
                    }
                }, {
                    "$lookup": {
                        "from": "users",
                        "localField": "updatedBy",
                        "foreignField": "_id",
                        "as": "updatedBy",
                        "pipeline": [
                            {
                                "$project": {
                                    "id": "$_id",
                                    "value": "$name",
                                    "_id": 0
                                }
                            }
                        ]
                    }
                }, {
                    "$addFields": {
                        "createdBy": {
                            "$first": "$createdBy"
                        },
                        "updatedBy": {
                            "$first": "$updatedBy"
                        }
                    }
                }
            ];

            // Step 9: Prepare Data query using fields, filters and sortings
            const dataQuery = ListAPIHelper.prepareDataQuery({
                collection: Policy,
                searchIndex: SearchIndex.POLICY_DEFAULT,
                must: [...commonMust, ...mustFilters],
                mustNot: mustNotFilters,
                matchQuery,
                projection: userColumns,
                atlasSort,
                regularSort,
                sortProcessingStages,
                optionalQuery
            });
            // Step 10: Prepare facet queries to get counts for all the flexible type fields
            const facetsQuery = ListAPIHelper.prepareFacetQuery({
                collection: Policy,
                searchIndex: SearchIndex.POLICY_DEFAULT,
                must: [...commonMust, ...mustFiltersForFacets],
                mustNot: mustNotFiltersForFacets,
                facetMatchQuery,
                facetFilter,
                userFacets,
                facetQueryMapping,
                optionalQuery
            });
            // Step 11: Get all the values for flexible type fields
            const fieldPromise = fetchModuleFilterFieldsBasedOnPermission({
                sections: sectionWiseActions,
                assignedActions: assignedActionsSet
            });

            const [data, facets, fields] = await Promise.all([dataQuery, facetsQuery, fieldPromise]);

            // Step 13: Prepare the flexible field value counts and quick filters
            ListAPIHelper.prepareQuickFilters({ facetsWithFields: facets[0], fields, appliedFilters });
            // Step 14: For applied filters get the selected values
            ListAPIHelper.prepareAppliedFiltersWithValues({ appliedFilters, fields });

            // Add AttachedEntities property for every policy
            const policyIds = data.map(i => i.id);
            const usersHavingPoliciesAttached = await User.find({ policyIds: { $in: policyIds } }, { policyIds: 1 }).lean().exec();
            const incidentsHavingPoliciesAttached = await Incident.find({ policyIds: { $in: policyIds } }, { policyIds: 1 }).lean().exec();
            const resiliencesHavingPoliciesAttached = await Tracker.find({ policyIds: { $in: policyIds } }, { policyIds: 1 }).lean().exec();

            const policiesAttachedCount: { [key: string]: number } = {};

            // Loop through usersHavingPoliciesAttached
            usersHavingPoliciesAttached.forEach(user => {
                user.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            // Loop through incidentsHavingPoliciesAttached
            incidentsHavingPoliciesAttached.forEach(incident => {
                incident.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            // Loop through resiliencesHavingPoliciesAttached
            resiliencesHavingPoliciesAttached.forEach(resilience => {
                resilience.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            const processedDocs = data.map(record => {
                record["attachedEntities"] = policiesAttachedCount[record.id] ?? 0;
                return record;
            });

            // Step 15: Create pagination and send the response
            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_POLICIES,
                fields,
                appliedFilters,
                processedDocs);

            if (response) {
                return res.sendResponse(response, {});
            }
            return res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Incident.ListPoliciesV2");
            console.error(error);
            next(error);
        }
    });

export { router as listPoliciesV2Router };
