import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, validateRequest, InsufficientPrivilagesError, ResourceNotFoundError, NotFoundCode, responseHandler } from "@moxfive-llc/common";
import { Action } from "../../models/action";
import { AccessControl } from "../../models/access-control";
import { getActionsValidation } from "./get-actions.validation";
import { Service } from "../../models/service";
import { insertWhiteSpaces } from "../../utils";
import { Category } from "@moxfive-llc/common/build/models/category.model";

const router = express.Router();

router.get(
    "/v1/authorization/policies/services/:serviceId/actions",
    responseHandler,
    currentUser,
    requireAuth,
    getActionsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { serviceId } = req.params;

            const hasActionToReadActions = await hasGlobalAction(req, "ListServiceActions");
            if (!hasActionToReadActions) {
                throw new InsufficientPrivilagesError();
            }

            const service = await Service.findById(serviceId);
            if(!service) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_SERVICE_NOT_FOUND, "Policy service not found.");
            }

            const actions = await Action.find({ serviceId, isEnabled: true, requireAuthorization: true }, { ruleIds: 0, serviceId: 0, requireAuthorization: 0 });
            const accessControlIds = actions.map(action => action.accessControlId).filter(Boolean);
            const accessControls = await AccessControl.find({ _id: accessControlIds }).sort({ name: 1 });

            // Fetch categories and make action-category map
            const categories = await Category.find({ serviceId });
            const statusUpdatesCategoryActions = categories.find(cat => cat.name === "Project Status Updates")?.actionIds.map(String) || [];
            const actionCategoryMap: Map<string, string> = new Map();
            categories.forEach(category => {
                category.actionIds.forEach(action => {
                    actionCategoryMap.set(String(action), category.name);
                });
            });

            const responseData = accessControls.map(accessControl => ({
                accessControl: accessControl.name,
                description: accessControl.description,
                actions: actions
                    .filter(action => String(action.accessControlId) === accessControl.id)
                    .map(action => ({
                        id: action.id,
                        name: action.name,
                        displayName: statusUpdatesCategoryActions.includes(String(action.id))
                            ? `(Version 2.0) ${insertWhiteSpaces(action.name)}`
                            : insertWhiteSpaces(action.name),
                        description: action.description,
                        moxfiveExclusive: action.moxfiveExclusive,
                        category: actionCategoryMap.get(action.id) || ""
                    }))
                    .sort((actionA, actionB) => actionA.name.localeCompare(actionB.name))
            }));
            res.sendResponse(responseData, {});
        }
        catch (error) {
            console.error("Authorization.GetActions");
            console.error(error);
            next(error);
        }

    }
);

export { router as getActions };
