import { param, query } from "express-validator";

export const getPoliciesForEntityValidation = [
    param("entityId")
        .isMongoId(),

    query("type")
        .exists().bail()
        .custom(value => {
            if (!["User", "Incident", "Resilience"].includes(value)) {
                throw new Error("Policy entity type can be any from this list only: User, Incident, Resilience.");
            }
            else {
                return true;
            }
        }),

    query("search")
        .optional()
        .isString().trim().isLength({ min: 1 }).withMessage("Search term must not be blank."),
];
