/* eslint-disable max-statements */
import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, validateRequest, ResourceNotFoundError, NotFoundCode, InsufficientPrivilagesError, responseHandler } from "@moxfive-llc/common";

import { Policy, PolicyDoc } from "../../models/policy";
import { User } from "../../models/users";
import { getPoliciesForEntityValidation } from "./get-policies-for-entity.validation";
import { fetchDefaultSortObject, escapeRegExp, checkMoxfiveUserOrSuperAdminPermissions } from "../../utils";
import { Incident } from "../../models/incident";
import { Tracker } from "../../models/tracker";
import { authorizationFields } from "../../utils/authorization-fields";
import { SortQuery } from "../../interfaces";
import {
    fetchSortFromMultipleSectionsBasedOnPermission
} from "../../utils/fetch-sort-from-multiple-sections-based-on-permission";

const router = express.Router();

router.get(
    "/v1/authorization/entities/:entityId/policies",
    responseHandler,
    currentUser,
    requireAuth,
    getPoliciesForEntityValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const isMoxfiveUser = process.env.MOXFIVE_ID === req.currentUser?.organizationId;

            const hasActionToGetEntityPolicies = await hasGlobalAction(req, "ListPoliciesForPlatformEntity");
            if (!hasActionToGetEntityPolicies) {
                throw new InsufficientPrivilagesError();
            }

            const { entityId } = req.params;
            const { type, search, sort } = req.query;
            let policyIds: string[] = [];

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            const searchQuery: { name?: { $regex: string, $options: string } } = {};
            if (search) {
                const searchTerm = decodeURIComponent(search as string);
                searchQuery.name = { $regex: `${escapeRegExp(searchTerm)}`, $options: "i" };
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authorizationFields.policies,
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);

            // If sort is provided then prepare sort query
            if (sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });
                Object.assign(sortQuery, sortObj);
            }

            if (type === "User") {
                const userDetails = await User.findById(entityId, { policyIds: 1, applicationPolicyIds: 1, organizationId: 1 });
                if (!userDetails) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                // Case: If user is moxfive user or super admin it will consider all the user policy regardng entity
                if (isMoxfiveUserOrSuperAdmin) {
                    policyIds = [...userDetails.policyIds, ...userDetails.applicationPolicyIds.map(policy => policy.policyId)];
                }
                else {
                    // Else if not MF user then check if userDetail regarding entity id is have same org as current user
                    if (String(userDetails.organizationId) === req.currentUser?.organizationId) {
                        policyIds = [...userDetails.policyIds, ...userDetails.applicationPolicyIds.map(policy => policy.policyId)];
                    }
                }
                let policyDetails: PolicyDoc[] = [];

                if (Object.keys(sortQuery).length) {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, type: 1 }).sort(sortQuery);
                }
                else {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, type: 1 });
                }

                if (!isMoxfiveUser) {
                    return res.sendResponse(policyDetails.map(policy => ({
                        id: policy._id,
                        name: policy.name
                    })), {});
                }

                // Send the response
                res.sendResponse(policyDetails.map(policy => ({
                    id: policy._id,
                    name: policy.name,
                    type: policy.type
                })), {});
            }
            else if (type === "Incident") {
                const incidentDetails = await Incident.findById(entityId, { policyIds: 1, members: 1 });
                if (!incidentDetails) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                // Case: If user is MF or super admin if not then check if its member of incident then only we will consider policys to be listed
                if (isMoxfiveUserOrSuperAdmin) {
                    policyIds = policyIds.concat([...incidentDetails.policyIds]);
                }
                else {
                    const members = incidentDetails.members.map(userId => String(userId));
                    if (members.includes(req.currentUser?.id ?? "")) {
                        policyIds = policyIds.concat([...incidentDetails.policyIds]);
                    }
                }

                let policyDetails: PolicyDoc[] = [];
                if (Object.keys(sortQuery).length) {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, isEnabled: 1 }).sort(sortQuery);
                }
                else {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, isEnabled: 1 });
                }

                const policiesIds = policyDetails.map(policy => String(policy._id));
                const policyUsersMap = new Map();
                const policyUsers = await User.find({
                    $and: [
                        { "applicationPolicyIds.applicationId": entityId },
                        { "applicationPolicyIds.policyId": { $in: [...policiesIds] } }
                    ]
                }, { applicationPolicyIds: 1 }).lean().exec();
                policiesIds.forEach(policyId => {
                    const usersCount = policyUsers.filter(user => !!user.applicationPolicyIds.find(policy => String(policy.policyId) === policyId)).length;
                    policyUsersMap.set(policyId, usersCount);
                });

                res.sendResponse(policyDetails.map(policy => ({
                    id: policy._id,
                    name: policy.name,
                    users: policyUsersMap.get(String(policy._id)) || 0,
                    status: policy.isEnabled
                })), {});
            }
            else if (type === "Resilience") {
                const resilienceDetails = await Tracker.findById(entityId, { policyIds: 1, members: 1 });
                if (!resilienceDetails) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                // Case: Check if user is MF or Super Admin user if not then check if user is member of tracker then only consider policy listing for user
                if(isMoxfiveUserOrSuperAdmin) {
                    policyIds = policyIds.concat([...resilienceDetails.policyIds]);
                }
                else {
                    const members = resilienceDetails.members.map(userId => String(userId));
                    if (members.includes(req.currentUser?.id ?? "")) {
                        policyIds = policyIds.concat([...resilienceDetails.policyIds]);
                    }
                }

                let policyDetails: PolicyDoc[] = [];

                if (Object.keys(sortQuery).length) {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, isEnabled: 1 }).sort(sortQuery);
                }
                else {
                    policyDetails = await Policy.find({ _id: { $in: [...policyIds] }, ...searchQuery }, { name: 1, isEnabled: 1 });
                }

                const policiesIds = policyDetails.map(policy => String(policy._id));
                const policyUsersMap = new Map();
                const policyUsers = await User.find({
                    $and: [
                        { "applicationPolicyIds.applicationId": entityId },
                        { "applicationPolicyIds.policyId": { $in: [...policiesIds] } }
                    ]
                }, { applicationPolicyIds: 1 }).lean().exec();
                policiesIds.forEach(policyId => {
                    const usersCount = policyUsers.filter(user => !!user.applicationPolicyIds.find(policy => String(policy.policyId) === policyId)).length;
                    policyUsersMap.set(policyId, usersCount);
                });

                res.sendResponse(policyDetails.map(policy => ({
                    id: policy._id,
                    name: policy.name,
                    users: policyUsersMap.get(String(policy._id)) || 0,
                    status: policy.isEnabled
                })), {});
            }
        }
        catch (error) {
            console.error("Authorization.GetPoliciesForEntity");
            console.error(error);
            next(error);
        }
    }
);

export { router as getPoliciesForEntity };
