import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, requireAuth, validateRequest, hasGlobalAction, InsufficientPrivilagesError, ResourceNotFoundError,
    NotFoundCode, responseHandler, TargetType
} from "@moxfive-llc/common";

import { Policy } from "../../models/policy";
import { deletePolicyValidation } from "./delete-policy.validations";
import { User } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { PolicyDeletedPublisher } from "../../events/publishers/policy-deleted-publisher";
import { natsWrapper } from "../../nats-wrapper";
import { Incident } from "../../models/incident";
import { incidentPolicyUpdatedPublisherWrapper } from "../../utils/incident-policy-updated-publisher-wrapper";
import { Tracker } from "../../models/tracker";
import { trackerPolicyUpdatedPublisherWrapper } from "../../utils/tracker-policy-updated-publisher-wrapper";
import { getUserName } from "../../utils";

const routes = express.Router();

routes.delete(
    "/v1/authorization/policies/:policyId",
    responseHandler,
    currentUser,
    requireAuth,
    deletePolicyValidation,
    validateRequest,
    async function (req: Request, res: Response, next: NextFunction) {
        try {
            const hasActionToDeletePolicy = await hasGlobalAction(req, "DeletePolicy");
            if (!hasActionToDeletePolicy) {
                throw new InsufficientPrivilagesError();
            }

            const { policyId } = req.params;
            const targets: any = [];

            const isPolicyExist = await Policy.findById(policyId);
            if (!isPolicyExist) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const policyType = isPolicyExist.type;
            await Policy.deleteOne({ _id: policyId });

            // For audit logs
            targets.push({
                type: TargetType.POLICY,
                details: {
                    id: policyId,
                    name: isPolicyExist.name
                }
            });

            // Remove policy from attached entities also
            if (policyType === "Global") {
                const users = await User.find({ policyIds: policyId });
                await Promise.all(users.map(async (user) => {
                    const index = user.policyIds.map(String).indexOf(policyId);
                    user.policyIds.splice(index, 1);
                    await user.save();
                    await userPolicyUpdatedPublisherWrapper(user);
                }));

                // For audit logs
                users.forEach(user => {
                    targets.push({
                        type: TargetType.USER,
                        details: {
                            id: user.id,
                            name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                            email: user.email,
                            azureId: ""
                        }
                    });
                });
            }
            else {
                // Remove applicaiton policy from attched incident
                const incidents = await Incident.find({ policyIds: policyId });
                await Promise.all(incidents.map(async (incident) => {
                    const index = incident.policyIds.map(String).indexOf(policyId);
                    incident.policyIds.splice(index, 1);
                    await incident.save();
                    await incidentPolicyUpdatedPublisherWrapper(incident);
                }));

                incidents.forEach(incident => {
                    targets.push({
                        type: TargetType.INCIDENT,
                        details: {
                            id: incident.id,
                            name: incident.name
                        }
                    });
                });

                // Remove application policy from attched resilience
                const resiliences = await Tracker.find({ policyIds: policyId });
                await Promise.all(resiliences.map(async (resilience) => {
                    const index = resilience.policyIds.map(String).indexOf(policyId);
                    resilience.policyIds.splice(index, 1);
                    await resilience.save();
                    await trackerPolicyUpdatedPublisherWrapper(resilience);
                }));

                resiliences.forEach(resilience => {
                    targets.push({
                        type: TargetType.BUSINESSRESILIENCE,
                        details: {
                            id: resilience.id,
                            name: resilience.name
                        }
                    });
                });

                // Remove application policy from attched user
                const users = await User.find({ "applicationPolicyIds.policyId": policyId });
                await Promise.all(users.map(async (user) => {
                    const userApplicationPolicies = [...user.applicationPolicyIds];
                    const userApplicationPolicyIds = userApplicationPolicies.map(policy => String(policy.policyId));
                    const policyIndex = userApplicationPolicyIds.indexOf(policyId) as number;

                    if (policyIndex !== -1) {
                        userApplicationPolicies.splice(policyIndex, 1);
                        user.applicationPolicyIds = [...userApplicationPolicies];
                        await user.save();
                        await userPolicyUpdatedPublisherWrapper(user);
                    }
                }));

                // For audit logs
                users.forEach(user => {
                    targets.push({
                        type: TargetType.USER,
                        details: {
                            id: user.id,
                            name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                            email: user.email,
                            azureId: ""
                        }
                    });
                });
            }

            // Publish policy deleted event
            const data = {
                id: policyId,
            };

            await new PolicyDeletedPublisher(natsWrapper.client).publish(data);

            res.sendResponse({
                meta: {
                    message: "Policy deleted successfully."
                }
            }, {
                targets
            });
        }
        catch (error) {
            console.error("Authorization.DeletePolicy");
            console.error(error);
            next(error);
        }
    }
);

export { routes as deletePolicy };
