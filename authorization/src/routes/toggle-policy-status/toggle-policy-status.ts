import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, requireAuth, validateRequest, hasGlobalAction, InsufficientPrivilagesError, ResourceNotFoundError,
    NotFoundCode, SucceededPartially, responseHandler, TargetType
} from "@moxfive-llc/common";

import { Policy, PolicyDoc } from "../../models/policy";
import { policyUpdatedPublisherWrapper } from "../../utils/policy-updated-publisher-wrapper";
import { togglePolicyStatusValidation } from "./toggle-policy-status.validation";

const routes = express.Router();

routes.put(
    "/v1/authorization/policies/toggle",
    responseHandler,
    currentUser,
    requireAuth,
    togglePolicyStatusValidation,
    validateRequest,
    async function (req: Request, res: Response, next: NextFunction) {
        try {
            const hasActionToUpdatePolicy = await hasGlobalAction(req, "UpdatePoliciesStatus");
            if (!hasActionToUpdatePolicy) {
                throw new InsufficientPrivilagesError();
            }

            const { isEnabled, policies: policiesIds } = req.body;
            // Step 1: Find organizations and if some organization not found then throw error
            const policies = await Policy.find({ _id: { $in: policiesIds } });
            if (!policies || (policies && !policies.length)) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policies not found.");
            }

            const isSuperAdminUpdate = policies.find(policy => policy.name === "Super Admin");
            if (isSuperAdminUpdate) {
                throw new InsufficientPrivilagesError();
            }

            const validPolicies: PolicyDoc[] = [];
            const inValidPolicyIds: string[] = [];
            const policyIds = policies.map(policy => String(policy.id));

            policiesIds.forEach((policyId: string) => {
                if (policyIds.includes(policyId)) {
                    validPolicies.push(policies.find(policy => policy.id === policyId)!);
                }
                else {
                    inValidPolicyIds.push(policyId);
                }
            });

            // For audit logs
            const targets: any = [];
            const modifiedProperties: any = [];

            // Step 2: If organization status is not same as provided status then update status
            await Promise.all(validPolicies.map(async policy => {
                if (policy.isEnabled !== isEnabled) {
                    policy.isEnabled = isEnabled;
                    policy.updatedBy = req.currentUser ? req.currentUser.id : "";
                    await policy.save();
                    await policyUpdatedPublisherWrapper(policy);

                    targets.push({
                        type: TargetType.POLICY,
                        details: {
                            id: policy.id,
                            name: policy.name
                        }
                    });

                    modifiedProperties.push({
                        target: TargetType.POLICY,
                        propertyName: "isEnabled",
                        oldValue: !policy.isEnabled,
                        newValue: policy.isEnabled
                    });
                }
            }));

            if (inValidPolicyIds.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: inValidPolicyIds,
                        message: "Policies not found."
                    }]
                }], "One or more policies status failed to update.");
            }

            res.sendResponse({
                meta: {
                    message: "Policies status updated successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.TogglePolicyStatus");
            console.error(error);
            next(error);
        }
    }
);

export { routes as togglePolicyStatus };
