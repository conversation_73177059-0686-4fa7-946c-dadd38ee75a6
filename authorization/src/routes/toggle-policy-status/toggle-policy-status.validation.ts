import { body } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const togglePolicyStatusValidation = [
    body("policies")
        .isArray({ min: 1, max: 20 }).withMessage("Policies not found. At a time max 20 policies can be updated.").bail()
        .custom((policies: string[]) => {
            return policies.every(policy => {
                return isValidMongoObjectId(policy);
            });
        }).withMessage("This policy ids are invalid."),

    body("isEnabled")
        .exists().bail()
        .isBoolean({ loose: false })
        .toBoolean()
        .withMessage("isEnabled must be boolean"),
];
