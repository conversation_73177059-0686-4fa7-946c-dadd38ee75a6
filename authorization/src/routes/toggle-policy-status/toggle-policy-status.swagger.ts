// /**
//  * @swagger
//  * /v1/authorization/roles/{roleId}/status:
//  *   put:
//  *     summary: Toggle Role.
//  *     description: toggle role.
//  *     tags:
//  *       - Roles
//  *     parameters:
//  *       - in: path
//  *         name: roleId
//  *         required: true
//  *         description: role id for getting data.
//  *         schema:
//  *           type : string
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *             $ref: "#/components/schemas/ToggleRole"
//  *     responses:
//  *       200: #response type
//  *         description: OK
//  *         content:
//  *           application/json:
//  *               example:
//  *                  {
//  *                  "id": "623c684a1f28c5b8defddb8a",
//  *                  "meta": {
//  *                      "message": "Role successfully toggled."
//  *                  }
//  *                  }
//  *       401:
//  *         description: Not authorized
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Not authorized"} ] }
//  *       400:
//  *         description: Bad Request
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Role is already Enabled" } ] }
//  *       204:
//  *         description: No Content (Already same status)
//  */
