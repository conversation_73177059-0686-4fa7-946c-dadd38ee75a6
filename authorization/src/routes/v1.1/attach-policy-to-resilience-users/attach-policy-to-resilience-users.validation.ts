import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../../utils";

export const attachPolicyToResilienceUsersValidations = [
    param("trackerId")
        .isMongoId(),

    body("userIds")
        .isArray({ min: 1, max: 100 })
        .withMessage("User must be valid array with min 1 element.").bail()
        .custom((userIds: string[]) => {
            return userIds.every(userId => {
                return isValidMongoObjectId(userId);
            });
        }).withMessage("User must be valid array with 1 element."),

    body("policies")
        .isArray({ min: 1, max: 100 })
        .withMessage("Policies must be valid array with min 1 element.").bail()
        .custom((policies: string[]) => {
            return policies.every(policy => {
                return isValidMongoObjectId(policy);
            });
        }).withMessage("Policies must be valid array with min 1 element.")
];
