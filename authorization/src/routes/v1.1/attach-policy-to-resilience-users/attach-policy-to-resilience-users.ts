import express, { Request, Response, NextFunction } from "express";
import {
    currentUser,
    hasGlobalAction,
    requireAuth,
    validateRequest,
    InsufficientPrivilagesError,
    ResourceNotFoundError,
    NotFoundCode,
    SucceededPartially,
    BasicResourceValueUnacceptableConflictError,
    ConflictErrorCodes,
    responseHandler,
    TargetType,
    NonMoxfiveUserWithMfExclusiveTypesEnum
} from "@moxfive-llc/common";
import { Policy } from "../../../models/policy";
import { User, UserDoc } from "../../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../../utils/user-policy-updated-publisher-wrapper";
import { attachPolicyToResilienceUsersValidations } from "./attach-policy-to-resilience-users.validation";
import { Tracker } from "../../../models/tracker";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { getUserName } from "../../../utils";
import { getUserApplicationModifiedProperties } from "../../../utils/get-user-application-modified-properties";
import { Action } from "../../../models/action";
import { isMOXFIVEUser } from "../../../utils/is-moxfive-user";
import { getUsersNameInitialsByIds } from "../../../utils/get-users-name-initials-by-ids";
import { Organization } from "../../../models/organizations";
import { queueGroupName } from "../../../events/queue-group-name";
import {
    NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper
} from "../../../utils/non-mf-user-with-mf-exclusive-permission-notification-publisher-wrapper";

const router = express.Router();

router.put("/v1.1/authorization/policies/trackers/:trackerId/users/attach",
    responseHandler,
    currentUser,
    requireAuth,
    attachPolicyToResilienceUsersValidations,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { trackerId } = req.params;
            const { userIds, policies }: {userIds: string[], policies: string[]} = req.body;

            const hasActionToAttachPolicyToResilienceUsers = await hasGlobalAction(req, "AttachPolicyToResilienceUsers");
            if (!hasActionToAttachPolicyToResilienceUsers) {
                throw new InsufficientPrivilagesError();
            }

            const policiesDetails = await Policy.find({ _id: { $in: policies } }).lean().exec();
            if(policies.length !== policiesDetails.length) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const resilience = await Tracker.findById(trackerId, { _id: 1, policyIds: 1 }).lean().exec();
            if (!resilience) {
                throw new ResourceNotFoundError(NotFoundCode.TRACKER_NOT_FOUND, "Tracker not found.");
            }

            const users = await User.find({ _id: { $in: [...userIds] } });
            if (users && !users.length) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            const isAnyGlobalPolicy = policiesDetails.some(policy => policy.type === "Global");
            if (isAnyGlobalPolicy) {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_ATTACH_CONFLICT, "Global Type policy cannot attached to users.");
            }

            const resiliencePoliciesSet = new Set(resilience.policyIds.map(String));
            const isEveryPolicyAttachedToResilience = policies.map(String).every(policy => resiliencePoliciesSet.has(policy));
            if (!isEveryPolicyAttachedToResilience) {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.POLICY_NOT_ATTACHED_TO_RESILIENCE, "Policy must be attached to resilience.");
            }

            // Make policyIdToNameMap
            const policyIdToNameMap = new Map();
            policiesDetails.forEach(policy => policyIdToNameMap.set(String(policy._id), policy.name));

            // Make actions map
            const actions = await Action.find({}).select("moxfiveExclusive").lean().exec();
            const actionsMap: Map<string, boolean> = new Map();
            actions.forEach(action => actionsMap.set(String(action._id), action.moxfiveExclusive));

            // Make map of policy as moxfive exclusive permission or not
            const policiesMFExclusivePermissionsSet: Set<string> = new Set();
            policiesDetails.forEach(policy => {
                // Policy should not be super admin policy and policy should have moxfive exclusive permissions
                const isMFExclusivePermissionAdded = String(policy._id) !== process.env.SUPER_ADMIN_POLICY_ID && policy.actionIds.some(action => actionsMap.get(String(action)));
                if(isMFExclusivePermissionAdded) {
                    policiesMFExclusivePermissionsSet.add(String(policy._id));
                }
            });

            const validUsers: UserDoc[] = [];
            const invalidUserIds: string[] = [];
            const usersIds = users.map(user => String(user.id));
            const targets: any = [];
            const modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[] = [];

            userIds.forEach((userId: string) => {
                if (usersIds.includes(userId)) {
                    const userDetail = users.find(user => user.id === userId);
                    if (userDetail) {
                        validUsers.push(userDetail);
                    }
                }
                else {
                    invalidUserIds.push(userId);
                }
            });

            await Promise.all(validUsers.map(async (user) => {
                let isAnyPolicyAssigned = false;
                const oldValue = [...user.applicationPolicyIds];

                // Prepare set of assigned policies of that tracker
                const assignedApplicationPoliciesSet = new Set(oldValue.filter(appPolicy => String(appPolicy.applicationId) === trackerId).map(policy => String(policy.policyId)));

                const shouldSendMoxfiveExclusiveNotification = policiesMFExclusivePermissionsSet.size && !isMOXFIVEUser({
                    req,
                    organizationId: String(user.organizationId),
                    throwError: false,
                });

                // Loop through all policies and if policy is not already assigned then assign it
                policies.forEach(policy => {
                    if(!assignedApplicationPoliciesSet.has(policy)) {
                        isAnyPolicyAssigned = true;

                        user.applicationPolicyIds.push({
                            applicationId: trackerId,
                            applicationType: "Resilience",
                            policyId: policy
                        });
                    }
                });

                if(isAnyPolicyAssigned) {
                    await user.save();
                    await userPolicyUpdatedPublisherWrapper(user);

                    // If we need to send moxfive exclusive notification then continue
                    if(shouldSendMoxfiveExclusiveNotification) {
                        // Fetch user info of entity and logged-in user
                        const userIds = [String(user._id)];
                        if (req.currentUser?.id) {
                            userIds.push(req.currentUser.id);
                        }
                        const usersMap = await getUsersNameInitialsByIds(userIds);
                        const entity = usersMap.get(String(user._id));

                        if (entity) {
                            // Fetch entity organization
                            const organization = await Organization.findById(user.organizationId).select("name").lean().exec();

                            // Fetch policy names that has MF Exclusive permission assigned
                            const policiesNames:string[] = [];
                            policiesMFExclusivePermissionsSet.forEach(policy => {
                                const name = policyIdToNameMap.get(String(policy));
                                if(name) {
                                    policiesNames.push(name);
                                }
                            });

                            // Prepare data and emit event
                            const data = {
                                recipientUserIds: [String(user._id)],
                                serviceName: queueGroupName,
                                assignedToUsers: [{
                                    name: entity.name,
                                    organization: organization?.name || "",
                                    email: user.email
                                }],
                                assignedByName: usersMap.get(req.currentUser?.id)?.name || "",
                                assignedByInitials: usersMap.get(req.currentUser?.id)?.initials || "",
                                policies: policiesNames,
                                type: NonMoxfiveUserWithMfExclusiveTypesEnum.SINGLE_USER,
                            };

                            await NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper(data);
                        }
                    }

                    targets.push({
                        type: TargetType.USER,
                        details: {
                            id: user.id,
                            name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                            email: user.email,
                            azureId: ""
                        }
                    });
                    modifiedApplicationIds.push({
                        oldValue,
                        newValue: user.applicationPolicyIds
                    });
                }
            }));

            const errors: { attributes: string[], message: string }[] = [];
            if (invalidUserIds.length) {
                errors.push({
                    attributes: invalidUserIds,
                    message: "Users not found."
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users are failed to attached with the specified policy.");
            }

            const modifiedProperties = await getUserApplicationModifiedProperties(modifiedApplicationIds);

            res.sendResponse({
                meta: {
                    message: "Policies attached successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.AttachPolicyToResilienceUsers v1.1");
            console.error(error);
            next(error);
        }
    });

export { router as attachPolicyToResilienceUsersVersionOnePointOne };
