import express, { NextFunction, Request, Response } from "express";
import {
    currentUser, requireAuth, validateRequest, hasGlobalAction, InsufficientPrivilagesError,
    ResourceAlreadyExistBadRequestError, InvalidResourceIdBadRequestError, ActionsConflictsError, responseHandler, TargetType
} from "@moxfive-llc/common";

import { Policy } from "../../models/policy";
import { PolicyCreatedPublisher } from "../../events/publishers/policy-created-publisher";
import { natsWrapper } from "../../nats-wrapper";
import { createPolicyValidation } from "./create-policy.validation";
import { escapeRegExp, insertWhiteSpaces } from "../../utils";
import { Action } from "../../models/action";
import { Service } from "../../models/service";

const router = express.Router();

router.post(
    "/v1/authorization/policies",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    createPolicyValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToCreatePolicy = await hasGlobalAction(req, "CreatePolicy", true);
            if (!hasActionToCreatePolicy) {
                throw new InsufficientPrivilagesError();
            }

            const { name, description, isEnabled, actionIds, type } = req.body;

            // Check policy
            const nameRegex = { $regex: `^${escapeRegExp(name)}$`, $options: "i" };
            const policyDetails = await Policy.findOne({ name: nameRegex }).lean().exec();
            if (policyDetails) {
                throw new ResourceAlreadyExistBadRequestError("name", name, "Policy name already in use.");
            }

            const fetchedPermissions = await Action.find({ _id: { $in: actionIds }, requireAuthorization: true  }, { _id: 1, serviceId: 1, name: 1 }).lean().exec();
            if (fetchedPermissions.length !== actionIds.length) {
                throw new InvalidResourceIdBadRequestError([{ name: "actionIds", value: actionIds, message: "This action ids are invalid." }]);
            }

            if (type === "Application") {
                const serviceIds = new Set();
                fetchedPermissions.forEach((action) => {
                    serviceIds.add(action.serviceId);
                });

                const serviceDetails = await Service.find({ _id: { $in: [...serviceIds] } }, { eligiblePolicyTypes: 1 });
                const globalService = serviceDetails.filter(service => !service.eligiblePolicyTypes.includes("Application")).map(service => String(service.id));
                if (globalService && globalService.length) {
                    const invalidActions = fetchedPermissions.filter(action => globalService.includes(String(action.serviceId)))
                        .map(action => insertWhiteSpaces(action.name)) as string[];

                    throw new ActionsConflictsError(invalidActions);
                }
            }

            const policy = Policy.build({
                name: name,
                description: description,
                isEnabled: isEnabled,
                actionIds: actionIds,
                type: type,
                createdBy: req.currentUser ? req.currentUser.id : "",
                updatedBy: req.currentUser ? req.currentUser.id : ""
            });
            await policy.save();

            const data = {
                id: String(policy.id),
                name: policy.name,
                version: policy.version,
                isEnabled: policy.isEnabled,
                type: policy.type,
                actionIds: (policy.actionIds.length && policy.actionIds.map(String) as string[]) || [],
                createdAt: policy.createdAt
            };

            await new PolicyCreatedPublisher(natsWrapper.client).publish(data);

            const actionIdToNameMap = new Map();
            fetchedPermissions.forEach(action => actionIdToNameMap.set(String(action._id), action.name));

            res.sendResponse({
                id: policy._id,
                meta: {
                    message: "Policy successfully created."
                }
            }, {
                targets: [{
                    type: TargetType.POLICY,
                    details: {
                        id: policy.id,
                        name: policy.name
                    }
                }],
                modifiedProperties: [{
                    target: TargetType.POLICY,
                    propertyName: "name",
                    oldValue: "",
                    newValue: name
                }, {
                    target: TargetType.POLICY,
                    propertyName: "description",
                    oldValue: "",
                    newValue: description
                }, {
                    target: TargetType.POLICY,
                    propertyName: "isEnabled",
                    oldValue: "",
                    newValue: isEnabled
                }, {
                    target: TargetType.POLICY,
                    propertyName: "type",
                    oldValue: "",
                    newValue: type
                }, {
                    target: TargetType.POLICY,
                    propertyName: "actionIds",
                    oldValue: "",
                    newValue: JSON.stringify(actionIds.map((action: string) => ({
                        id: action,
                        value: insertWhiteSpaces(actionIdToNameMap.get(String(action)) ?? "")
                    })))
                }]
            });
        }
        catch (error) {
            console.error("Authorization.CreatePolicy");
            console.error(error);
            next(error);
        }
    }
);

export { router as createPolicy };
