import { body } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const createPolicyValidation = [
    body("name")
        .exists().bail()
        .isString().trim().blacklist("<>").isLength({ min: 1, max: 100 }).withMessage("Name must be max 100 characters long."),

    body("description")
        .exists().bail()
        .isString().trim().blacklist("<>").isLength({ min: 1, max: 2000 }).withMessage("Description must be max 2000 characters long."),

    body("type")
        .exists().bail()
        .custom((value: string) => {
            if (!(["Global", "Application"].includes(value))) {
                throw new Error("Policy type can be any from this list only: Global, Application.");
            }
            else {
                return true;
            }
        }),

    body("isEnabled")
        .isBoolean({ loose: false })
        .toBoolean()
        .withMessage("isEnabled must be boolean."),

    body("actionIds")
        .isArray({ min: 1 })
        .withMessage("Minimum 1 action is required to create a policy.").bail()
        .custom((actionIds: string[]) => {
            return actionIds.every(actionId => {
                return isValidMongoObjectId(actionId);
            });
        }).withMessage("These action ids are invalid.")
];
