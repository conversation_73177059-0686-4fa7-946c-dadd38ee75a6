// /**
//  * @swagger
//  * /v1/authorization/roles?page={page}&limit={limit}:
//  *   get:
//  *     name: Get Roles
//  *     summary: Get roles list.
//  *     description: this will send list of roles of system and this list is only get by the moxfive user.
//  *     tags:
//  *       - Roles
//  *     parameters:
//  *       - in: query
//  *         name: page
//  *         required: true
//  *         description: query.
//  *         schema:
//  *           type : number
//  *           format: int64
//  *           minimum: 1
//  *       - in: query
//  *         name: limit
//  *         required: true
//  *         description: limit.
//  *         schema:
//  *           type : number
//  *           format: int64
//  *           minimum: 1
//  *     responses:
//  *       200: #response type
//  *         description: OK
//  *         content:
//  *           application/json:
//  *               example:
//  *                  {
//  *                  "totalRows": 6,
//  *                  "data": [
//  *                      {
//  *                      "id": "6230433a540c054da1d312f8",
//  *                      "roleName": "Privacy Counsel Executive Lead",
//  *                      "roleDescription": "",
//  *                      "isEnabled": true,
//  *                      "organizationType": "Privacy Counsel",
//  *                      "users": []
//  *                      },
//  *                      {
//  *                      "id": "623042bb540c054da1d312f2",
//  *                      "roleName": "Client Executive Lead",
//  *                      "roleDescription": "",
//  *                      "isEnabled": true,
//  *                      "organizationType": "Client",
//  *                      "users": []
//  *                      }
//  *                  ]
//  *                  }
//  *       401:
//  *         description: Not authorized
//  *         content:
//  *           application/json:
//  *             example:
//  *               { "errors": [ { "message": "Not authorized"} ] }
//  */
