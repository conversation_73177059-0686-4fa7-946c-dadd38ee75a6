import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, validateRequest, InsufficientPrivilagesError, responseHandler } from "@moxfive-llc/common";
import { Policy, PolicyDoc } from "../../models/policy";
import { User } from "../../models/users";
import { getPoliciesValidation } from "./get-policies.validation";
import { fetchDefaultSortObject, escapeRegExp, getUserName } from "../../utils";
import { Incident } from "../../models/incident";
import { Tracker } from "../../models/tracker";
import { FilterQuery, SortQuery } from "../../interfaces";
import {
    fetchFiltersFromMultipleSectionsBasedOnPermission
} from "../../utils/fetch-filters-from-multiple-sections-based-on-permission";
import {
    fetchSortFromMultipleSectionsBasedOnPermission
} from "../../utils/fetch-sort-from-multiple-sections-based-on-permission";
import { authorizationFields } from "../../utils/authorization-fields";

const router = express.Router();

interface queryObject {
    page: number,
    limit: number,
    search: string,
    type: "Global" | "Application"
}
const defaultLimit = 50;
const defaultPage = 1;

router.get("/v1/authorization/policies",
    responseHandler,
    currentUser,
    requireAuth,
    getPoliciesValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToListPolicies = await hasGlobalAction(req, "ListPolicies");
            if (!hasActionToListPolicies) {
                throw new InsufficientPrivilagesError();
            }

            // Destructure the request query
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            const { page = defaultPage, limit = defaultLimit, search: searchTerm, type, filter, sort }: queryObject = req.query;
            const skipIndex = (page - 1) * limit;

            const searchQuery: { name?: { $regex: string, $options: string }, type?: string } = {};

            // If search is provided then prepare search query
            if (searchTerm) {
                const search = decodeURIComponent(searchTerm as string);
                searchQuery.name = { $regex: `${escapeRegExp(search)}`, $options: "i" };
            }
            if (type) {
                searchQuery.type = type;
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authorizationFields.policies,
                },
                {
                    path: authorizationFields.modifiedAt,
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
            const filtersQuery: FilterQuery = {};

            // If filter is provided then prepare filter query
            if(filter) {
                const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
                    filter: filter as string,
                    sections
                });
                Object.assign(filtersQuery, filters);
            }

            // If sort is provided then prepare sort query
            if(sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });
                Object.assign(sortQuery, sortObj);
            }

            // Fetch policies count and policies
            let aggregateQuery: any = [
                { $match: searchQuery },
                { $match: filtersQuery },
            ];

            if(Object.keys(sortQuery).length) {
                aggregateQuery.push({ "$sort": sortQuery });
            }

            aggregateQuery = aggregateQuery.concat([
                { "$project": { version: 0, actionIds: 0  } },
                { "$facet": { "totalRows": [{ "$count": "count" }], "data": [{ "$skip": skipIndex }, { "$limit": limit as number }] } }
            ]);

            const result: { totalRows: { count: number }[], data: PolicyDoc[] }[] = await Policy.aggregate(aggregateQuery, {
                collation: {
                    locale: "en",
                    numericOrdering: true
                }
            });
            if (result && (!result.length || !result[0].totalRows.length)) {
                return res.sendResponse({
                    totalRows: 0,
                    data: []
                }, {});
            }

            const policiesCount = result[0].totalRows[0].count;
            const policies = result[0].data;

            const userIds = new Set();
            const policyIds: string[] = [];
            policies.forEach(policy => {
                userIds.add(policy.createdBy);
                userIds.add(policy.updatedBy);
                policyIds.push(policy._id as string);
            });
            const modifiedByUsers = await User.find({ _id: [...userIds] }, { firstName: 1, lastName: 1, displayName: 1, policyIds: 1 }).lean().exec();
            const modifiedByUsersMap = new Map();
            modifiedByUsers.forEach(user => modifiedByUsersMap.set(String(user._id), getUserName(
                {
                    firstName: user.firstName,
                    lastName: user.lastName,
                    displayName: user.displayName
                })));
            const usersHavingPoliciesAttached = await User.find({ policyIds: { $in: policyIds } }).lean().exec();
            const incidentsHavingPoliciesAttached = await Incident.find({ policyIds: { $in: policyIds } }).lean().exec();
            const resiliencesHavingPoliciesAttached = await Tracker.find({ policyIds: { $in: policyIds } }).lean().exec();

            const policiesAttachedCount: { [key: string]: number } = {};

            // Loop through usersHavingPoliciesAttached
            usersHavingPoliciesAttached.forEach(user => {
                user.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            // Loop through incidentsHavingPoliciesAttached
            incidentsHavingPoliciesAttached.forEach(incident => {
                incident.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            // Loop through resiliencesHavingPoliciesAttached
            resiliencesHavingPoliciesAttached.forEach(resilience => {
                resilience.policyIds.forEach(policyId => {
                    if(!policiesAttachedCount.hasOwnProperty(policyId)) {
                        policiesAttachedCount[String(policyId)] = 0;
                    }
                    policiesAttachedCount[String(policyId)]++;
                });
            });

            // Sending the response
            res.sendResponse({
                totalRows: policiesCount,
                data: policies.map((policy: any) => ({
                    id: policy._id,
                    name: policy.name,
                    description: policy.description,
                    type: policy.type,
                    isEnabled: policy.isEnabled,
                    attachedEntities: policiesAttachedCount[policy._id] || 0,
                    createdBy: modifiedByUsersMap.get(String(policy.createdBy)),
                    updatedBy: modifiedByUsersMap.get(String(policy.updatedBy)),
                    createdAt: policy.createdAt,
                    updatedAt: policy.updatedAt
                }))
            }, {});
        }
        catch (error) {
            console.error("Authorization.GetPolicies");
            console.error(error);
            next(error);
        }
    });

export { router as getPolicies };
