import { query } from "express-validator";

export const getPoliciesValidation = [
    query("search")
        .optional()
        .isString().trim().isLength({ min: 1 }).withMessage("Search term must not be blank."),

    query("page")
        .optional()
        .isInt({ min: 1 }).toInt().withMessage("Page parameter must be a positive number."),

    query("limit")
        .optional()
        .isInt({ min: 1, max: 999 }).toInt().withMessage("Limit parameter must be a positive number and between 1 to 999."),

    query("type")
        .optional()
        .custom((value: string) => {
            if (!(["Global", "Application"].includes(value))) {
                throw new Error("Policy type can be any from this list only: Global, Application.");
            }
            else {
                return true;
            }
        }),
];
