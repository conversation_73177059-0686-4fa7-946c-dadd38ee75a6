/* eslint-disable max-depth */
/* eslint-disable max-statements */
import express, { Request, Response, NextFunction } from "express";
import { currentUser, hasGlobalAction, requireAuth, validateRequest, InsufficientPrivilagesError, ResourceNotFoundError, NotFoundCode, responseHandler } from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User } from "../../models/users";
import { Organization } from "../../models/organizations";
import { listEntitiesByPolicyValidation } from "./list-entities-by-policy.validation";
import { fetchDefaultSortObject, escapeRegExp, getUserName, hasWhiteSpace, checkMoxfiveUserOrSuperAdminPermissions } from "../../utils";
import { authorizationFields } from "../../utils/authorization-fields";
import { FilterQuery, SortQuery } from "../../interfaces";
import {
    fetchFiltersFromMultipleSectionsBasedOnPermission
} from "../../utils/fetch-filters-from-multiple-sections-based-on-permission";
import {
    fetchSortFromMultipleSectionsBasedOnPermission
} from "../../utils/fetch-sort-from-multiple-sections-based-on-permission";
import mongoose from "mongoose";
import { PolicyTypesEnum } from "../../enums/policy-types.enum";

const router = express.Router();

interface entity {
    id: string;
    name: string | null;
    email?: string;
    users?: string;
    organizationId: string | null;
    organizationName: string | null;
    type: "User" | "Incident" | "Resilience"
}

interface queryObject {
    page: number,
    limit: number,
    fetchAll: string
}
const defaultLimit = 50;

router.get("/v1/authorization/policies/:policyId/entities",
    responseHandler,
    currentUser,
    requireAuth,
    listEntitiesByPolicyValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction): Promise<any> => {
        try {
            const { policyId } = req.params;

            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "ListPlatformEntitiesForPolicy");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            // Destructure the request query
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            const { search, page = 1, limit = defaultLimit, fetchAll, filter, sort }: queryObject = req.query;
            const skipIndex = (page - 1) * limit;

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            const policy = await Policy.findById(policyId, { type: 1 }).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: policy.type === PolicyTypesEnum.GLOBAL ? authorizationFields.entitiesByPolicy.global : authorizationFields.entitiesByPolicy.application,
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
            const filtersQuery: FilterQuery = {};

            // If filter is provided then prepare filter query
            if (filter) {
                const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
                    filter: filter as string,
                    sections
                });
                Object.assign(filtersQuery, filters);
            }

            // If sort is provided then prepare sort query
            if (sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });
                Object.assign(sortQuery, sortObj);
            }

            let entities: entity[] = [];
            let entitiesCount = 0;

            if (policy.type === PolicyTypesEnum.GLOBAL) {
                const searchQuery: { keys?: string | { $in: string[] } } = {};
                if (search) {
                    const searchTerm = decodeURIComponent(search as string);
                    searchQuery.keys = hasWhiteSpace(searchTerm) ? { $in: searchTerm.split(" ") } : searchTerm;
                }

                let usersWithThePolicy = [];
                if (fetchAll) {
                    // CASE: if user is MF or Super admin user then it will consider all ther users else it will consider only those user who is part of same org
                    if (isMoxfiveUserOrSuperAdmin) {
                        usersWithThePolicy = await User.find({ policyIds: { $in: policy._id }, ...searchQuery },
                            { email: 1, firstName: 1, lastName: 1, displayName: 1, organizationId: 1 })
                            .collation({ locale: "en", numericOrdering: true })
                            .sort({ firstName: 1 })
                            .lean()
                            .exec();
                    }
                    else {
                        usersWithThePolicy = await User.find({ policyIds: { $in: policy._id }, organizationId: req.currentUser?.organizationId, ...searchQuery },
                            { email: 1, firstName: 1, lastName: 1, displayName: 1, organizationId: 1 })
                            .collation({ locale: "en", numericOrdering: true })
                            .sort({ firstName: 1 })
                            .lean()
                            .exec();
                    }
                    entitiesCount = usersWithThePolicy && usersWithThePolicy.length;

                    const organizationIds = usersWithThePolicy.map(user => user.organizationId).filter(Boolean);
                    const organizationMap = new Map();
                    const usersOrganizations = await Organization.find({ _id: { $in: organizationIds } }, { name: 1 }).lean().exec();
                    usersOrganizations.forEach(organization => organizationMap.set(String(organization._id), organization.name));
                    usersWithThePolicy.forEach(user => {
                        entities.push({
                            id: user._id as string,
                            name: getUserName(
                                {
                                    firstName: user.firstName,
                                    lastName: user.lastName,
                                    displayName: user.displayName
                                }),
                            email: user.email,
                            organizationId: user.organizationId || null,
                            organizationName: organizationMap.get(String(user.organizationId)) || null,
                            type: "User"
                        });
                    });
                }
                else {
                    let aggregateQuery: any = [];

                    // Case: Check if current user is MF or Super admin if not then get only those users whose org is same as curr user
                    if (isMoxfiveUserOrSuperAdmin) {
                        aggregateQuery = aggregateQuery.concat([
                            {
                                $match: {
                                    policyIds: new mongoose.Types.ObjectId(policy._id as string),
                                    ...searchQuery
                                }
                            }
                        ]);
                    }
                    else {
                        aggregateQuery = aggregateQuery.concat([
                            {
                                $match: {
                                    policyIds: new mongoose.Types.ObjectId(policy._id as string),
                                    organizationId: new mongoose.Types.ObjectId(req.currentUser?.organizationId),
                                    ...searchQuery
                                }
                            },
                        ]);
                    }

                    aggregateQuery = aggregateQuery.concat([
                        {
                            $addFields: {
                                name:
                                {
                                    $cond: {
                                        if: { $or: [{ $ne: ["$firstName", null] }, { $ne: ["$lastName", null] }] },
                                        then: {
                                            $concat: [
                                                { "$ifNull": ["$firstName", ""] },
                                                " ",
                                                { "$ifNull": ["$lastName", ""] },
                                            ]
                                        },
                                        else: "$displayName"
                                    }
                                },
                                type: "User"
                            }
                        },
                        {
                            $match: filtersQuery
                        }
                    ]);

                    if (Object.keys(sortQuery).length) {
                        aggregateQuery.push(
                            { $sort: sortQuery }
                        );
                    }
                    aggregateQuery = aggregateQuery.concat([
                        {
                            $lookup: {
                                from: "organizations",
                                localField: "organizationId",
                                foreignField: "_id",
                                as: "organization"
                            }
                        },
                        {
                            $unwind: {
                                path: "$organization"
                            }
                        },
                        {
                            $project: {
                                id: "$_id",
                                _id: 0,
                                email: 1,
                                name: 1,
                                type: 1,
                                organizationId: 1,
                                organizationName: "$organization.name"
                            }
                        },
                        {
                            $facet: { totalRows: [{ $count: "count" }], data: [{ $skip: skipIndex }, { $limit: limit }] }
                        }
                    ]);

                    const users: { totalRows: { count: number }[], data: entity[] }[] = await User.aggregate(aggregateQuery, {
                        collation: {
                            locale: "en",
                            numericOrdering: true
                        }
                    });

                    if (!users || !users.length || !users[0].totalRows ||
                        !users[0].totalRows.length || !users[0].totalRows[0].count || !users[0].data) {
                        return res.json({
                            totalRows: 0,
                            data: []
                        });
                    }

                    entitiesCount = users[0].totalRows[0].count;
                    entities = users[0].data;
                }

            }
            else {
                let aggregateQuery: any = [];
                const searchQuery: { name?: { $regex: string, $options: string } } = {};
                if (search) {
                    const searchTerm = decodeURIComponent(search as string);
                    searchQuery.name = { $regex: `${escapeRegExp(searchTerm)}`, $options: "i" };
                }

                if (fetchAll) {
                    // Prepare aggregate query for fetchAll
                    aggregateQuery = [
                        {
                            $match: {
                                _id: new mongoose.Types.ObjectId(policy._id as string),
                            }
                        }
                    ];

                    /* CASE: If current user is MF or super admin it will consider all the entities with incident and resilienc
                        else if not then it will consider only those whose having current user as a member in incident or resilience
                    */
                    if (isMoxfiveUserOrSuperAdmin) {
                        aggregateQuery = aggregateQuery.concat([
                            {
                                $lookup: {
                                    from: "incidents",
                                    localField: "_id",
                                    foreignField: "policyIds",
                                    as: "incidents"
                                }
                            },
                            {
                                $addFields: {
                                    "incidents.type": "Incident"
                                }
                            },
                            {
                                $lookup: {
                                    from: "trackers",
                                    localField: "_id",
                                    foreignField: "policyIds",
                                    as: "trackers"
                                },
                            },
                            {
                                $addFields: {
                                    "trackers.type": "Resilience"
                                }
                            }
                        ]);
                    }
                    else {
                        aggregateQuery = aggregateQuery.concat([
                            {
                                $lookup: {
                                    from: "incidents",
                                    localField: "_id",
                                    foreignField: "policyIds",
                                    pipeline: [
                                        {
                                            $match: {
                                                members: { $in: [new mongoose.Types.ObjectId(req.currentUser?.id)] }
                                            }
                                        }
                                    ],
                                    as: "incidents"
                                }
                            },
                            {
                                $addFields: {
                                    "incidents.type": "Incident"
                                }
                            },
                            {
                                $lookup: {
                                    from: "trackers",
                                    localField: "_id",
                                    foreignField: "policyIds",
                                    pipeline: [
                                        {
                                            $match: {
                                                members: { $in: [new mongoose.Types.ObjectId(req.currentUser?.id)] }
                                            }
                                        }
                                    ],
                                    as: "trackers"
                                },
                            },
                            {
                                $addFields: {
                                    "trackers.type": "Resilience"
                                }
                            }
                        ]);
                    }

                    aggregateQuery = aggregateQuery.concat([
                        {
                            $addFields: {
                                entities: { $concatArrays: ["$incidents", "$trackers"] }
                            }
                        },
                        {
                            $unwind: {
                                path: "$entities"
                            }
                        },
                        {
                            $replaceRoot: {
                                newRoot: "$entities"
                            }
                        },
                        {
                            $lookup: {
                                from: "organizations",
                                localField: "clientId",
                                foreignField: "_id",
                                as: "organization"
                            }
                        },
                        {
                            $unwind: {
                                path: "$organization"
                            }
                        },
                        {
                            $project: {
                                id: "$_id",
                                _id: 0,
                                name: 1,
                                type: 1,
                                organizationId: "$clientId",
                                organizationName: "$organization.name"
                            }
                        },
                        {
                            $facet: { totalRows: [{ $count: "count" }], data: [{ $skip: 0 }] }
                        }
                    ]);
                }
                else {
                    // Prepare aggregate query
                    aggregateQuery = [
                        {
                            $match: {
                                _id: new mongoose.Types.ObjectId(policy._id as string),
                            }
                        }
                    ];

                    const entitiesToPopulate: string[] = [];

                    // If filtersQuery has not type filter or it contains Incident
                    if (!filtersQuery.type || (filtersQuery.type["$in"] && filtersQuery.type["$in"].includes("Incident"))
                        || (filtersQuery.type["$nin"] && !(filtersQuery.type["$nin"].includes("Incident")))) {
                        // CASE: if user is MF or Super admin user then it consider all the incidents else it will consider on incidents in which current user is member
                        if (isMoxfiveUserOrSuperAdmin) {
                            aggregateQuery = aggregateQuery.concat([
                                {
                                    $lookup: {
                                        from: "incidents",
                                        localField: "_id",
                                        foreignField: "policyIds",
                                        as: "incidents"
                                    }
                                },
                                {
                                    $addFields: {
                                        "incidents.type": "Incident"
                                    }
                                }
                            ]);
                        }
                        else {
                            aggregateQuery = aggregateQuery.concat([
                                {
                                    $lookup: {
                                        from: "incidents",
                                        localField: "_id",
                                        foreignField: "policyIds",
                                        pipeline: [
                                            {
                                                $match: {
                                                    members: { $in: [new mongoose.Types.ObjectId(req.currentUser?.id)] }
                                                }
                                            }
                                        ],
                                        as: "incidents"
                                    }
                                },
                                {
                                    $addFields: {
                                        "incidents.type": "Incident"
                                    }
                                }
                            ]);
                        }
                        entitiesToPopulate.push("$incidents");
                    }

                    // If filtersQuery has not type filter or it contains Resilience
                    if (!filtersQuery.type || (filtersQuery.type["$in"] && filtersQuery.type["$in"].includes("Resilience"))
                        || (filtersQuery.type["$nin"] && !filtersQuery.type["$nin"].includes("Resilience"))) {
                        // CASE: if user is MF or Super admin user then it consider all the resilience else it will consider on resilience in which current user is member
                        if (isMoxfiveUserOrSuperAdmin) {
                            aggregateQuery = aggregateQuery.concat([
                                {
                                    $lookup: {
                                        from: "trackers",
                                        localField: "_id",
                                        foreignField: "policyIds",
                                        as: "trackers"
                                    },
                                },
                                {
                                    $addFields: {
                                        "trackers.type": "Resilience"
                                    }
                                }
                            ]);
                        }
                        else {
                            aggregateQuery = aggregateQuery.concat([
                                {
                                    $lookup: {
                                        from: "trackers",
                                        localField: "_id",
                                        foreignField: "policyIds",
                                        pipeline: [
                                            {
                                                $match: {
                                                    members: { $in: [new mongoose.Types.ObjectId(req.currentUser?.id)] }
                                                }
                                            }
                                        ],
                                        as: "trackers"
                                    },
                                },
                                {
                                    $addFields: {
                                        "trackers.type": "Resilience"
                                    }
                                }
                            ]);
                        }
                        entitiesToPopulate.push("$trackers");
                    }

                    if (!entitiesToPopulate.length) {
                        return res.sendResponse({
                            totalRows: 0,
                            data: []
                        }, {});
                    }

                    delete filtersQuery.type;

                    aggregateQuery = aggregateQuery.concat([
                        {
                            $addFields: {
                                entities: { $concatArrays: entitiesToPopulate }
                            }
                        },
                        {
                            $unwind: {
                                path: "$entities"
                            }
                        },
                        {
                            $replaceRoot: {
                                newRoot: "$entities"
                            }
                        },
                        {
                            $match: searchQuery
                        },
                        {
                            $lookup: {
                                from: "organizations",
                                localField: "clientId",
                                foreignField: "_id",
                                as: "organization"
                            }
                        },
                        {
                            $unwind: {
                                path: "$organization"
                            }
                        },
                    ]);

                    if (Object.keys(sortQuery).length) {
                        aggregateQuery.push(
                            { $sort: sortQuery }
                        );
                    }
                    aggregateQuery = aggregateQuery.concat([
                        {
                            $match: filtersQuery
                        },
                        {
                            $project: {
                                id: "$_id",
                                _id: 0,
                                name: 1,
                                type: 1,
                                organizationId: "$clientId",
                                organizationName: "$organization.name"
                            }
                        },
                        {
                            $facet: { totalRows: [{ $count: "count" }], data: [{ $skip: skipIndex }, { $limit: limit }] }
                        }
                    ]);

                }
                const entityDetails: { totalRows: { count: number }[], data: entity[] }[] = await Policy.aggregate(aggregateQuery, {
                    collation: {
                        locale: "en",
                        numericOrdering: true
                    }
                });

                if (!entityDetails || !entityDetails.length || !entityDetails[0].totalRows ||
                    !entityDetails[0].totalRows.length || !entityDetails[0].totalRows[0].count || !entityDetails[0].data) {
                    return res.sendResponse({
                        totalRows: 0,
                        data: []
                    }, {});
                }

                entitiesCount = entityDetails[0].totalRows[0].count;
                entities = entityDetails[0].data;

                // Fetch attached incident users
                const entityIds = entities.map(entity => String(entity.id));
                const entityUsersMap = new Map();
                const entityUsers = await User.find({
                    $and: [
                        { "applicationPolicyIds.applicationId": { $in: [...entityIds] } },
                        { "applicationPolicyIds.applicationType": "Incident" },
                        { "applicationPolicyIds.policyId": policyId }
                    ]
                }, { applicationPolicyIds: 1 }).lean().exec();

                entityIds.forEach(entityId => {
                    const usersCount = entityUsers.filter(user => !!user.applicationPolicyIds.find(policy => String(policy.applicationId) === entityId)).length;
                    entityUsersMap.set(entityId, usersCount);
                });

                entities = entities.map(entity => {
                    return {
                        ...entity,
                        users: entityUsersMap.get(String(entity.id)) || 0,
                    };
                });
            }

            res.sendResponse({
                totalRows: entitiesCount,
                data: entities
            }, {});
        }
        catch (error) {
            console.error("Authorization.ListPolicyEntities");
            console.error(error);
            next(error);
        }
    });

export { router as listPolicyEntities };
