import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, hasGlobalAction, requireAuth, validateRequest, ResourceNotFoundError, NotFoundCode,
    InsufficientPrivilagesError, SucceededPartially, BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, responseHandler, TargetType
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { entityObj } from "../../interfaces/entity-object";
import { detachPolicyFromEntitiesValidation } from "./detach-policy-from-entities.validation";
import { Incident, IncidentDoc } from "../../models/incident";
import { incidentPolicyUpdatedPublisherWrapper } from "../../utils/incident-policy-updated-publisher-wrapper";
import { Tracker, TrackerDoc } from "../../models/tracker";
import { trackerPolicyUpdatedPublisherWrapper } from "../../utils/tracker-policy-updated-publisher-wrapper";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { ACTIVE_FOCUS_VIEW_ACTION, checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { getPolicyNameByIds } from "../../utils/get-policy-names-by-ids";
import { getUserApplicationModifiedProperties } from "../../utils/get-user-application-modified-properties";
import { Action } from "../../models/action";
import { ActiveFocusViewRemovedPublisherWrapper } from "../../utils/active-focus-view-removed-wrapper";

const router = express.Router();

router.put("/v1/authorization/policies/:policyId/entities/detach",
    responseHandler,
    currentUser,
    requireAuth,
    detachPolicyFromEntitiesValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { policyId } = req.params;
            const { entities } = req.body;

            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "DetachPolicyFromPlatformEntity");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            const policy = await Policy.findById(policyId, { _id: 1, type: 1, actionIds: 1 }).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not exist.");
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            // For Focus View, If ActiveFocusView action gets removed then publish an event
            const actions = await Action.find({ _id: { $in: [...policy.actionIds] }, name: ACTIVE_FOCUS_VIEW_ACTION }).lean().exec();
            const actionWasPresent = !!(actions && actions.length);
            const affectedUserIds: string[] = [];

            const isGlobalPolicy = policy.type === "Global";
            if (isGlobalPolicy) {
                const invalidEntities = entities.filter((entity: { id: string, type: string }) => entity.type !== "User");
                if (invalidEntities && invalidEntities.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_DETACH_CONFLICT, "Global Type policy cannot be detached from incident.");
                }
            }

            const failedUserEntities: string[] = [];
            const failedIncidentEntities: string[] = [];
            const failedResilienceEntities: string[] = [];
            const targets: any = [];
            const modifiedProperties: any = [];
            const targetsArray: { target: TargetType, obj: UserDoc | IncidentDoc | TrackerDoc }[] = [];
            const modifiedPolicyIds: { target: TargetType, oldValue: string[], newValue: string[] }[] = [];
            const modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[] = [];

            /*
            CASE:
                For policy remove to user user should be MF + Super admin or can assign to users from same org
                For policy remove to incident/resilience User must be member of incident/resilience or MF + Super Admin user
            */
            const userMap = new Map<string, UserDoc>();
            const incidentMap = new Map<string, IncidentDoc>();
            const resilienceMap = new Map<string, TrackerDoc>();
            await Promise.all(entities.map(async (entity: entityObj) => {
                if(entity.type === "User") {
                    const user = await User.findById(entity.id);
                    if(user) {
                        // Setting users to a map with entity id for easy retrival
                        userMap.set(entity.id, user);

                        // Check if user is not moxfive user or has super admin permission then check if currentuser org is same as users org
                        if(!isMoxfiveUserOrSuperAdmin) {
                            if(req.currentUser?.organizationId !== String(user?.organizationId)) throw new InsufficientPrivilagesError();
                        }
                    }
                }
                else if (entity.type === "Incident") {
                    const incident = await Incident.findById(entity.id);
                    if(incident) {
                        // Setting incident to map with entity id which is incident id only for easy retrival
                        incidentMap.set(entity.id, incident);

                        // Check if user is not moxfive user or has super admin permission then check if currentuser is member of incident
                        if(!isMoxfiveUserOrSuperAdmin) {
                            const members = incident?.members.map(userId => String(userId));
                            if(!members?.includes(req.currentUser?.id ?? "")) throw new InsufficientPrivilagesError();
                        }
                    }
                }
                else if (entity.type === "Resilience") {
                    const resilience = await Tracker.findById(entity.id);
                    if(resilience) {
                        // Setting resilience to map with entity id which is incident id only for easy retrival
                        resilienceMap.set(entity.id, resilience);

                        // Check if user is not moxfive user or has super admin permission then check if currentuser is member of resilience
                        if(!isMoxfiveUserOrSuperAdmin) {
                            const members = resilience?.members.map(userId => String(userId));
                            if(!members?.includes(req.currentUser?.id ?? "")) throw new InsufficientPrivilagesError();
                        }
                    }
                }
            }));

            await Promise.all(entities.map(async (entity: entityObj) => {
                if (entity.type === "User") {
                    const user = userMap.get(entity.id);
                    if (user) {
                        if (isGlobalPolicy) {
                            const oldValue = [...user.policyIds];
                            const index = user.policyIds?.indexOf(policyId) as number;
                            if (index !== -1 && user.policyIds) {
                                user.policyIds.splice(index, 1);
                            }
                            modifiedPolicyIds.push({
                                target: TargetType.USER,
                                oldValue,
                                newValue: user.policyIds
                            });
                        }
                        else {
                            const userApplicationPolicies = [...user.applicationPolicyIds];
                            const oldValue = [...user.applicationPolicyIds];
                            const userApplicationPolicyIds = userApplicationPolicies.map(policy => String(policy.policyId));
                            const index = userApplicationPolicyIds.indexOf(policyId) as number;
                            // (index !== -1) && userApplicationPolicies.splice(index, 1);
                            if (index !== -1) {
                                userApplicationPolicies.splice(index, 1);
                            }
                            user.applicationPolicyIds = [...userApplicationPolicies];

                            modifiedApplicationIds.push({
                                oldValue,
                                newValue: [...userApplicationPolicies]
                            });
                        }
                        await user.save();
                        await userPolicyUpdatedPublisherWrapper(user);

                        targetsArray.push({
                            target: TargetType.USER,
                            obj: user
                        });

                        if (actionWasPresent) {
                            affectedUserIds.push(entity.id);
                        }
                    }
                    else {
                        failedUserEntities.push(entity.id);
                    }
                }
                else if (entity.type === "Incident") {
                    const incident = incidentMap.get(entity.id);
                    if (incident) {
                        const index = incident.policyIds.indexOf(policyId) as number;
                        const oldValue = [...incident.policyIds];
                        if (index !== -1) {
                            incident.policyIds.splice(index, 1);
                            await incident.save();
                            await incidentPolicyUpdatedPublisherWrapper(incident);

                            // For audit logs
                            targetsArray.push({
                                target: TargetType.INCIDENT,
                                obj: incident
                            });
                            modifiedPolicyIds.push({
                                target: TargetType.INCIDENT,
                                oldValue,
                                newValue: incident.policyIds
                            });

                            const users = await User.find({ _id: { $in: [...incident.members] }, "applicationPolicyIds.policyId": policyId });
                            await Promise.all(users.map(async (user) => {
                                const userApplicationPolicies = [...user.applicationPolicyIds];
                                const oldValue = [...user.applicationPolicyIds];
                                const userApplicationPolicyIds = userApplicationPolicies.map(policy => String(policy.policyId));
                                const policyIndex = userApplicationPolicyIds.indexOf(policyId) as number;

                                if (policyIndex !== -1) {
                                    userApplicationPolicies.splice(policyIndex, 1);
                                    user.applicationPolicyIds = [...userApplicationPolicies];
                                    await user.save();
                                    await userPolicyUpdatedPublisherWrapper(user);

                                    targetsArray.push({
                                        target: TargetType.USER,
                                        obj: user
                                    });
                                    modifiedApplicationIds.push({
                                        oldValue,
                                        newValue: user.applicationPolicyIds
                                    });
                                }
                            }));
                        }
                    }
                    else {
                        failedIncidentEntities.push(entity.id);
                    }
                }
                else if (entity.type === "Resilience") {
                    const resilience = resilienceMap.get(entity.id);
                    if (resilience) {
                        const index = resilience.policyIds.indexOf(policyId) as number;
                        const oldValue = [...resilience.policyIds];
                        if (index !== -1) {
                            resilience.policyIds.splice(index, 1);
                            await resilience.save();
                            await trackerPolicyUpdatedPublisherWrapper(resilience);

                            // For audit log
                            targetsArray.push({
                                target: TargetType.BUSINESSRESILIENCE,
                                obj: resilience
                            });
                            modifiedPolicyIds.push({
                                target: TargetType.BUSINESSRESILIENCE,
                                oldValue,
                                newValue: resilience.policyIds
                            });

                            const users = await User.find({ _id: { $in: [...resilience.members] }, "applicationPolicyIds.policyId": policyId });
                            await Promise.all(users.map(async (user) => {
                                const userApplicationPolicies = [...user.applicationPolicyIds];
                                const oldValue = [...user.applicationPolicyIds];
                                const userApplicationPolicyIds = userApplicationPolicies.map(policy => String(policy.policyId));
                                const policyIndex = userApplicationPolicyIds.indexOf(policyId) as number;

                                if (policyIndex !== -1) {
                                    userApplicationPolicies.splice(policyIndex, 1);
                                    user.applicationPolicyIds = [...userApplicationPolicies];
                                    await user.save();
                                    await userPolicyUpdatedPublisherWrapper(user);

                                    targetsArray.push({
                                        target: TargetType.USER,
                                        obj: user
                                    });
                                    modifiedApplicationIds.push({
                                        oldValue,
                                        newValue: user.applicationPolicyIds
                                    });
                                }
                            }));
                        }
                    }
                    else {
                        failedResilienceEntities.push(entity.id);
                    }
                }
            }));

            if (affectedUserIds.length) {
                await ActiveFocusViewRemovedPublisherWrapper(affectedUserIds);
            }

            if (failedUserEntities.length === entities.length || failedIncidentEntities.length === entities.length) {
                throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entities are not found in the list of eligible entities for policy.");
            }

            if (failedUserEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedUserEntities,
                        message: "Users not found."
                    }]
                }], "One or more users are failed to detach from the specified policy.");
            }

            if (failedIncidentEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedIncidentEntities,
                        message: "Incidents not found."
                    }]
                }], "One or more incidents are failed to detach from the specified policy.");
            }

            if (failedResilienceEntities.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedResilienceEntities,
                        message: "Resiliences not found."
                    }]
                }], "One or more resiliences are failed to detach from the specified policy.");
            }

            // Prepare targets
            targetsArray.forEach(target => {
                if (target.target === TargetType.USER) {
                    const user = target.obj as UserDoc;
                    targets.push({
                        type: target.target,
                        details: {
                            id: user.id,
                            name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                            email: user.email,
                            azureId: ""
                        }
                    });
                }
                else {
                    const values = target.obj as IncidentDoc | TrackerDoc;
                    targets.push({
                        type: target.target,
                        details: {
                            id: values.id,
                            name: values.name
                        }
                    });
                }
            });

            // Prepare policyId modified property
            let policyIds: string[] = [];
            modifiedPolicyIds.forEach(item => {
                policyIds = [...policyIds, ...item.oldValue];
            });

            const policyIdToNameMap = await getPolicyNameByIds(policyIds);
            modifiedPolicyIds.forEach(item => {
                modifiedProperties.push({
                    target: item.target,
                    propertyName: "policyIds",
                    oldValue: JSON.stringify(item.oldValue.map((policyId: string) => ({
                        id: String(policyId),
                        value: policyIdToNameMap.get(String(policyId))
                    }))),
                    newValue: JSON.stringify(item.newValue.map((policyId: string) => ({
                        id: String(policyId),
                        value: policyIdToNameMap.get(String(policyId))
                    })))
                });
            });

            const newModifiedProperties = await getUserApplicationModifiedProperties(modifiedApplicationIds);

            res.sendResponse({
                meta: {
                    message: "Policy detached successfully."
                }
            }, {
                targets,
                modifiedProperties: [
                    ...modifiedProperties,
                    ...newModifiedProperties
                ]
            });
        }
        catch (error) {
            console.error("Authorization.DetachPolicyFromEntities");
            console.error(error);
            next(error);
        }
    });

export { router as detachPolicyFromEntities };
