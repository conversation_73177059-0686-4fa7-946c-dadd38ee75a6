import { body, param } from "express-validator";

export const detachPolicyFromEntitiesValidation = [
    param("policyId")
        .isMongoId(),

    body("entities")
        .isArray({ min: 1 })
        .withMessage("No entity found to be attached with the specified policy."),

    body("entities.*.type")
        .exists().bail()
        .custom(value => {
            if (!["User", "Incident", "Resilience"].includes(value)) {
                throw new Error("Policy entity type can be any from this list only: User, Incident, Resilience.");
            }
            else {
                return true;
            }
        }),

    body("entities.*.id")
        .isMongoId()
        .withMessage("This is invalid entity id."),
];
