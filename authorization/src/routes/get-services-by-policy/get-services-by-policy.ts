import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, InsufficientPrivilagesError, ResourceNotFoundError, NotFoundCode, validateRequest, responseHandler } from "@moxfive-llc/common";

import { Service } from "../../models/service";
import { Policy } from "../../models/policy";
import { Action } from "../../models/action";
import { AccessControl } from "../../models/access-control";
import { getServicesByPolicyValidation } from "./get-services-by-policy.validation";

const router = express.Router();

router.get(
    "/v1/authorization/policies/:policyId/services",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    getServicesByPolicyValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const isMoxfiveUser = process.env.MOXFIVE_ID === req.currentUser?.organizationId;

            const hasActionToGetPolicyDetail = await hasGlobalAction(req, "ListServicesForPolicy");
            if (!hasActionToGetPolicyDetail) {
                throw new InsufficientPrivilagesError();
            }

            const policy = await Policy.findById(req.params.policyId).lean().exec();
            if(!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not Found.");
            }

            const actions = await Action.find({ _id: { $in: policy.actionIds }, requireAuthorization: true }).lean().exec();
            const serviceIds = new Set();
            actions.forEach(action => serviceIds.add(action.serviceId));
            const services = await Service.find({ _id: { $in: [...serviceIds] } }).lean().exec();
            if(!isMoxfiveUser) {
                return res.sendResponse(services.map(service => ({
                    id: service._id,
                    name: service.name
                })), {});
            }

            const fullActions = await Action.find({ serviceId: { $in: [...serviceIds] },  requireAuthorization: true }).lean().exec();
            const accessControlIds = new Set();
            fullActions.forEach(action => accessControlIds.add(action.accessControlId));
            const accessControls = await AccessControl.find({ _id: { $in: [...accessControlIds] } }).lean().exec();
            const responseData: any = [];
            services.forEach(service => {
                const serviceAccessControlIds = fullActions.filter(action => String(action.serviceId) === String(service._id)).map(action => String(action.accessControlId));
                const serviceAccessControls = accessControls.filter(accessControl => serviceAccessControlIds.includes(String(accessControl._id)));
                const accessControlsData: { name: string; access: string; }[] = [];
                serviceAccessControls.forEach(accessControl => {
                    const fullServiceActions = fullActions.filter(action =>
                        String(action.serviceId) === String(service._id) && String(action.accessControlId) === String(accessControl._id));
                    const selectedServiceActions = fullServiceActions.filter(action => policy.actionIds.map(String).includes(String(action._id)));
                    accessControlsData.push({
                        name: accessControl.name,
                        // eslint-disable-next-line no-nested-ternary
                        access: selectedServiceActions.length ? fullServiceActions.length === selectedServiceActions.length ? "Full" : "Limited" : "None"
                    });
                });

                const matchedWords = service.name.match(/[A-Z][a-z]+/g);

                responseData.push({
                    id: service._id,
                    name: service.name,
                    displayName: (matchedWords && matchedWords.length) ? matchedWords.join(" ") : service.name,
                    description: service.description,
                    accessControls: accessControlsData
                });
            });

            res.sendResponse(responseData, {});
        }
        catch (error) {
            console.error("Authorization.GetServicesByPolicy");
            console.error(error);
            next(error);
        }
    }
);

export { router as getServicesByPolicy };
