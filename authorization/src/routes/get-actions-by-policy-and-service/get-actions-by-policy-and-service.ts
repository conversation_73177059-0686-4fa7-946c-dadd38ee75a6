import express, { Request, Response, NextFunction } from "express";
import { currentUser, requireAuth, hasGlobalAction, InsufficientPrivilagesError, ResourceNotFoundError, NotFoundCode, validateRequest, responseHandler } from "@moxfive-llc/common";

import { Service } from "../../models/service";
import { Policy } from "../../models/policy";
import { Action } from "../../models/action";
import { AccessControl } from "../../models/access-control";
import { getActionsByPolicyAndServiceValidation } from "./get-actions-by-policy-and-service.validation";
import { insertWhiteSpaces } from "../../utils";
import { Category } from "@moxfive-llc/common/build/models/category.model";

const router = express.Router();

router.get(
    "/v1/authorization/policies/:policyId/services/:serviceId/actions",
    responseHandler,
    currentUser,
    requireAuth,
    getActionsByPolicyAndServiceValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "ListServiceActionsForPolicy");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            const { policyId, serviceId } = req.params;

            const policy = await Policy.findById(policyId).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not Found.");
            }

            const service = await Service.findById(serviceId).lean().exec();
            if (!service) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_SERVICE_NOT_FOUND, "Policy service not found.");
            }
            const serviceActions = await Action.find({ serviceId: service._id, requireAuthorization: true }, { requireAuthorization: 0 }).lean().exec();
            const accessControlIds = new Set();
            serviceActions.forEach(action => accessControlIds.add(action.accessControlId));
            const serviceControls = await AccessControl.find({ _id: { $in: [...accessControlIds] } }).lean().exec();

            // Fetch categories and make action-category map
            const categories = await Category.find({ serviceId });
            const statusUpdatesCategoryActions = categories.find(cat => cat.name === "Project Status Updates")?.actionIds.map(String) || [];
            const actionCategoryMap: Map<string, string> = new Map();
            categories.forEach(category => {
                category.actionIds.forEach(action => {
                    actionCategoryMap.set(String(action), category.name);
                });
            });

            const responseData = serviceControls.map(serviceControl => ({
                id: serviceControl._id,
                name: serviceControl.name,
                description: serviceControl.description,
                actions: serviceActions
                    .filter(action => String(action.accessControlId) === String(serviceControl._id))
                    .map(action => ({
                        id: String(action._id),
                        name: action.name,
                        displayName: statusUpdatesCategoryActions.includes(String(action._id))
                            ? `(Version 2.0) ${insertWhiteSpaces(action.name)}`
                            : insertWhiteSpaces(action.name),
                        description: action.description,
                        isEnabled: action.isEnabled,
                        selected: policy.actionIds.map(String).includes(String(action._id)),
                        moxfiveExclusive: action.moxfiveExclusive,
                        category: actionCategoryMap.get(String(action._id)) || ""
                    }))
                    .sort((actionA, actionB) => actionA.name.localeCompare(actionB.name))
            }));
            res.sendResponse(responseData, {});
        }
        catch (error) {
            console.error("Authorization.GetActionsByServicesAndPolicy");
            console.error(error);
            next(error);
        }
    }
);

export { router as getActionsByServicesAndPolicy };
