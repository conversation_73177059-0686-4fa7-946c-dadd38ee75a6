import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, hasGlobalAction, requireAuth, validateRequest, InsufficientPrivilagesError, ResourceNotFoundError,
    NotFoundCode, SucceededPartially, BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, responseHandler, TargetType
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { Incident } from "../../models/incident";
import { attachPolicyToIncidentUsersValidations } from "./attach-policy-to-incident-users.validation";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { getUserApplicationModifiedProperties } from "../../utils/get-user-application-modified-properties";

const router = express.Router();

router.put("/v1/authorization/policies/:policyId/incidents/:incidentId/users/attach",
    responseHandler,
    currentUser,
    requireAuth,
    attachPolicyToIncidentUsersValidations,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { policyId, incidentId } = req.params;
            const { userIds } = req.body;

            const hasActionToAttachPolicyToIncidentUsers = await hasGlobalAction(req, "AttachPolicyToIncidentUsers");
            if (!hasActionToAttachPolicyToIncidentUsers) {
                throw new InsufficientPrivilagesError();
            }

            const policy = await Policy.findById(policyId, { _id: 1, type: 1 }).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const incident = await Incident.findById(incidentId, { _id: 1, policyIds: 1, members: 1 }).lean().exec();
            if (!incident) {
                throw new ResourceNotFoundError(NotFoundCode.INCIDENT_NOT_FOUND, "Incident not found.");
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);
            if (!isMoxfiveUserOrSuperAdmin) {
                const matcheduser = (incident.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                if (!matcheduser) throw new InsufficientPrivilagesError();
            }

            const users = await User.find({ _id: { $in: [...userIds] } });
            if (users && !users.length) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            if (policy.type === "Global") {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_ATTACH_CONFLICT, "Global Type policy cannot attached to users.");
            }

            const isPolicyAttachedToIncident = incident.policyIds.map(String).find(policy => policy === policyId);
            if (!isPolicyAttachedToIncident) {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.POLICY_NOT_ATTACHED_TO_INCIDENT, "Policy must be attached to incident.");
            }

            const validUsers: UserDoc[] = [];
            const usersWithPolicyExistForIncident: string[] = [];
            const invalidUserIds: string[] = [];
            /* Case: This usersPartOfIncident is for making assurity that all the users are members of incident
                Because policy can be assigned to the incident members only
            */
            const incidentMembers = incident.members.map(userId => String(userId));
            const usersPartOfIncident = users.filter(user => incidentMembers.includes(String(user.id)));
            const usersIds = usersPartOfIncident.map(user => String(user.id));
            const targets: any = [];
            const modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[] = [];

            userIds.forEach((userId: string) => {
                if (usersIds.includes(userId)) {
                    const userDetail = users.find(user => user.id === userId);
                    if (userDetail) {
                        const userApplicationPolicies = [...userDetail.applicationPolicyIds];
                        const isIncidentPolicyExist = userApplicationPolicies.find(policy => String(policy.applicationId) === String(incidentId));
                        if (isIncidentPolicyExist) {
                            if (String(isIncidentPolicyExist.policyId) !== String(policyId)) {
                                usersWithPolicyExistForIncident.push(userId);
                            }
                        }
                        else {
                            validUsers.push(userDetail);
                        }
                    }
                }
                else {
                    invalidUserIds.push(policyId);
                }
            });

            if (usersWithPolicyExistForIncident.length === userIds.length) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ONE_POLICY_PER_INCIDENT_FOR_USER,
                    "User can have only one application policy attached for each incident. Please detach existing policy from user and try again."
                );
            }

            await Promise.all(validUsers.map(async (user) => {
                const oldValue = [...user.applicationPolicyIds];
                user.applicationPolicyIds.push({
                    applicationId: incidentId,
                    applicationType: "Incident",
                    policyId: policyId
                });
                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);

                targets.push({
                    type: TargetType.USER,
                    details: {
                        id: user.id,
                        name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                        email: user.email,
                        azureId: ""
                    }
                });
                modifiedApplicationIds.push({
                    oldValue,
                    newValue: user.applicationPolicyIds
                });
            }));

            const errors: { attributes: string[], message: string }[] = [];
            if (invalidUserIds.length) {
                errors.push({
                    attributes: invalidUserIds,
                    message: "Users not found."
                });
            }

            if (usersWithPolicyExistForIncident.length) {
                errors.push({
                    attributes: usersWithPolicyExistForIncident,
                    message: "User can have only one application policy attached for each incident. Please detach existing policy from this users and try again."
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users are failed to attached with the specified policy.");
            }

            // For audit logs - user applicationPolicyIds
            const modifiedProperties = await getUserApplicationModifiedProperties(modifiedApplicationIds);

            res.sendResponse({
                meta: {
                    message: "Policy attached successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.AttachPolicyToIncidentUsers");
            console.error(error);
            next(error);
        }
    });

export { router as attachPolicyToIncidentUsers };
