import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const attachPolicyToResilienceUsersValidations = [
    param("policyId")
        .isMongoId(),

    param("trackerId")
        .isMongoId(),

    body("userIds")
        .isArray({ min: 1 })
        .withMessage("Users not found.").bail()
        .custom((userIds: string[]) => {
            return userIds.every(userId => {
                return isValidMongoObjectId(userId);
            });
        }).withMessage("This are invalid users ids.")
];
