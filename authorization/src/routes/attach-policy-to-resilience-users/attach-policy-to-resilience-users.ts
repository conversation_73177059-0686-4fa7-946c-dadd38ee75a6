import express, { Request, Response, NextFunction } from "express";
import {
    currentUser, hasGlobalAction, requireAuth, validateRequest, InsufficientPrivilagesError, ResourceNotFoundError,
    NotFoundCode, SucceededPartially, BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, responseHandler, TargetType
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { attachPolicyToResilienceUsersValidations } from "./attach-policy-to-resilience-users.validation";
import { Tracker } from "../../models/tracker";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { getUserApplicationModifiedProperties } from "../../utils/get-user-application-modified-properties";

const router = express.Router();

router.put("/v1/authorization/policies/:policyId/trackers/:trackerId/users/attach",
    responseHandler,
    currentUser,
    requireAuth,
    attachPolicyToResilienceUsersValidations,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { policyId, trackerId } = req.params;
            const { userIds } = req.body;

            const hasActionToAttachPolicyToResilienceUsers = await hasGlobalAction(req, "AttachPolicyToResilienceUsers");
            if (!hasActionToAttachPolicyToResilienceUsers) {
                throw new InsufficientPrivilagesError();
            }

            const policy = await Policy.findById(policyId, { _id: 1, type: 1 }).lean().exec();
            if (!policy) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policy not found.");
            }

            const resilience = await Tracker.findById(trackerId, { _id: 1, policyIds: 1, members: 1 }).lean().exec();
            if (!resilience) {
                throw new ResourceNotFoundError(NotFoundCode.TRACKER_NOT_FOUND, "Tracker not found.");
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);
            if (!isMoxfiveUserOrSuperAdmin) {
                const matcheduser = (resilience.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                if (!matcheduser) throw new InsufficientPrivilagesError();
            }

            const users = await User.find({ _id: { $in: [...userIds] } });
            if (users && !users.length) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            if (policy.type === "Global") {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_ATTACH_CONFLICT, "Global Type policy cannot attached to users.");
            }

            const isPolicyAttachedToResilience = resilience.policyIds.map(String).find(policy => policy === policyId);
            if (!isPolicyAttachedToResilience) {
                throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.POLICY_NOT_ATTACHED_TO_RESILIENCE, "Policy must be attached to resilience.");
            }

            const validUsers: UserDoc[] = [];
            const usersWithPolicyExistForResilience: string[] = [];
            const invalidUserIds: string[] = [];
            /* Case: usersPartOfResilience is for making assurity that all the users are members of resilience
                Because policy can be assigned to the resilience members only
            */
            const resilienceMembers = resilience.members.map(userId => String(userId));
            const usersPartOfResilience = users.filter(user => resilienceMembers.includes(String(user.id)));
            const usersIds = usersPartOfResilience.map(user => String(user.id));
            const targets: any = [];
            const modifiedApplicationIds: { oldValue: ApplicationPolicyIdObj[], newValue: ApplicationPolicyIdObj[] }[] = [];

            userIds.forEach((userId: string) => {
                if (usersIds.includes(userId)) {
                    const userDetail = users.find(user => user.id === userId);
                    if (userDetail) {
                        const userApplicationPolicies = [...userDetail.applicationPolicyIds];
                        const isResiliencePolicyExist = userApplicationPolicies.find(policy => String(policy.applicationId) === String(trackerId));
                        if (isResiliencePolicyExist) {
                            if (String(isResiliencePolicyExist.policyId) !== String(policyId)) {
                                usersWithPolicyExistForResilience.push(userId);
                            }
                        }
                        else {
                            validUsers.push(userDetail);
                        }
                    }
                }
                else {
                    invalidUserIds.push(policyId);
                }
            });

            if (usersWithPolicyExistForResilience.length === userIds.length) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.ONE_POLICY_PER_RESILIENCE_FOR_USER,
                    "User can have only one application policy attached for each resilience. Please detach existing policy from user and try again."
                );
            }

            await Promise.all(validUsers.map(async (user) => {
                const oldValue = [...user.applicationPolicyIds];
                user.applicationPolicyIds.push({
                    applicationId: trackerId,
                    applicationType: "Resilience",
                    policyId: policyId
                });
                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);

                targets.push({
                    type: TargetType.USER,
                    details: {
                        id: user.id,
                        name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                        email: user.email,
                        azureId: ""
                    }
                });
                modifiedApplicationIds.push({
                    oldValue,
                    newValue: user.applicationPolicyIds
                });
            }));

            const errors: { attributes: string[], message: string }[] = [];
            if (invalidUserIds.length) {
                errors.push({
                    attributes: invalidUserIds,
                    message: "Users not found."
                });
            }

            if (usersWithPolicyExistForResilience.length) {
                errors.push({
                    attributes: usersWithPolicyExistForResilience,
                    message: "User can have only one application policy attached for each resilience. Please detach existing policy from this users and try again."
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users are failed to attached with the specified policy.");
            }

            const modifiedProperties = await getUserApplicationModifiedProperties(modifiedApplicationIds);

            res.sendResponse({
                meta: {
                    message: "Policy attached successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.AttachPolicyToResilienceUsers");
            console.error(error);
            next(error);
        }
    });

export { router as attachPolicyToResilienceUsers };
