/* eslint-disable max-depth */
/* eslint-disable max-statements */
import express, { Request, Response, NextFunction } from "express";
import {
    currentUser,
    hasGlobalAction,
    requireAuth,
    validateRequest,
    InsufficientPrivilagesError,
    ResourceNotFoundError,
    NotFoundCode,
    SucceededPartially,
    BasicResourceValueUnacceptableConflictError,
    ConflictErrorCodes,
    responseHandler,
    TargetType,
    NonMoxfiveUserWithMfExclusiveTypesEnum,
    InternalServerError
} from "@moxfive-llc/common";
import { Policy } from "../../models/policy";
import { User } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { attachPoliciesToEntityValidation } from "./attach-policies-to-entity.validation";
import { Incident } from "../../models/incident";
import { incidentPolicyUpdatedPublisherWrapper } from "../../utils/incident-policy-updated-publisher-wrapper";
import { Tracker } from "../../models/tracker";
import { trackerPolicyUpdatedPublisherWrapper } from "../../utils/tracker-policy-updated-publisher-wrapper";
import { checkMoxfiveUserOrSuperAdminPermissions, getUserName } from "../../utils";
import { queueGroupName } from "../../events/queue-group-name";
import {
    SuperAdminPolicyAssignedNotificationPublisher
} from "../../events/publishers/super-admin-policy-assigned-notification-publisher";
import { natsWrapper } from "../../nats-wrapper";
import { getUsersNameInitialsByIds } from "../../utils/get-users-name-initials-by-ids";
import { Organization } from "../../models/organizations";
import { Action } from "../../models/action";
import {
    NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper
} from "../../utils/non-mf-user-with-mf-exclusive-permission-notification-publisher-wrapper";
import { isMOXFIVEUser } from "../../utils/is-moxfive-user";

const router = express.Router();

router.put("/v1/authorization/entities/:entityId/policies/attach",
    responseHandler,
    currentUser,
    requireAuth,
    attachPoliciesToEntityValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { entityId } = req.params;
            const { type, policyIds } = req.body;
            const targets: any = [];
            const modifiedProperties: any = [];

            const hasActionToGetPolicyDetails = await hasGlobalAction(req, "AttachPoliciesToPlatformEntity");
            if (!hasActionToGetPolicyDetails) {
                throw new InsufficientPrivilagesError();
            }

            if (!req.currentUser) {
                throw new InternalServerError();
            }

            // Check if logged in user is super admin or moxfive user.
            const isMoxfiveUserOrSuperAdmin = await checkMoxfiveUserOrSuperAdminPermissions(req, false);

            const policyDetails = await Policy.find({ _id: { $in: [...policyIds] } });
            if (!policyDetails || (policyDetails && !policyDetails.length)) {
                throw new ResourceNotFoundError(NotFoundCode.POLICY_NOT_FOUND, "Policies not found.");
            }

            // Make actions map
            const actions = await Action.find({}).select("moxfiveExclusive").lean().exec();
            const actionsMap: Map<string, boolean> = new Map();
            actions.forEach(action => actionsMap.set(String(action._id), action.moxfiveExclusive));

            // Make map of policy as moxfive exclusive permission or not
            const policiesMFExclusivePermissionsSet: Set<string> = new Set();
            policyDetails.forEach(policy => {
                // Policy should not be super admin policy and policy should have moxfive exclusive permissions
                const isMFExclusivePermissionAdded = String(policy._id) !== process.env.SUPER_ADMIN_POLICY_ID && policy.actionIds.some(action => actionsMap.get(String(action)));
                if(isMFExclusivePermissionAdded) {
                    policiesMFExclusivePermissionsSet.add(String(policy._id));
                }
            });

            const globalPolicies: string[] = [];
            const applicationPolicies: string[] = [];
            const inValidPolicyIds: string[] = [];
            const policiesIds = policyDetails.map(policy => String(policy.id));

            const policyIdToNameMap = new Map();
            policyDetails.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));

            policyIds.forEach((policyId: string) => {
                if (policiesIds.includes(policyId)) {
                    const policy = policyDetails.find(policy => policy.id === policyId);
                    if (policy) {
                        if (policy.type === "Global") {
                            globalPolicies.push(policyId);
                        }
                        else {
                            applicationPolicies.push(policyId);
                        }
                    }
                }
                else {
                    inValidPolicyIds.push(policyId);
                }
            });

            if (type === "User") {
                const user = await User.findById(entityId);
                if (!user) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    // Can only assign permission to users of his organization only.
                    if (req.currentUser.organizationId !== String(user.organizationId)) throw new InsufficientPrivilagesError();
                }

                if (applicationPolicies.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.APPLICATION_TYPE_CONFLICT, "Application Type policy cannot attached to user.");
                }

                const newPolicyIds = new Set(user.policyIds ? [...user.policyIds].map(String) : []);

                const isSuperAdminPolicyExist = policyIds.includes(process.env.SUPER_ADMIN_POLICY_ID);
                const isSuperAdminAlreadyAssigned = newPolicyIds.has(process.env.SUPER_ADMIN_POLICY_ID || "");

                // For audit logs
                const oldValue = [...newPolicyIds];
                globalPolicies.forEach(newPolicyIds.add, newPolicyIds);
                user.policyIds = [...newPolicyIds];
                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);

                // Check whether we should send super admin policy and moxfive exclusive notification
                const shouldSendSuperAdminPolicyNotification = isSuperAdminPolicyExist && !isSuperAdminAlreadyAssigned;
                const shouldSendMoxfiveExclusiveNotification = policiesMFExclusivePermissionsSet.size && !isMOXFIVEUser({
                    req,
                    organizationId: String(user.organizationId),
                    throwError: false,
                });

                // If either super admin notification or should send moxfive exclusive notification send
                if(shouldSendSuperAdminPolicyNotification || shouldSendMoxfiveExclusiveNotification) {
                    // Fetch user info of entity and logged-in user
                    const userIds = [entityId];
                    if(req.currentUser?.id) {
                        userIds.push(req.currentUser.id);
                    }
                    const usersMap = await getUsersNameInitialsByIds(userIds);
                    const entity = usersMap.get(entityId);

                    if(entity) {
                        // Fetch entity organization
                        const organization = await Organization.findById(user.organizationId).select("name").lean().exec();

                        if(shouldSendSuperAdminPolicyNotification) {
                            const data = {
                                recipientUserIds: [entityId],
                                serviceName: queueGroupName,
                                assignedToUsers: [{
                                    name: entity.name,
                                    organization: organization?.name || ""
                                }],
                                assignedByName: usersMap.get(req.currentUser?.id)?.name || "",
                                assignedByInitials: usersMap.get(req.currentUser?.id)?.initials || ""
                            };

                            await new SuperAdminPolicyAssignedNotificationPublisher(natsWrapper.client).publish(data);
                        }

                        if(shouldSendMoxfiveExclusiveNotification) {
                            // Fetch policy names that has MF Exclusive permission assigned
                            const policiesNames:string[] = [];
                            policiesMFExclusivePermissionsSet.forEach(policy => {
                                const name = policyIdToNameMap.get(String(policy));
                                if(name) {
                                    policiesNames.push(name);
                                }
                            });

                            // Prepare data and emit event
                            const data = {
                                recipientUserIds: [entityId],
                                serviceName: queueGroupName,
                                assignedToUsers: [{
                                    name: entity.name,
                                    organization: organization?.name || "",
                                    email: user.email
                                }],
                                assignedByName: usersMap.get(req.currentUser?.id)?.name || "",
                                assignedByInitials: usersMap.get(req.currentUser?.id)?.initials || "",
                                policies: policiesNames,
                                type: NonMoxfiveUserWithMfExclusiveTypesEnum.SINGLE_USER,
                            };

                            await NonMfUserWithMfExclusivePermissionNotificationPublisherWrapper(data);

                        }
                    }
                }

                const newValue = user.policyIds.map(String);
                const idsToFetch: string[] = oldValue.filter(policyId => !globalPolicies.includes(policyId));
                if (idsToFetch.length) {
                    const fetchedPolicies = await Policy.find({ _id: { $in: idsToFetch } }, { name: 1 }).lean().exec();
                    fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                }

                // For Audit logs
                targets.push({
                    type: TargetType.USER,
                    details: {
                        id: user.id,
                        name: getUserName({ firstName: user.firstName, lastName: user.lastName, displayName: user.displayName }),
                        email: user.email,
                        azureId: ""
                    }
                });
                modifiedProperties.push({
                    target: TargetType.USER,
                    propertyName: "policyIds",
                    oldValue: JSON.stringify(oldValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    }))),
                    newValue: JSON.stringify(newValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    })))
                });
            }
            else if (type === "Incident") {
                const incident = await Incident.findById(entityId);
                if (!incident) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    const matcheduser = (incident.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                    if (!matcheduser) throw new InsufficientPrivilagesError();
                }

                if (globalPolicies.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_CONFLICT, "Global Type policy cannot attached to incident.");
                }

                const oldValue = [...incident.policyIds.map(String)];
                const newPolicyIds = [...new Set([...oldValue, ...applicationPolicies])];
                incident.policyIds = newPolicyIds;
                await incident.save();
                await incidentPolicyUpdatedPublisherWrapper(incident);

                const newValue = incident.policyIds.map(String);
                const idsToFetch: string[] = oldValue.filter(policyId => !applicationPolicies.includes(policyId));
                if (idsToFetch.length) {
                    const fetchedPolicies = await Policy.find({ _id: { $in: idsToFetch } }, { name: 1 }).lean().exec();
                    fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                }

                // For audit logs
                targets.push({
                    type: TargetType.INCIDENT,
                    details: {
                        id: incident.id,
                        name: incident.name
                    }
                });
                modifiedProperties.push({
                    target: TargetType.INCIDENT,
                    propertyName: "policyIds",
                    oldValue: JSON.stringify(oldValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    }))),
                    newValue: JSON.stringify(newValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    })))
                });
            }
            else if (type === "Resilience") {
                const resilience = await Tracker.findById(entityId);
                if (!resilience) {
                    throw new ResourceNotFoundError(NotFoundCode.ENTITY_NOT_FOUND, "Specified entity is not found in the list of eligible entities for policy.");
                }

                if (!isMoxfiveUserOrSuperAdmin) {
                    const matcheduser = (resilience.members ?? []).find(member => String(member) === String(req.currentUser?.id));
                    if (!matcheduser) throw new InsufficientPrivilagesError();
                }

                if (globalPolicies.length) {
                    throw new BasicResourceValueUnacceptableConflictError(ConflictErrorCodes.GLOBAL_TYPE_CONFLICT, "Global Type policy cannot attached to resilience.");
                }

                const oldValue = [...resilience.policyIds.map(String)];
                const newPolicyIds = [...new Set([...resilience.policyIds.map(String), ...applicationPolicies])];
                resilience.policyIds = newPolicyIds;
                await resilience.save();
                await trackerPolicyUpdatedPublisherWrapper(resilience);

                const newValue = resilience.policyIds.map(String);
                const idsToFetch: string[] = oldValue.filter(policyId => !applicationPolicies.includes(policyId));
                if (idsToFetch.length) {
                    const fetchedPolicies = await Policy.find({ _id: { $in: idsToFetch } }, { name: 1 }).lean().exec();
                    fetchedPolicies.forEach(policy => policyIdToNameMap.set(String(policy.id), policy.name));
                }

                // For audit logs
                targets.push({
                    type: TargetType.BUSINESSRESILIENCE,
                    details: {
                        id: resilience.id,
                        name: resilience.name
                    }
                });
                modifiedProperties.push({
                    target: TargetType.BUSINESSRESILIENCE,
                    propertyName: "policyIds",
                    oldValue: JSON.stringify(oldValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    }))),
                    newValue: JSON.stringify(newValue.map(value => ({
                        id: value,
                        value: policyIdToNameMap.get(String(value))
                    })))
                });
            }

            if (inValidPolicyIds.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: inValidPolicyIds,
                        message: "Policies not found."
                    }]
                }], "One or more policies are failed to attach with the specified entity.");
            }

            res.sendResponse({
                meta: {
                    message: "Policies attached to entity successfully."
                }
            }, {
                targets,
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authorization.AttachPoliciesToEntity");
            console.error(error);
            next(error);
        }
    });

export { router as attachPoliciesToEntity };
