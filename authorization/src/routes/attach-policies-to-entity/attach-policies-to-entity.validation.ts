import { body, param } from "express-validator";
import { isValidMongoObjectId } from "../../utils";

export const attachPoliciesToEntityValidation = [
    param("entityId")
        .isMongoId(),

    body("type")
        .exists().bail()
        .custom(value => {
            if (!["User", "Incident", "Resilience"].includes(value)) {
                throw new Error("Policy entity type can be any from this list only: User, Incident, Resilience.");
            }
            else {
                return true;
            }
        }),

    body("policyIds")
        .isArray({ min: 1, max: 100 })
        .withMessage("No policy found to be attached to the specified entity.")
        .custom((policyIds: string[]) => {
            return policyIds.every(policyId => {
                return isValidMongoObjectId(policyId);
            });
        }).withMessage("This are invalid policy ids.")
];
