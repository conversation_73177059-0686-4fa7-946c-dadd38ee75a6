/* eslint-disable no-await-in-loop */
import { Listener, Subjects, TrackerDeletedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { Tracker } from "../../models/tracker";
import { User } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { queueGroupName } from "../queue-group-name";

export class TrackerDeletedListener extends Listener<TrackerDeletedEvent> {
    subject: Subjects.TrackerDeleted = Subjects.TrackerDeleted;
    queueGroupName = queueGroupName;

    async onMessage(data: TrackerDeletedEvent["data"], msg: JsMsg) {
        const { id } = data;

        await Tracker.findByIdAndDelete(id).lean().exec();
        msg.ack();

        const usersWithTrackerAssignedPolicy = await User.find({ "applicationPolicyIds.applicationId": id });
        for (const user of usersWithTrackerAssignedPolicy) {
            // Find policy with application id which is same as incident id
            const policyIndex = user.applicationPolicyIds.findIndex(policy => String(policy.applicationId) === String(id));

            // If policy found then remove that policy
            if (policyIndex !== -1) {
                user.applicationPolicyIds.splice(policyIndex, 1);
                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);
            }
        }
    }
}
