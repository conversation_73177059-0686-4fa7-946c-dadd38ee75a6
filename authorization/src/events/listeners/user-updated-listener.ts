import { Listener, Subjects, UserUpdatedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { User } from "../../models/users";
import { queueGroupName } from "../queue-group-name";

export class UserUpdatedListener extends Listener<UserUpdatedEvent> {
    subject: Subjects.UserUpdated = Subjects.UserUpdated;
    queueGroupName = queueGroupName;

    async onMessage(data: UserUpdatedEvent["data"], msg: JsMsg) {
        const user = await User.findById(data.id).lean().exec();
        if(user && user.version >= data.version) {
            msg.ack();
            return;
        }

        await User.findByIdAndUpdate(data.id, {
            _id: data.id,
            firstName: data.firstName,
            lastName: data.lastName,
            name: data.name,
            email: data.email,
            displayName: data.displayName,
            organizationId: data.organizationId,
            isEnabled: data.isEnabled,
            keys: data.keys,
            allowedLoginTokens: data.allowedLoginTokens,
            version: data.version
        }, { upsert: true });
        msg.ack();
    }
}
