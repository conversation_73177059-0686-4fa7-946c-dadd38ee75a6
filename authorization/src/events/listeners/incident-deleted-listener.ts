/* eslint-disable no-await-in-loop */
import { Listener, Subjects, IncidentDeletedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { Incident } from "../../models/incident";
import { User } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";

export class IncidentDeletedListener extends Listener<IncidentDeletedEvent> {
    subject: Subjects.IncidentDeleted = Subjects.IncidentDeleted;
    queueGroupName = queueGroupName;

    async onMessage(data: IncidentDeletedEvent["data"], msg: JsMsg) {
        const { id } = data;

        await Incident.findByIdAndDelete(id).lean().exec();
        msg.ack();

        const usersWithIncidentAssignedPolicy = await User.find({ "applicationPolicyIds.applicationId": id });
        for (const user of usersWithIncidentAssignedPolicy) {
            // Find policy with application id which is same as incident id
            const policyIndex = user.applicationPolicyIds.findIndex(policy => String(policy.applicationId) === String(id));

            // If policy found then remove that policy
            if (policyIndex !== -1) {
                user.applicationPolicyIds.splice(policyIndex, 1);
                await user.save();
                await userPolicyUpdatedPublisherWrapper(user);
            }
        }
    }
}
