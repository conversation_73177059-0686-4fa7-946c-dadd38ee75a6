/* eslint-disable no-await-in-loop  */
import { Listener, Subjects, ResourceNotFoundError, NotFoundCode, TrackerMembersRemovedEvent } from "@moxfive-llc/common";
import { JsMsg } from "nats";
import { queueGroupName } from "../queue-group-name";
import { User, UserDoc } from "../../models/users";
import { userPolicyUpdatedPublisherWrapper } from "../../utils/user-policy-updated-publisher-wrapper";
import { Tracker } from "../../models/tracker";

export class TrackerMembersRemovedListener extends Listener<TrackerMembersRemovedEvent> {
    subject: Subjects.TrackerMembersRemoved = Subjects.TrackerMembersRemoved;
    queueGroupName = queueGroupName;

    async onMessage(data: TrackerMembersRemovedEvent["data"], msg: JsMsg) {
        const tracker = await Tracker.findById(data.id).lean().exec();
        if(!tracker) {
            throw new ResourceNotFoundError(NotFoundCode.TRACKER_NOT_FOUND, "Tracker not found.");
        }
        // If current version is greater than provided version then acknowledge the event directly
        if(tracker.version >= data.version) {
            msg.ack();
            return;
        }
        const { id, removedMembers } = data;

        await Tracker.findByIdAndUpdate(data.id, {
            _id: data.id,
            members: data.members,
            version: data.version
        });
        msg.ack();
        // Fetch users and make usersMap
        const users = await User.find({ _id: { $in: removedMembers } });
        const usersMap: Map<string, UserDoc> = new Map();
        users.forEach(user => {
            usersMap.set(String(user._id), user);
        });

        // Loop through all removedMembers
        for(const member of removedMembers) {
            // Fetch user details from map and if user found then continue
            const user = usersMap.get(member);

            if(user) {
                // Find policy with application id which is same as incident id
                const policyIndex = user.applicationPolicyIds.findIndex(policy => String(policy.applicationId) === id);

                // If policy found then remove that policy
                if(policyIndex !== -1) {
                    user.applicationPolicyIds.splice(policyIndex, 1);
                    await user.save();
                    await userPolicyUpdatedPublisherWrapper(user);
                }
            }
        }

        msg.ack();
    }
}
