import { Publisher, Subjects, NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationEvent } from "@moxfive-llc/common";
import { queueGroupName } from "../queue-group-name";

export class NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationPublisher extends Publisher<NonMoxfiveUserWithMoxfiveExclusivePermissionNotificationEvent> {
    subject: Subjects.NonMoxfiveUserWithMoxfiveExclusivePermissionNotification = Subjects.NonMoxfiveUserWithMoxfiveExclusivePermissionNotification;
    queueGroupName = queueGroupName;
}
