import { Publisher, Subjects, SuperAdminPolicyAssignedNotificationEvent } from "@moxfive-llc/common";
import { queueGroupName } from "../queue-group-name";

export class SuperAdminPolicyAssignedNotificationPublisher extends Publisher<SuperAdminPolicyAssignedNotificationEvent> {
    subject: Subjects.SuperAdminPolicyAssignedNotification = Subjects.SuperAdminPolicyAssignedNotification;
    queueGroupName = queueGroupName;
}
