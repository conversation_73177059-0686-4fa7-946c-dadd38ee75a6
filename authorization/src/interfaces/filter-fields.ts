import { ValueTypesEnum } from "../enums/value-types.enum";

export interface FilterFields {
    displayName?: string,
    name: string,
    isFlexibleField: boolean,
    key?: string | null,
    allowedFilters: {label: string, value: string}[],
    type: ValueTypesEnum,
    assetStatusField?: boolean,
    ownerField?: boolean,
    categoryType?: boolean,
    values?: {id: string, value: string}[]  | null,
    ignoreSanitize?: boolean,
    organizations?: boolean,
    policyTypes?: boolean,
    applicationPolicyModules?: boolean,
    isBooleanField?: boolean,
    isQuickFilter?: boolean
}
