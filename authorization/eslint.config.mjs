import typescriptEslint from "@typescript-eslint/eslint-plugin";
import security from "eslint-plugin-security";
import globals from "globals";
import tsParser from "@typescript-eslint/parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default [{
    ignores: ["**/node_modules", "**/dist", "**/build"],
}, ...compat.extends(
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    // "plugin:@typescript-eslint/eslint-recommended", //  this is deprecated
), {
    plugins: {
        "@typescript-eslint": typescriptEslint,
        security
    },

    languageOptions: {
        globals: {
            ...globals.node,
        },

        parser: tsParser,
    },

    rules: {
        ...security.configs.recommended.rules,
        "@typescript-eslint/no-explicit-any": "off",
        "array-bracket-spacing": [2, "never"],
        "brace-style": ["error", "stroustrup"],
        "computed-property-spacing": [2, "never"],
        "eol-last": 2,
        "max-depth": [1, 4],
        "max-statements": [1, 60],
        "no-mixed-spaces-and-tabs": 2,
        "object-curly-spacing": ["error", "always"],
        "block-scoped-var": "error",

        camelcase: ["error", {
            properties: "never",
        }],

        "comma-dangle": "off",

        "comma-spacing": ["error", {
            before: false,
            after: true,
        }],

        "comma-style": ["error", "last"],
        "consistent-return": "off",
        curly: ["error", "multi-line"],
        "dot-location": ["error", "property"],
        eqeqeq: ["error", "allow-null"],
        "guard-for-in": "error",

        indent: ["error", 4, {
            SwitchCase: 1,
        }],

        "key-spacing": ["error", {
            beforeColon: false,
            afterColon: true,
        }],

        "max-len": ["error", 180, 2, {
            ignoreComments: true,
            ignoreUrls: true,
        }],

        "no-multiple-empty-lines": ["error", {
            max: 1,
            maxEOF: 1,
        }],

        "callback-return": ["error", ["done", "response.sendError", "response.sendResponse"]],
        "handle-callback-err": "error",

        "no-console": ["error", {
            allow: ["error", "info", "warn"],
        }],

        "no-bitwise": "off",
        "no-caller": "error",
        "no-cond-assign": "off",
        "no-const-assign": "error",
        "no-debugger": "error",
        "no-empty": "error",
        "no-eval": "error",
        "no-extend-native": "error",
        "no-extra-parens": "off",
        "no-extra-semi": ["error"],
        "no-global-assign": "error",
        "no-irregular-whitespace": "error",
        "no-iterator": "error",
        "no-loop-func": "off",
        "no-multi-spaces": "off",
        "no-multi-str": "error",
        "no-nested-ternary": "error",
        "no-new": "off",
        "no-path-concat": "off",
        "no-proto": "error",
        "no-redeclare": "error",
        "no-return-assign": "off",
        "no-sync": "error",
        "no-script-url": "error",
        "no-sequences": "error",
        "no-shadow": "off",
        "no-trailing-spaces": "error",
        "no-undef": "error",
        "no-underscore-dangle": "off",
        "no-unused-expressions": "off",
        "no-var": "error",
        "no-with": "error",
        "one-var": ["error", "never"],
        "prefer-const": "error",

        quotes: ["error", "double", {
            allowTemplateLiterals: true,
        }],

        "semi-spacing": ["error", {
            before: false,
            after: true,
        }],

        semi: ["error", "always"],
        "space-before-blocks": ["error", "always"],

        "space-before-function-paren": ["error", {
            anonymous: "always",
            named: "never",
        }],

        "space-in-parens": ["error", "never"],

        "space-infix-ops": ["error", {
            int32Hint: false,
        }],

        "space-unary-ops": ["error"],
        strict: ["error", "never"],
        "valid-typeof": "error",
        "wrap-iife": ["error", "outside"],
        yoda: "off",
        "global-require": "error",
        "no-param-reassign": "error",
        "no-prototype-builtins": "off",
        "no-useless-catch": "off",
        "require-atomic-updates": "off",
        "no-async-promise-executor": "off",
        "no-await-in-loop": ["error"],
    },
}];
