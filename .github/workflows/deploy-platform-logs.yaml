name: deploy-platform-logs-dev

on:
  push:
    paths:
      - "platform-logs/**"

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
      id-token: write
    steps:
      - name: "Checkout code"
        uses: actions/checkout@v2

      - if: github.ref == 'refs/heads/local_development'
        name: "Build docker image for local development"
        run: cd platform-logs && docker build --build-arg GITHUB_TOKEN=$GITHUB_TOKEN -t moxfive/platform-logs:development .
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_TOKEN }}

      - if: github.ref == 'refs/heads/dev'
        name: "Build docker image for dev"
        run: cd platform-logs && docker build --build-arg GITHUB_TOKEN=$GITHUB_TOKEN -t moxfive/platform-logs:dev .
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_TOKEN }}

      - if: github.ref == 'refs/heads/test'
        name: "Build docker image for test"
        run: cd platform-logs && docker build --build-arg GITHUB_TOKEN=$GITHUB_TOKEN -t moxfive/platform-logs:test .
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_TOKEN }}

      - if: github.ref == 'refs/heads/demo'
        name: "Build docker image for demo"
        run: cd platform-logs && docker build --build-arg GITHUB_TOKEN=$GITHUB_TOKEN -t moxfive/platform-logs:demo .
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_TOKEN }}

      - if: github.ref == 'refs/heads/prod'
        name: "Build docker image for prod"
        run: cd platform-logs && docker build --build-arg GITHUB_TOKEN=$GITHUB_TOKEN -t moxfive/platform-logs:prod .
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_TOKEN }}

      - name: "Login to docker hub"
        run: docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

      - if: github.ref == 'refs/heads/local_development'
        name: "Push docker image to docker hub with tag development"
        run: docker push moxfive/platform-logs:development

      - if: github.ref == 'refs/heads/dev'
        name: "Push docker image to docker hub with tag dev"
        run: docker push moxfive/platform-logs:dev

      - if: github.ref == 'refs/heads/test'
        name: "Push docker image to docker hub with tag test"
        run: docker push moxfive/platform-logs:test

      - if: github.ref == 'refs/heads/demo'
        name: "Push docker image to docker hub with tag demo"
        run: docker push moxfive/platform-logs:demo

      - if: github.ref == 'refs/heads/prod'
        name: "Push docker image to docker hub with tag prod"
        run: docker push moxfive/platform-logs:prod

      - if: github.ref == 'refs/heads/dev'
        name: "Login to dev cluster"
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_DEV_CREDENTIALS }}"

      - if: github.ref == 'refs/heads/dev'
        name: "Dev deployment"
        run: az aks command invoke --resource-group rg-phoenix-dev-001 --name phoenix-dev --command "kubectl rollout restart deployment platform-logs-depl -n default"

      - if: github.ref == 'refs/heads/test'
        name: "Login to QA cluster"
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_TEST_CREDENTIALS }}"

      - if: github.ref == 'refs/heads/test'
        name: "QA deployment"
        run: az aks command invoke --resource-group rg-phoenix-test-001 --name phoenix-test --command "kubectl rollout restart deployment platform-logs-depl -n default"

      - if: github.ref == 'refs/heads/demo'
        name: "Login to demo cluster"
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_DEMO_CREDENTIALS }}"

      - if: github.ref == 'refs/heads/demo'
        name: "Demo deployment"
        run: az aks command invoke --resource-group rg-phoenix-demo-001 --name phoenix-demo --command "kubectl rollout restart deployment platform-logs-depl -n default"

      - if: github.ref == 'refs/heads/prod'
        name: "Login to prod cluster"
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_PROD_CREDENTIALS }}"

      - if: github.ref == 'refs/heads/prod'
        name: "Prod deployment"
        run: az aks command invoke --resource-group rg-phoenix-prod-001 --name phoenix-prod --command "kubectl rollout restart deployment platform-logs-depl -n default"
