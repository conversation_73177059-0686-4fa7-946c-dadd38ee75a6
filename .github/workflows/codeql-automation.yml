name: "CodeQL Automation"

on:
  pull_request:
    paths:
      - "automation/**"

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: 'ubuntu-latest'
    permissions:
      security-events: write
      packages: read
      actions: read
      contents: read
    strategy:
      fail-fast: false
      matrix:
        include:
          - language: go
            build-mode: autobuild
          - language: javascript-typescript
            build-mode: none
          - language: actions
            build-mode: none

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Checkout Custom CodeQL Queries Repository
        uses: actions/checkout@v4
        with:
          repository: moxfive-llc/codeql-custom-rules
          path: queries
          token: ${{ secrets.PACKAGE_TOKEN }}

      - if: matrix.language == 'javascript-typescript'
        name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          build-mode: ${{ matrix.build-mode }}
          config: |
            disable-default-queries: true
            queries:
              - uses: security-extended
              - uses: ./queries
            paths:
              - ./automation

      - if: matrix.language == 'javascript-typescript'
        name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"
          output: codeql-results.sarif
          upload: never

      - name: Install dependencies
        run: npm install @octokit/rest

      - if: matrix.language == 'javascript-typescript'
        name: Extract CodeQL Issue Counts
        run: |
          node codeql.js
          OUTPUT=$(node codeql.js)
          read -r CRITICAL HIGH MEDIUM LOW <<< "$OUTPUT"
          echo "CRITICAL=$CRITICAL" >> $GITHUB_ENV
          echo "HIGH=$HIGH" >> $GITHUB_ENV
          echo "MEDIUM=$MEDIUM" >> $GITHUB_ENV
          echo "LOW=$LOW" >> $GITHUB_ENV
        env:
          GITHUB_REF: ${{ github.ref }}
          GITHUB_BASE_REF: ${{ github.base_ref }}
          PACKAGE_TOKEN: ${{ secrets.PACKAGE_TOKEN }}
          GITHUB_REPOSITORY: ${{ github.repository }}

      - if: matrix.language == 'javascript-typescript'
        name: Upload Serif file
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: codeql-results.sarif

      - if: matrix.language == 'javascript-typescript'
        name: Fail if Severity Issues Exist
        run: |
          if [[ "$CRITICAL" -gt 0 || "$HIGH" -gt 0 || "$MEDIUM" -gt 0 || "$LOW" -gt 0 ]]; then
            echo "❌ severity issues found! Failing the build."
            exit 1
          fi
          echo "✅ No severity issues found. Proceeding..."
        env:
          CRITICAL: ${{ env.CRITICAL }}
          HIGH: ${{ env.HIGH }}
          MEDIUM: ${{ env.MEDIUM }}
          LOW: ${{ env.LOW }}
