name: deploy-manifests
on:
  push:
    paths:
      - "infra/k8s/**"
      - "infra/k8s-dev/**"
      - "infra/k8s-test/**"
      - "infra/k8s-demo/**"
      - "infra/k8s-prod/**"
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
      id-token: write
    steps:
      - uses: actions/checkout@v2
      - if: github.ref == 'refs/heads/dev'
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_DEV_CREDENTIALS }}"
      - if: github.ref == 'refs/heads/dev'
        run: cd infra && az aks command invoke --resource-group rg-phoenix-dev-001 --name phoenix-dev --command "kubectl apply -f k8s && kubectl apply -f k8s-dev" --file .

      - if: github.ref == 'refs/heads/test'
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_TEST_CREDENTIALS }}"
      - if: github.ref == 'refs/heads/test'
        run: cd infra && az aks command invoke --resource-group rg-phoenix-test-001 --name phoenix-test --command "kubectl apply -f k8s && kubectl apply -f k8s-test" --file .

      - if: github.ref == 'refs/heads/demo'
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_DEMO_CREDENTIALS }}"
      - if: github.ref == 'refs/heads/demo'
        run: cd infra && az aks command invoke --resource-group rg-phoenix-demo-001 --name phoenix-demo --command "kubectl apply -f k8s && kubectl apply -f k8s-demo" --file .

      - if: github.ref == 'refs/heads/prod'
        uses: azure/login@cb79c773a3cfa27f31f25eb3f677781210c9ce3d
        with:
          creds: "${{ secrets.AZURE_PROD_CREDENTIALS }}"
      - if: github.ref == 'refs/heads/prod'
        run: cd infra && az aks command invoke --resource-group rg-phoenix-prod-001 --name phoenix-prod --command "kubectl apply -f k8s && kubectl apply -f k8s-prod" --file .
