name: eslint-notification

on:
  pull_request:
    paths:
      - "notification/**"

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    steps:
      - uses: actions/checkout@v2
      - shell: bash
        env:
          CI_ACCESS_TOKEN: ${{ secrets.PACKAGE_TOKEN }}
        run: |
          npm install -g npm-cli-login
          npm-cli-login -u "nishit-rapidops" -p $CI_ACCESS_TOKEN -e "<EMAIL>" -r "https://npm.pkg.github.com" -s "@moxfive-llc"
          cd notification && npm install && npm run lint
