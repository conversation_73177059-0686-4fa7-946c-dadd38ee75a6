version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/agreement"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/authentication"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/authorization"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/automation"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/client"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/dashboard"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/incident"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/keyvault"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/moxfive-insight"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/nats-test"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/notification"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/nps"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/oauth"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/platform-settings"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/resilience"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "npm"
    directory: "/system-maintenance"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
  - package-ecosystem: "gomod"
    directory: "/platform-logs"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
