import React, { useEffect, useState } from 'react';
import { getFeatures } from './app-wrapper.service';
import { ModuleName } from '../../constants/constant';
import { useAppDispatch } from '../../redux/hooks';
import { CurrentVersions } from '../../redux/slices/current-version';


export const AppContext =  React.createContext(null);

export const AppWrapper = ({ children }) => {

  const [currentVersion, setCurrentVersion] = useState(null);
  const dispatch = useAppDispatch();
  useEffect(() => {
    (async () => {
      try {
        const res = await getFeatures();
        setCurrentVersion(res.find(({ name }) => name === ModuleName.authentication )?.currentVersion);
        dispatch(CurrentVersions({ currentVersion : res?.find(({ name }) => name === ModuleName?.authentication )?.currentVersion }));
      } catch (e) {

      }
    })();

  }, []);
  return (
        <>
            <AppContext.Provider value={{
              currentVersion,
            }} >
                {
                    currentVersion ? (
                        <>
                            {children}

                        </>
                    ) : ''
                }
            </AppContext.Provider>
        </>
  );
};
