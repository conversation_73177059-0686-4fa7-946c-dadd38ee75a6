import React from 'react';
import Description from '../../../../../shared/components/description/description';
import AuditLogsSectionComponent from '../section-component/audit-logs-section-component';

function TargetsTab({ data }) {
  const renderSections = () => {
    return (
      <>
          {
              data.map(d => {
                return (
                      <AuditLogsSectionComponent title={d.Title} sectionType={'Detail'}>
                          <>
                              {Object.keys(d).map((key, index) => {
                                return <Description key={index} title={key} desc={d[key]} />;
                              })}
                          </>
                      </AuditLogsSectionComponent>
                );
              })
          }
      </>
    );
  };
  return (
      <>
          <div className={'m-t-lg'}>
              {
                  data !== null ? renderSections() : <span className={'text-center block'}>No Data Found </span>
              }
          </div>
      </>
  );
}

export default TargetsTab;
