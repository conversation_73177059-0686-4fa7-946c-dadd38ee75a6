import React, { useEffect, useRef, useState } from 'react';
import classes from '../audit-logs.module.scss';
import { TableColumn } from '../../../shared/components/platform-logs-table/api-table.types';
import { AuthenticationLogsColumn } from '../../columns/authentication-logs.column';
import { fetchAuthenticationLogs, fetchAuthenticationLogsFilters } from '../../audit-logs.service';
import { formatFieldValueForFilterDropdown } from '../../../shared/components/table/filter/filter.service';
import PlatformLogsTableData from '../../../shared/components/platform-logs-table/api-table';
import { DEFAULT_VALUE, GRID, GRID_DISPLAY_NAME } from '../../../../constants/constant';
import AuthenticationLogsDetailSideBar from './authentication-logs-detail/authentication-logs-detail';
import { formatCreatedDate } from '../../../../utils/utils';

function AuthenticationLogs() {
  const tasksRef = useRef(null);
  const detailSidebarRef = useRef(null);
  const [selectedLogDetails, setSelectedLogDetails] = useState(null);
  const [nextLink, setnextLink] = useState(null);
  const [isShowLocalTime, setShowLocalTime] = useState(true);

  const organizationCellRenderer = (e) =>{
    const { organization } = e.data;
    return (
        <span>{organization?.value || DEFAULT_VALUE}</span>
    );
  };

  const userCellRenderer = (e) =>{
    const { user } = e.data;
    return (
        <span>{user?.value || DEFAULT_VALUE}</span>
    );
  };

  const resultCellRenderer = (e) =>{
    const { isFailure } = e.data;
    return (
        <span>{isFailure ? 'Failed' : 'Success'}</span>
    );
  };

  //Define Column
  const columns: TableColumn[] = AuthenticationLogsColumn({ isShowLocalTime, organizationCellRenderer, userCellRenderer, resultCellRenderer });

  // This function is to toggle detail sidebar
  const showDetailSidebar = () => {
    detailSidebarRef.current.showSidebar();
  };
  const hideDetailSidebar = () => {
    setSelectedLogDetails(null);
    detailSidebarRef.current.hideSidebar();
  };
  const onPageChanged = () => {
  };
  const getAuthenticationLogs = async (filterData) => {
    if (filterData.apiURL === nextLink && nextLink !== undefined && nextLink !== null) {
      return;
    }
    setnextLink(filterData?.apiURL);
    return fetchAuthenticationLogs({ filterData });
  };
  const fetchFilterOption = async () => {
    try {
      const filters = await fetchAuthenticationLogsFilters();
      if (filters) {
        return formatFieldValueForFilterDropdown(filters);
      } else {
        throw new Error();
      }
      return [];
    } catch (e) {
      console.info(e);
      return [];
    }
  };

  const prepareDetailData = (data) =>{
    return {
      ...data,
      status:data.isFailure ? 'Failed' : 'Success',
      timestamp:formatCreatedDate(data.timestamp, true),
      organization: data.organization?.value,
      user:data.user?.value,
    };
  };
  const goToDetailPage = (event) => {
    setSelectedLogDetails(prepareDetailData(event.data));
    showDetailSidebar();
    if (tasksRef.current) {
      tasksRef.current.closeAllFilterDialog();
    }
  };
    //handles back button and close all sidebars that are open
  const onBackButtonEvent = (e) => {
    e.preventDefault();
    hideDetailSidebar();
  };
  useEffect(() => {
    window.history.pushState(null, null, window.location.pathname);
    window.addEventListener('popstate', onBackButtonEvent);
    return () => {
      window.removeEventListener('popstate', onBackButtonEvent);
    };
  }, []);
  const handleCallBack = (callBackType, value) => {
    if (callBackType === 'changeTimeZone') {
      setShowLocalTime(value);
    }

  };

  return (
        <div className={classes.sectionmain} id={'sectionmain'}>
            <div className={`${classes.sectiontitle} clearfix`}>
                <h2>Authentication Logs (Auth0)</h2>
            </div>
            <div className={`${classes.sectiontop} clearfix`}>
                <div className="UserGridList pos-rlt">
                  {
                    columns ? (
                        <PlatformLogsTableData
                            defaultSortField={[
                              {
                                columnName:'timestamp',
                                sort:'desc',
                              },
                            ]}
                            ref={tasksRef}
                            gridDataService={getAuthenticationLogs}
                            columns={columns}
                            gridName={GRID.AuthenticationLogs}
                            displayGridName={GRID_DISPLAY_NAME.AuthenticationLogs}
                            notFoundMsg={`No ${GRID_DISPLAY_NAME.AuthenticationLogs} found`}
                            onViewRow={goToDetailPage}
                            onRowSelected={null}
                            onPageChanged={onPageChanged}
                            filterData={fetchFilterOption}
                            callBack = {handleCallBack}
                            checkboxSelection={false}
                        />
                    )  : <></>
                  }

                </div>
                <AuthenticationLogsDetailSideBar ref={detailSidebarRef} selectedLogDetails={selectedLogDetails}/>
            </div>
        </div>
  );
}

export default AuthenticationLogs;
