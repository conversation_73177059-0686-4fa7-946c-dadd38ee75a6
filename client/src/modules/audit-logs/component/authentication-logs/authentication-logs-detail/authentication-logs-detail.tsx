import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
} from 'react';
import UseComponentVisible from '../../../../shared/components/component-visible/component-visible';
import CloseIcon from '../../../../shared/icons/close-icon';
import classes from './authentication-logs-sidebar.module.scss';
import RightSidebarComponent from '../../../../shared/components/right-sidebar-component/right-sidebar-component';
import { DATA_ATTRIBUTE_FOR_SIDEBAR_COLUMNS_TITLE } from '../../../../../constants/data-attribute-constant';
import General from '../authentication-logs-detail/detail/general';
import { toggleBackdropClassForSidebar } from '../../../../../utils/utils';

interface AuditLogsSideBarProps {
  selectedLogDetails: any
}

const AuthenticationLogsDetailSideBar = forwardRef((props: AuditLogsSideBarProps, forwardedRef) => {
  const { selectedLogDetails } = props;
  const { ref, isComponentVisible, setIsComponentVisible } =
        UseComponentVisible({ makeEscapeActive: true });

  useImperativeHandle(forwardedRef, () => ({
    hideSidebar() {
      setIsComponentVisible(false);
    },
    showSidebar() {
      setIsComponentVisible(true);
    },
  }));

  useEffect(() => {
    toggleBackdropClassForSidebar({ addClass: isComponentVisible });
  }, [isComponentVisible]);

  return (
        <>
            {
                <div ref={ref}>
                    {isComponentVisible ? (
                        <RightSidebarComponent>
                            <div className={`${classes.sidecolumns} timelinesidecolumns`}>
                                <div className={classes.sidecolumnstiteMain}>
                                    <div className={`${classes.sidecolumnstite}`} data-idForSMH={DATA_ATTRIBUTE_FOR_SIDEBAR_COLUMNS_TITLE}>
                                        <div className={classes.sidecolumnstiteinner}>
                                            <span>{selectedLogDetails.type}</span>
                                        </div>
                                        <a href={'javascript:void(0)'}
                                            onClick={() => {
                                              setIsComponentVisible(false);
                                            }
                                            }
                                            className={`${classes.closeicon}`}
                                        >
                                            <CloseIcon />
                                        </a>
                                    </div>
                                    <div className={`${classes.sectionmain} sectionmain`} id={'sectionmain'}>
                                        <div>
                                            <General data={selectedLogDetails} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </RightSidebarComponent>
                    ) : (
                        <></>
                    )}
                </div>
            }
        </>
  );

});

export default AuthenticationLogsDetailSideBar;
