@import "../../../../../styles/variables";

.sectionmain{
  padding-top: 0px !important;
  padding:32px 32px 32px 32px;
  //height: calc(100vh - 64px);
  //overflow-y: auto;
  .sectiontitle{
    display: inline-block;
    width: 100%;
    h2{
      display: inline-block;
    }
  }
}
.sectiontop{
  margin-bottom: 25px;
  .activeInactive{
    position: relative;
    display: inline-block;
    margin-left: 8px;
    margin-top: 10px;
    vertical-align: top;
    .btn{
      svg{
        vertical-align: middle;
      }
    }
    a{
      font-weight: 600;
      color: $db600;
      margin: 0 24px;
      &.disabled{
        color: $lg700;
      }
    }
  }
  .optionsdrowdown{
    display: inline-block;
    .showdropdown{
      display: block;
      position: absolute;
      top: 44px;
      right: 0;
      z-index: 999;
      background: $white;
      box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.1), 0px 20px 24px rgba(20, 37, 63, 0.06);
      border-radius: 5px;
      width: 124px;
      border: 0;
      padding: 5px 0;
      a{
        padding:8px 20px;
        margin: 0;
        display: block;
        font-weight: 400;
        font-size: 14px;
        color: $dg600;
        text-decoration: none;
        width: 100%;
        border-radius: 5px;
        background: $white;
        border: 0;
        text-align: left;
        white-space: break-spaces;
        cursor: pointer;
        &:hover{
          color: $o300;
        }
      }
      &:after{
        content: '';
        position: absolute;
        top: -3px;
        right: 12px;
        background: $white;
        transform: matrix(0.71, -0.96, 0.52, 0.71, 0, 0);
        width: 15px;
        height: 15px;
      }
    }
    .btn{
      border-color: $lg300;
      border-radius: 14px;
      padding: 3px 10px;
      margin-top: 5px;
      margin-left: 24px;
      svg{
        vertical-align: middle;
      }
    }
  }

}
.searchmain{
  background: $white;
  padding: 0;
  border: 1px solid $lg200;
  border-radius: 4px;
  width:300px;
  display: flex;
  input{
    background: none;
    border: none;
    padding: 8px 10px 7px 10px;
    font-weight: 400;
    font-size: 14px;
    width: 100%;
    &::placeholder{
      color: $lg700;
    }
    &:focus{
      border: none;
    }
  }
  .searchicon{
    margin:10px 0 4px 17px; vertical-align: middle;
  }
}
.sidecolumns{
  position: fixed;
  top:0; right: 0;
  width: 1075px;
  background: $white;
  box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.1), 0px 4px 20px -2px rgba(50, 50, 71, 0.08);
  z-index: 101; 
  height: 100%;
  .sidecolumnstiteMain{
    padding: 0;
  }
  .sidecolumnstite{
    font-weight: 700;
    font-size: 18px;
    display: inline-block;
    width: 100%;
    padding: 24px 32px 0 32px;
   //border-bottom: solid 1px $lg300;
    span{
      float: left;
    }
    .sidecolumnstiteinner{
      display: inline-block;
      width: 90%;
      span{
        display: block;
        width: 100%;
        color: $db600;
        margin-bottom: 0px;
      }
      .subtitle {
        font-size: 14px;
        font-weight: 400;
        color: $dg200;
      }
      p{
        font-weight: 400;
        font-size: 12px;
        color: $dg100;
        margin-bottom: 0;
      }
    }
    .closeicon{
      float: right;
      margin-top: 5px;
      cursor: pointer;
    }
  }
  .timelinedetailtitle{
    padding:24px 24px 0 0;
  }
}
.sidecolumnsdetail{
  position: fixed;
  top:0; right: 0; width: 1056px;
  background: $white;
  box-shadow: 0px 0px 1px rgba(12, 26, 75, 0.1), 0px 4px 20px -2px rgba(50, 50, 71, 0.08);
  z-index: 999;
  height: 100%;
  .sidecolumnstiteMain{
    padding: 0;
  }
  .sidecolumnstite{
    font-weight: 700;
    font-size: 18px;
    display: inline-block;
    width: 100%;
    padding: 24px 32px 0 32px;
    //border-bottom: solid 1px $lg300;
    span{
      float: left;
    }
    .sidecolumnstiteinner{
      display: inline-block;
      width: 220px;
      span{
        display: block;
        width: 100%;
        color: $db600;
        margin-bottom: 0px;
      }
      p{
        font-weight: 400;
        font-size: 12px;
        color: $dg100;
        margin-bottom: 0;
      }
    }
    .closeicon{
      float: right;
      margin-top: 5px;
      cursor: pointer;
    }
  }
}

.sidecolumnsbottons{
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16px;
  text-align: right;
  background: $white;
  box-shadow: inset 0px 1px 0px #E8E8E8;
  z-index: 9;
  &.deletebutton{
    button{
      color: #FF4949;
   font-weight: normal;
    }

  }
}


.fixbuttons{
  width: 100%;
  background: $white;
  box-shadow: inset 0px 1px 0px #E8E8E8;
  padding: 16px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  .fixbuttonsright{
    span{
      font-weight: 400;
      font-size: 14px;
      color: $lg700;
      margin-right: 16px;
    }
  }
}

.tabs{
    padding: 20px;
}
.auditlogSidebar{
  .sectionmain{
    overflow-y: hidden;
  }
}
.tabdetail{
  //height: calc(100vh - 164px);
  //overflow-y: auto;
}
.audittabs {
  height: 100%;
  overflow-y: auto;
}

@media only screen and (max-width: 1400px) {
  .sidecolumns {
    width: 800px;
  }
}
