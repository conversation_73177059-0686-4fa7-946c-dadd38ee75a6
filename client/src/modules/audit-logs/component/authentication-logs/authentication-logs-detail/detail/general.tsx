import React from 'react';
import Description from '../../../../../shared/components/description/description';
import ComponentLoading from '../../../../../shared/components/component-loading/component-loading';
import AuditLogsSectionComponent from '../../../platform-logs/audit-logs-detail/section-component/audit-logs-section-component';
import { formFieldsLabel } from './form';

function General({ data }) {

  const renderSections = () => {
    return (
      <>
        <div className={'m-t-lg'}>
          <AuditLogsSectionComponent title={'General Information'} sectionType={'Detail'}>
            {data !== null ? (
                <>
                  {Object.keys(formFieldsLabel).map((key, index) => {
                    return <Description key={index} title={formFieldsLabel[key]} desc={data[key]} />;
                  })}
                </>
            ) : (
                <ComponentLoading />
            )}
          </AuditLogsSectionComponent>
        </div>
      </>
    );
  };
  return (
    <>
        <div className={'m-t-lg'}>
            {
                data !== null ? renderSections() : <span className={'text-center block'}>No Data Found </span>
            }
        </div>
    </>
  );
}

export default General;
