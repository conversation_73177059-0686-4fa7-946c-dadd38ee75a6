export const AuthenticationLogsColumn = ({ isShowLocalTime, organizationCellRenderer, userCellRenderer, resultCellRenderer,
}) => {
  return [
    {
      field: 'timestamp',
      title: `Activity Date ${!isShowLocalTime ?  '(UTC)' : ''} `,
      filter: false,
      sortable: true,
      isShowHideColumnDisable: true,
      isStopDragColumn: true,
      minWidth: 180,
      hide: false,
      isShowLocalTime: isShowLocalTime,
      isCreatedDate: true,
      displaySecondsWithDateTime: true,
      isClickable: true,
    },
    {
      field: 'type',
      title: 'Type',
      filter: false,
      sortable: true,
      minWidth: 120,
      hide: false,
    },
    {
      field: 'description',
      title: 'Description',
      filter: false,
      sortable: false,
      minWidth: 140,
      hide: false,
    },
    {
      field: 'organization',
      title: 'Organization',
      cellRenderer: organizationCellRenderer,
      filter: false,
      sortable: true,
      minWidth: 100,
      hide: false,
    },
    {
      field: 'ipAddress',
      title: 'IP Address',
      filter: false,
      sortable: true,
      minWidth: 100,
      hide: false,
    },
    {
      field: 'userAgent',
      title: 'User Agent',
      filter: false,
      sortable: true,
      minWidth: 120,
      hide: false,
    },
    {
      field: 'email',
      title: 'Email',
      filter: false,
      sortable: true,
      minWidth: 240,
      hide: false,
    },
    {
      field: 'user',
      title: 'User',
      cellRenderer: userCellRenderer,
      filter: false,
      sortable: true,
      minWidth: 120,
      hide: false,
    },
    {
      field: 'isFailure',
      title: 'Result',
      cellRenderer: resultCellRenderer,
      filter: false,
      sortable: true,
      minWidth: 120,
      hide: false,
    },
  ];
};
