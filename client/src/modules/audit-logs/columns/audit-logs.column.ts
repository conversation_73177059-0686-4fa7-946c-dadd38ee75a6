import { doesUserHasPermissions } from '../../../utils/utils';
import { Actions } from '../../../enums/actions.enum';

export const AuditLogsColumn = ({
  userPermissions, isShowLocalTime,
}) => {
  return [
    {
      field: 'activityDateTime',
      title: `Activity Date ${!isShowLocalTime ?  '(UTC)' : ''} `,
      filter: false,
      sortable: true,
      isShowHideColumnDisable: true,
      isStopDragColumn: true,
      minWidth: 260,
      hide: false,
      isShowLocalTime: isShowLocalTime,
      isClickable: doesUserHasPermissions({
        userPermissions,
        actions: [Actions.GetAuditLogDetails],
      }),
      isCreatedDate: true,
      displaySecondsWithDateTime: true,
    },
    {
      field: 'loggedByService',
      title: 'Service',
      filter: false,
      sortable: true,
      minWidth: 120,
      hide: false,
    },
    {
      field: 'category',
      title: 'Category',
      filter: false,
      sortable: true,
      minWidth: 140,
      hide: false,
    },
    {
      field: 'activityDisplayName',
      title: 'Activity',
      filter: false,
      sortable: true,
      minWidth: 100,
      hide: false,
    },
    {
      field: 'result',
      title: 'Result',
      filter: false,
      sortable: false,
      minWidth: 100,
      hide: false,
    },
    {
      field: 'resultReason',
      title: 'Result Reason',
      filter: false,
      sortable: false,
      minWidth: 120,
      hide: false,
    },
    {
      field: 'initiatedBy',
      title: 'Initiated By (Actor)',
      filter: false,
      sortable: false,
      minWidth: 240,
      hide: false,
    },
    {
      field: 'targetResources',
      title: 'Target(s)',
      renderArrayOfObjectWithKey: 'type',
      filter: false,
      sortable: false,
      minWidth: 260,
      hide: false,
    },
  ];
};
