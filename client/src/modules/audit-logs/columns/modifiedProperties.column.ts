export const modifiedPropertiesColumn = () => {
  return [
    {
      field: 'target',
      title: 'Target',
      suppressMenu: true,
      sortable: false,
      isStopDragColumn: true,
      filter: false,
    },
    {
      field: 'propertyName',
      title: 'Property Name',
      suppressMenu: true,
      sortable: false,
      isStopDragColumn: true,
      filter: false,
    },
    {
      field: 'oldValue',
      title: 'Old Value',
      width:200,
      wrapText:true,
      autoHeight:true,
      suppressMenu: true,
      sortable: false,
      isStopDragColumn: true,
      filter: false,
    },
    {
      field: 'newValue',
      title: 'New Value',
      width:200,
      wrapText:true,
      autoHeight:true,
      suppressMenu: true,
      sortable: false,
      isStopDragColumn: true,
      filter: false,
    },
  ];
};
