import http from '../../utils/service';

export const getAgreementsForUser = () => {
  return http.get({
    url: '/v1/agreements/mine',
    messageSettings: {
      hideSuccessMessage: true,
      hideErrorMessage: false,
    },
  });
};
export const getAgreementDetails = ({ agreementId }) => {
  return http.get({
    url: `/v1/agreements/${agreementId}`,
    messageSettings: {
      hideSuccessMessage: true,
      hideErrorMessage: false,
    },
  });
};
export const submitAgreement = ({ body }) => {
  return http.post({
    url: '/v1/agreements/sign',
    data:body,
    messageSettings: {
      hideSuccessMessage: true,
      hideErrorMessage: false,
    },
  });
};
