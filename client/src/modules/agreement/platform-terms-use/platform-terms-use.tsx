import React, { useEffect, useRef, useState } from 'react';
import Button from '../../shared/components/button/button';
import ModalWrapper from '../../shared/components/modal/modal';
import { getAgreementDetails, getAgreementsForUser, submitAgreement } from '../agreement.service';
import DOMPurify from 'dompurify';
import { DASHBOARD_URL } from '../../../constants/route-constant';

function PlatformTermsUse() {
  //state
  const [isReachBottom, setReachBottom] = useState(false);
  const [isAgreementChecked, setAgreementChecked] = useState(false);
  const [agreementId, setAgreementId] = useState(null);
  const [agreementDetails, setAgreementDetails] = useState(null);
  const [isAgreementAPIErrored, setAgreementAPIErrored] = useState(null);
  const ref = useRef(null);

  const openModal = () => {
    if (ref) {
      ref.current.openModal();
    }
  };

  const redirectToDashboard = () => {
    window.location.href = DASHBOARD_URL;
  };
  //Get Agreement Detail
  const getAgreementDetail = async (id) => {
    try {
      const agreementData = await getAgreementDetails({ agreementId:id });
      setAgreementDetails(agreementData);
      setAgreementAPIErrored(false);
      openModal();
    } catch (e){
      setAgreementAPIErrored(true);
      console.error(e);
    }
  };
  const checkScrollLogic = () => {
    const scrollTop = document.getElementById('terms-of-use-scroll')?.scrollTop;
    const scrollHeight = document.getElementById('terms-of-use-scroll')?.scrollHeight; // added
    const offsetHeight = document.getElementById('terms-of-use-scroll')?.offsetHeight;
    const contentHeight = scrollHeight - offsetHeight; // added
    if (contentHeight <= scrollTop) {
      setReachBottom(true);
    }
  };
  //handle Modal Scroll
  const handleScroll = async () => {
    setTimeout(() => {
      document.getElementById('terms-of-use-scroll')?.addEventListener(
        'scroll',
        () => checkScrollLogic(),
        false,
      );  
    }, 0);
  };
  //Get Agreements for user
  const getAgreements = async () => {
    try {
      const agreementData = await getAgreementsForUser();
      // in future will implement map
      if (agreementData[0].signed === false){
        setAgreementId(agreementData[0].id);
        await getAgreementDetail(agreementData[0].id);
        setAgreementAPIErrored(false);
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        await handleScroll();

      } else {
        setAgreementAPIErrored(false);
        redirectToDashboard();
      }
    } catch (e){
      //Handle error
      setAgreementAPIErrored(true);
      console.error(e);
    }
  };

  useEffect(()=>{
    setTimeout(() => {
      (async () => {
        await getAgreements();
      })();
    }, 0);
  }, []);

  //Handle Submit
  const handleSubmit = async () =>{
    try {
      const body = { agreementId };
      await submitAgreement({ body });
      redirectToDashboard();
    } catch (e){
      console.error(e);
    }
  };

  useEffect(() => {
    return () => {
      document.getElementById('terms-of-use-scroll')?.removeEventListener('scroll', () => checkScrollLogic(), false);
    };
  }, []);
  //Handle Checkbox
  const handleCheckboxChanged = () =>{
    setAgreementChecked(!isAgreementChecked);
  };
  //Modal Content
  const renderTermsOfUse = () => {
    return (<>
      {
        !isAgreementAPIErrored ? (
          <ModalWrapper ref={ref} makeEscapeActive={false} className={'detail importtempletedialog termsofusedialog'} >
            {
              agreementDetails &&
                <div className={'detailpopup'}>
                    <div className={'modeltitle clearfix'}>
                        <h1>{agreementDetails.title}</h1>
                    </div>
                    <div id={'terms-of-use-scroll'} className={'modelbody'} dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(agreementDetails.metadata) }}/>
                    <div className={'modelfooter'}>
                        <div className={'agreecheckbox'}>
                          {
                            isReachBottom ?
                              <input type="checkbox" name="agree" value="agree" onChange={handleCheckboxChanged}/>
                              :
                              <input type="checkbox" name="agree" value="agree" disabled={true}/>
                          }
                            <label>I have read and agree to the Terms of Use of the Platform.</label>
                        </div>
                        <div>
                            <Button
                                type="primary"
                                disabled={!isAgreementChecked}
                                title={'Submit'}
                                onClick={() => {
                                  handleSubmit();
                                }}
                            />
                        </div>
                    </div>
                </div>
            }
          </ModalWrapper>
        ) : <></>
      }
        </>);
  };
  return (
        <div className={'inline'}>
          {renderTermsOfUse()}
        </div>
  );
}

export default PlatformTermsUse;
