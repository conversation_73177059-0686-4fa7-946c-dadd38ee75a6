export interface GridColumn {
  field: string; // Field name for data binding
  title: string; // Header text for the column
  filter?: any; // Property indicating if filtering is enabled for the column
  sortable?: boolean; // Property indicating if sorting is enabled for the column
  checkboxSelection?: boolean; // Property indicating if checkbox selection is enabled for the column
  isStopDragColumn?: boolean; // Property indicating if dragging the column is disabled
  isShowHideColumnDisable?: boolean; // Property indicating if showing/hiding the column is disabled
  isClickable?: boolean; // Property indicating if the column is clickable
  minWidth?: number; // Minimum width for the column
  hide: boolean; //Property for hidden state of the column
  defaultHideValue: boolean; // Property indicating if the column is initially hidden
  cellRenderer?: any; // Custom cell renderer component
  hasPermission?: boolean; // Property indicating if the user has permission for the column
  renderArrayOfObjectWithKey?: string; // Key for rendering array of objects
  isCurrency?: boolean; // Property indicating if the column represents currency
  renderPartner?: any; // Custom renderer for partner data
  isPercentage?: boolean; // Property indicating if the column represents percentage
  isDateOnly?: boolean; // Property indicating if only date is displayed in the column
  tooltipCustomComponent?: any; // Custom tooltip component
  tooltipField?: string; // Field name for tooltip text
  isCreatedDate?: boolean; // Property indicating if the column represents created date
  isUpdatedDate?: boolean; // Property indicating if the column represents updated date
  hideColumnInSideBar?: boolean; // Property indicating if the column should be hidden in the sidebar
  tooltipHeading?: string; // Property indicating the title used for the tooltip heading
  inlineEdit?: boolean; // Property indicating if inline edit is enabled for the column
  renderObjectWithKey?: string; // Property indicating the key used to render an object
  isStatusField?: boolean; // Property indicating if the column represents a status field
  Component?: any; // Component to be rendered in the cell
  cellEditorParams?: any; // Parameters for the cell editor
  isFormattedNumber?:boolean //to show number formatted
  inlineEditComponentType?: string
  inlineEditPermission?: boolean; // used for inline editing permissions
  onCellDoubleClicked?: any;
  inlineEditForModel?: boolean;
  cellClass?: string;
  isActionColumn?: boolean,
  isPinnedColumn?: boolean,
  filterParams?:any,
  isAddress?:boolean,
  maxWidth?:number,
  currentVersion?: any
}
