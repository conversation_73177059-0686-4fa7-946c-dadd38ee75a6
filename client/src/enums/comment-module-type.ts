export const commentModuleAPIParamMapping = {
  Incident: 'incidents',
  IncidentModule: {
    ProjectStatus: 'projectStatus',
    ProjectStatusModule: {
      Milestones: 'milestones',
      Tasks: 'tasks',
      StatusUpdates: 'statusUpdates',
      StatusUpdateModule: {
        Published: 'published',
        InReview: 'inReview',
        PendingForReview: 'pendingForReview',
      },
    },
    Claims: 'claims',
    ClaimsModule: {
      Invoices: 'invoices',
      RequestForInformations: 'requestForInformations',
      Supplementals: 'supplementals',
      Timeline: 'timeline',
    },
    Assets: 'assets',
    IOC: 'ioc',
    IncidentTimeline: 'timeline',
    MoxfiveInsights: 'moxfiveInsights',
    MoxfiveInsightsModule: {
      MoxfiveCarrierInsights: 'moxfiveCarrierInsights',
    },
    Members: 'users',

  },
  Resilience:'resiliences',
  ResilienceModule: {
    Tracker : 'trackers',
    Objective : 'objectives',
    StatusUpdates : 'statusUpdates',
  },
};

export const snapshotsModuleAPIParamMapping = {
  Incident: 'incidents',
  IncidentModule: {
    ProjectStatusModule: {
      Tasks: 'tasks',
    },
    IOC: 'ioc',
    IncidentTimeline: 'timeline',
  },
};
