import { generateOperatorDropdownValueAttribute } from '../modules/shared/components/table/filter/filter.service';

export enum ValueTypesEnum {
  STRING = 'String',
  NUMBER = 'Number',
  DROPDOWN = 'Dropdown',
  DATE = 'Date',
  BOOLEAN = 'Boolean',
  IP_ADDRESS = 'IP Address',
  URL = 'URL',
  EMAIL = 'Email',
  PERCENTAGE = 'Percentage',
  CURRENCY = 'Currency',
  DATERANGE = 'Daterange',
  TEXTAREA = 'Textarea',
  StringWithEQ = 'StringWithEQ',
}
export const ValueTypeFieldObject = {
  [ValueTypesEnum.STRING]: { type: 'input' },
  [ValueTypesEnum.NUMBER]: { type: 'number' },
  [ValueTypesEnum.DROPDOWN]: { type: 'select' },
  [ValueTypesEnum.DATE]: { type: 'datepicker' },
  [ValueTypesEnum.BOOLEAN]: { type: 'switchInput' }, // TODO Need to change according to boolean
  [ValueTypesEnum.IP_ADDRESS]: { type: 'ipAddressInput' },
  [ValueTypesEnum.URL]: { type: 'input' },
  [ValueTypesEnum.EMAIL]: { type: 'input' },
  [ValueTypesEnum.PERCENTAGE]: { type: 'percentageInput' },
  [ValueTypesEnum.CURRENCY]: { type: 'currencyInput' },
  [ValueTypesEnum.TEXTAREA] : { type: 'input' },
  [ValueTypesEnum.DATERANGE]: { type: 'customDateRangePicker' },
};
export const OPERATOR_FIELD_DATA = (allowedOperator) => {
  const operators = allowedOperator.map((allowFilter, index) => {
    return {
      label: allowFilter.label,
      // value: allowFilter.value,
      value: generateOperatorDropdownValueAttribute(allowFilter.value, index),
    };
  });
  return {
    name: 'operator',
    type: 'select',
    label: 'Operator',
    isMulti: false,
    options: operators,
    placeholder: 'Select Operator',
  };
};
export const COMMON_FIELD_DATA = {
  name: 'field',
  label: 'Value',
};
