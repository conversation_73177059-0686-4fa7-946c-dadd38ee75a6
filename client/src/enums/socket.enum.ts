export enum SocketNamespaces {
  ASSETS_UPDATE = 'assetsUpdate',
  IMPORT = 'import',
  WEB_NOTIFICATION = 'webNotification',
  MILESTONES_TASKS_UPDATE = 'milestonesTasksUpdate',
  STATUS_UPDATES = 'statusUpdates',
  STATUS_UPDATES_V2 = 'statusUpdatesV2',
}

export enum SocketEvents {
  ASSETS_HOST_DETAILS = 'host-details',
  ASSETS_RECOVERY_DETAILS = 'recovery-details',
  ASSETS_RECOVERY_STATUS = 'recovery-status',
  ASSETS_DATA_PRESERVATION_DETAILS = 'data-preservation-details',
  ASSETS_FORENSICS_DETAILS = 'forensics-details',
  ASSETS_FORENSICS_COMPROMISED_STATUS = 'forensics-compromised-status',
  ASSETS_DATES_DETAILS = 'dates-details',
  ASSETS_REMOVED = 'removed-assets',
  CONNECTED_ASSETS_LIST_USERS = 'connected-assets-list-users',
  CONNECTED_ASSETS_DETAIL_USERS = 'connected-assets-detail-users',
  CONNECTED_MILESTONES_TASKS_LIST_USERS = 'connected-milestones-tasks-list-users',
  TASKS_UPDATED = 'tasks-updated',
  MILESTONES_UPDATED = 'milestones-updated',
  IMPORT_FILE_NOTIFICATION = 'import-file-notification',
  IMPORT_FILE_NOTIFICATION_ACK = 'import-file-notification-ack',
  WEB_NOTIFICATION_EVENT = 'web-notification-event',
  WEB_NOTIFICATION_EVENT_ACK = 'web-notification-event',
  CONNECTED_STATUS_UPDATE_USERS = 'connected-status-update-users',
  STATUS_UPDATE_USER_REMOVED = 'status-update-user-removed',
  STATUS_UPDATE_UPDATED = 'status-update-updated',
  STATUS_UPDATE_LAYOUT_CHANGED = 'status-update-layout-changed',
  STATUS_UPDATE_WIDGET_PERMISSION_UPDATED = 'status-update-widget-permission-updated',
  STATUS_UPDATE_PUBLISHED = 'status-update-published',
  STATUS_UPDATE_DELETED = 'status-update-deleted',
  STATUS_UPDATE_WIDGET_REMOVED = 'status-update-widget-removed',
  STATUS_UPDATE_NEW_VERSION_CONFIGURATION_CHANGE = 'status-update-new-version-configuration-change',
  STATUS_UPDATE_NOTES_COLLABORATION_SYNC_UPDATE = 'status-update-notes-collaboration-sync-update',
  STATUS_UPDATE_NOTES_COLLABORATION_GET_CONTENT = 'status-update-notes-collaboration-get-content',
  STATUS_UPDATE_NOTES_COLLABORATION_CURSOR_POINTER_UPDATE = 'status-update-notes-collaboration-cursor-pointer-update',
  CONNECTED_STATUS_UPDATE_V2_USERS = 'connected-status-updates-v2-users',
  STATUS_UPDATE_V2_VERSION_CHANGE = 'status-update-v2-version-change',
}

export enum SocketPaths {
  INCIDENT = '/v1/incidents/socket.io/connection',
  NOTIFICATION = '/v1/notifications/socket.io/connection',
}
