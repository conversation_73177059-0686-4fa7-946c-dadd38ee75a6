export enum IncidentModules {
  DETAILS = 'Details',
  PROJECT_STATUS = 'Project Status',
  DASHBOARD = 'Dashboard',
  MEMBERS = 'Members',
  ASSETS = 'Assets',
  INCIDENT_TIMELINE = 'Incident Timeline',
  IOC = 'Indicators of Compromise (IOC)',
  MOXFIVE_INSIGHTS = 'MOXFIVE Insights',
  CLAIMS = 'Claims',
  PROJECT_STATUS_OVERVIEW = 'Overview',
  PROJECT_STATUS_STATUS_UPDATES = 'Status Updates',
  PROJECT_STATUS_MILESTONES = 'Milestones',
  PROJECT_STATUS_TASKS = 'Tasks',
  ASSETS_ALL_ASSETS = 'All Assets',
  ASSETS_ALL_MANAGE_STATUS = 'Manage Status',
  CLAIMS_INVOICES = 'Invoices',
  CLAIMS_SUPPLEMENTAL_DATA = 'Supplemental Data',
  CLAIMS_RFI = 'RFI',
  CLAIMS_TIMELINE = 'Timeline',
  ECONOMICS = 'Economics',
  OVERVIEW = 'Overview',
  SETTINGS = 'Settings',
  TASKS = 'Tasks',
  MILESTONES = 'Milestones',
  STATUS_UPDATES = 'Status Updates',
  MANAGE_ASSETS_STATUS = 'Manage Assets Status',
}
