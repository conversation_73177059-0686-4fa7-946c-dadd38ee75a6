export enum Actions {
  // List Actions
  ListOrganizations = 'ListOrganizations',
  ListUsers = 'ListUsers',
  ListOrganizationTypes = 'ListOrganizationTypes',
  ListOrganizationUsers = 'ListOrganizationUsers',
  ListServicesForPolicy = 'ListServicesForPolicy',
  ListServiceActionsForPolicy = 'ListServiceActionsForPolicy',
  ListServices = 'ListServices',
  ListServiceActions = 'ListServiceActions',
  ListPolicies = 'ListPolicies',
  ListPlatformEntitiesForPolicy = 'ListPlatformEntitiesForPolicy',
  ListPoliciesForPlatformEntity = 'ListPoliciesForPlatformEntity',
  ListIncidentOpportunities = 'ListIncidentOpportunities',
  ListIncidents = 'ListIncidents',
  ListProjectTasks = 'ListProjectTasks',
  ListProjectStatusUpdatesforMF = 'ListProjectStatusUpdatesforMF',
  ListProjectStatusUpdatesforNonMF = 'ListProjectStatusUpdatesforNonMF',
  ListIncidentUsers = 'ListIncidentUsers',
  ListAssets = 'ListAssets',
  ListAssetStatuses = 'ListAssetStatuses',
  ListClaimTimelines = 'ListClaimTimelines',
  ListClaimRequestForInformations = 'ListClaimRequestForInformations',
  ListClaimInvoices = 'ListClaimInvoices',
  ListClaimSupplementals = 'ListClaimSupplementals',
  ListTimelines = 'ListTimelines',
  // Read Actions
  GetUsersOfActiveDirectory = 'GetUsersOfActiveDirectory',
  GetOrganizationFlexibleFields = 'GetOrganizationFlexibleFields',
  GetOrganizationUserDetail = 'GetOrganizationUserDetail',
  GetUserDetail = 'GetUserDetail',
  GetOrganizationDetail = 'GetOrganizationDetail',
  GetPolicyDetail = 'GetPolicyDetail',
  GetIncidentModules = 'GetIncidentModules',
  GetCompanyBackground = 'GetCompanyBackground',
  GetEnvironmentActiveDirectoryDetail = 'GetEnvironmentActiveDirectoryDetail',
  GetEnvironmentBackupsDetail = 'GetEnvironmentBackupsDetail',
  GetEnvironmentEmailDetail = 'GetEnvironmentEmailDetail',
  GetEnvironmentGeneralDetail = 'GetEnvironmentGeneralDetail',
  GetEnvironmentSolutionsDetail = 'GetEnvironmentSolutionsDetail',
  GetIncidentBusinessEmailCompromiseDetail = 'GetIncidentBusinessEmailCompromiseDetail',
  GetIncidentExtortionDetail = 'GetIncidentExtortionDetail',
  GetIncidentGeneralDetail = 'GetIncidentGeneralDetail',
  GetIncidentRansomewareDetail = 'GetIncidentRansomewareDetail',
  GetInsuranceClaimDetail = 'GetInsuranceClaimDetail',
  GetInsuranceGeneralDetail = 'GetInsuranceGeneralDetail',
  GetInsurancePolicyDetail = 'GetInsurancePolicyDetail',
  GetProjectMilestoneGeneralDetail = 'GetProjectMilestoneGeneralDetail',
  GetProjectMilestoneStatusDetail = 'GetProjectMilestoneStatusDetail',
  GetProjectTaskGeneralDetail = 'GetProjectTaskGeneralDetail',
  GetProjectTaskStatusDetail = 'GetProjectTaskStatusDetail',
  GetProjectTaskMilestoneDetail = 'GetProjectTaskMilestoneDetail',
  GetProjectStatusUpdateGeneralDetail = 'GetProjectStatusUpdateGeneralDetail',
  GetProjectStatusUpdateStatusDetail = 'GetProjectStatusUpdateStatusDetail',
  RemoveProjectTasks = 'RemoveProjectTasks',
  GetIncidentDetails = 'GetIncidentDetails',
  GetAssetHostDetail = 'GetAssetHostDetail',
  GetAssetRecoveryDetail = 'GetAssetRecoveryDetail',
  GetAssetDataPreservationDetail = 'GetAssetDataPreservationDetail',
  GetAssetForensicDetail = 'GetAssetForensicDetail',
  GetAssetDatesDetail = 'GetAssetDatesDetail',
  GetAssetStatusLibrary = 'GetAssetStatusLibrary',
  GetClaimTimelineGeneralDetail = 'GetClaimTimelineGeneralDetail',
  GetClaimTimelineVendorDetailsDetail = 'GetClaimTimelineVendorDetailsDetail',
  GetClaimRequestForInformationGeneralDetail = 'GetClaimRequestForInformationGeneralDetail',
  GetClaimRequestForInformationInsuredResponseDetail = 'GetClaimRequestForInformationInsuredResponseDetail',
  GetClaimInvoiceCarrierUpdatesDetail = 'GetClaimInvoiceCarrierUpdatesDetail',
  GetClaimInvoiceDocumentsDetail = 'GetClaimInvoiceDocumentsDetail',
  GetClaimInvoiceGeneralDetail = 'GetClaimInvoiceGeneralDetail',
  GetClaimInvoiceMOXFIVEAnalysisDetail = 'GetClaimInvoiceMOXFIVEAnalysisDetail',
  GetClaimInvoiceStatusDetail = 'GetClaimInvoiceStatusDetail',
  GetClaimSupplementalAnalysisDetail = 'GetClaimSupplementalAnalysisDetail',
  GetClaimSupplementalDocumentsDetail = 'GetClaimSupplementalDocumentsDetail',
  GetClaimSupplementalGeneralDetail = 'GetClaimSupplementalGeneralDetail',
  GetTimelineGeneralDetail = 'GetTimelineGeneralDetail',
  GetTimelineForensicDetail = 'GetTimelineForensicDetail',
  GetTimelineNegotiationDetail = 'GetTimelineNegotiationDetail',
  GetAccessManagementSummary = 'GetAccessManagementSummary',
  // Write actions
  ChangeOrganizationMembership = 'ChangeOrganizationMembership',
  InviteUser = 'InviteUser',
  AddOrganizationFlexibleField = 'AddOrganizationFlexibleField',
  AddMemberToOrganizationUsingEmail = 'AddMemberToOrganizationUsingEmail',
  AddMemberToOrganization = 'AddMemberToOrganization',
  AddOwnerToOrganizationUsingEmail = 'AddOwnerToOrganizationUsingEmail',
  AddOwnerToOrganization = 'AddOwnerToOrganization',
  AddIncidentMember = 'AddIncidentMember',
  AddIncidentFlexibleField = 'AddIncidentFlexibleField',
  AddAssetStatus = 'AddAssetStatus',
  CreateOrganization = 'CreateOrganization',
  CreatePolicy = 'CreatePolicy',
  CreateIncidentOpportunity = 'CreateIncidentOpportunity',
  CreateMilestone = 'CreateMilestone',
  CreateTask = 'CreateTask',
  CreateStatusUpdate = 'CreateStatusUpdate',
  CreateAsset = 'CreateAsset',
  PublishIncidentDeal = 'PublishIncidentDeal',
  UpdateUserEmailOfAnOrganization = 'UpdateUserEmailOfAnOrganization',
  UpdateUsersStatusOfAnOrganization = 'UpdateUsersStatusOfAnOrganization',
  UpdateUserProfileOfAnOrganization = 'UpdateUserProfileOfAnOrganization',
  UpdateOrganizationStatus = 'UpdateOrganizationStatus',
  UpdateUsersStatus = 'UpdateUsersStatus',
  UpdateOrganization = 'UpdateOrganization',
  UpdateProfileIcon = 'UpdateProfileIcon',
  UpdatePolicy = 'UpdatePolicy',
  UpdatePoliciesStatus = 'UpdatePoliciesStatus',
  UpdateEnvironmentActiveDirectoryDetail = 'UpdateEnvironmentActiveDirectoryDetail',
  UpdateEnvironmentBackupsDetail = 'UpdateEnvironmentBackupsDetail',
  UpdateEnvironmentEmailDetail = 'UpdateEnvironmentEmailDetail',
  UpdateEnvironmentGeneralDetail = 'UpdateEnvironmentGeneralDetail',
  UpdateEnvironmentSolutionsDetail = 'UpdateEnvironmentSolutionsDetail',
  UpdateIncidentBusinessEmailCompromiseDetail = 'UpdateIncidentBusinessEmailCompromiseDetail',
  UpdateIncidentExtortionDetail = 'UpdateIncidentExtortionDetail',
  UpdateIncidentGeneralDetail = 'UpdateIncidentGeneralDetail',
  UpdateIncidentModules = 'UpdateIncidentModules',
  UpdateIncidentRansomwareDetail = 'UpdateIncidentRansomwareDetail',
  UpdateInsuranceClaimDetail = 'UpdateInsuranceClaimDetail',
  UpdateInsuranceGeneralDetail = 'UpdateInsuranceGeneralDetail',
  UpdateInsurancePolicyDetail = 'UpdateInsurancePolicyDetail',
  UpdateCompanyBackgroundDetail = 'UpdateCompanyBackgroundDetail',
  UpdateIncidentStatus = 'UpdateIncidentStatus',
  UpdateIncidentProgressPercentage = 'UpdateIncidentProgressPercentage',
  UpdateProjectMilestoneGeneralDetails = 'UpdateProjectMilestoneGeneralDetails',
  UpdateProjectMilestoneStatusDetails = 'UpdateProjectMilestoneStatusDetails',
  UpdateTasks = 'UpdateTasks',
  UpdateProjectTaskGeneralDetails = 'UpdateProjectTaskGeneralDetails',
  UpdateProjectTaskStatusDetails = 'UpdateProjectTaskStatusDetails',
  UpdateProjectTaskMilestoneDetails = 'UpdateProjectTaskMilestoneDetails',
  UpdatePendingForReviewStatusOfProject = 'UpdatePendingForReviewStatusOfProject',
  UpdateProjectStatusUpdateGeneralDetails = 'UpdateProjectStatusUpdateGeneralDetails',
  UpdateProjectStatusUpdateStatusDetails = 'UpdateProjectStatusUpdateStatusDetails',
  UpdateAssets = 'UpdateAssets',
  UpdateAssetHostDetail = 'UpdateAssetHostDetail',
  UpdateAssetRecoveryDetail = 'UpdateAssetRecoveryDetail',
  UpdateAssetDataPreservationDetail = 'UpdateAssetDataPreservationDetail',
  UpdateAssetForensicsDetail = 'UpdateAssetForensicsDetail',
  UpdateAssetForensicsCompromisedStatus = 'UpdateAssetForensicsCompromisedStatus',
  UpdateAssetDatesDetail = 'UpdateAssetDatesDetail',
  UpdateAssetRecoveryStatus = 'UpdateAssetRecoveryStatus',
  UpdateAssetStatusOfLibrary = 'UpdateAssetStatusOfLibrary',
  UpdateAssetStatuses = 'UpdateAssetStatuses',
  RemoveAssets = 'RemoveAssets',
  RemoveOwnersOfOrganization = 'RemoveOwnersOfOrganization',
  RemoveMembersOfOrganization = 'RemoveMembersOfOrganization',
  RemoveIncidentUsers = 'RemoveIncidentUsers',
  RemoveProjectMilestones = 'RemoveProjectMilestones',
  RemoveProjectTask = 'RemoveProjectTask',
  RemoveProjectStatusUpdates = 'RemoveProjectStatusUpdates',
  RemoveAssetStatusFromLibrary = 'RemoveAssetStatusFromLibrary',
  ListIncidentsForOrganization = 'ListIncidentsForOrganization',
  ListIncidentsForOrganizationUser = 'ListIncidentsForOrganizationUser',
  GetEconomicsOverview = 'GetEconomicsOverview',
  GetEconomicsWorkStreams = 'GetEconomicsWorkStreams',
  ListMOXFIVECarrierInsights = 'ListMOXFIVECarrierInsights',
  CreateMOXFIVECarrierInsight = 'CreateMOXFIVECarrierInsight',
  GetMOXFIVECarrierInsight = 'GetMOXFIVECarrierInsight',
  RemoveMOXFIVECarrierInsights = 'RemoveMOXFIVECarrierInsights',
  UpdateMOXFIVECarrierInsight = 'UpdateMOXFIVECarrierInsight',
  GenerateMOXFIVEInsightsOverview = 'GenerateMOXFIVEInsightsOverview',
  DeletePolicy = 'DeletePolicy',
  AttachPolicyToPlatformEntities = 'AttachPolicyToPlatformEntities',
  AttachPoliciesToPlatformEntity = 'AttachPoliciesToPlatformEntity',
  AttachPolicyToIncidentUsers = 'AttachPolicyToIncidentUsers',
  DetachPolicyFromPlatformEntity = 'DetachPolicyFromPlatformEntity',
  DetachPoliciesFromPlatformEntity = 'DetachPoliciesFromPlatformEntity',
  DetachInsuranceCarrier = 'DetachInsuranceCarrier',
  CreateTimeline = 'CreateTimeline',
  RemoveClaimTimeline = 'RemoveClaimTimeline',
  UpdateClaimTimelineGeneralDetail = 'UpdateClaimTimelineGeneralDetail',
  UpdateClaimTimelineVendorDetails = 'UpdateClaimTimelineVendorDetails',
  CreateRFI = 'CreateRFI',
  RemoveClaimRequestForInformation = 'RemoveClaimRequestForInformation',
  UpdateClaimRequestForInformationGeneralDetail = 'UpdateClaimRequestForInformationGeneralDetail',
  UpdateClaimRequestForInformationInsuredResponseDetail = 'UpdateClaimRequestForInformationInsuredResponseDetail',
  CreateInvoice = 'CreateInvoice',
  RemoveClaimInvoices = 'RemoveClaimInvoices',
  UpdateClaimInvoiceDocumentsDetail = 'UpdateClaimInvoiceDocumentsDetail',
  UpdateClaimInvoiceCarrierUpdatesDetail = 'UpdateClaimInvoiceCarrierUpdatesDetail',
  UpdateClaimInvoiceStatusDetail = 'UpdateClaimInvoiceStatusDetail',
  UpdateClaimInvoiceGeneralDetail = 'UpdateClaimInvoiceGeneralDetail',
  UpdateClaimInvoiceMOXFIVEAnalysisDetail = 'UpdateClaimInvoiceMOXFIVEAnalysisDetail',
  CreateSupplementalData = 'CreateSupplementalData',
  RemoveClaimSupplementals = 'RemoveClaimSupplementals',
  UpdateClaimSupplementalAnalysisDetail = 'UpdateClaimSupplementalAnalysisDetail',
  UpdateClaimSupplementalDocumentsDetail = 'UpdateClaimSupplementalDocumentsDetail',
  UpdateClaimSupplementalGeneralDetail = 'UpdateClaimSupplementalGeneralDetail',
  CreateIncidentTimelineEntry = 'CreateIncidentTimelineEntry',
  UpdateTimelineGeneralDetails = 'UpdateTimelineGeneralDetails',
  UpdateTimelineForensicDetails = 'UpdateTimelineForensicDetails',
  UpdateTimelineNegotiationDetails = 'UpdateTimelineNegotiationDetails',
  RemoveIncidentTimeline = 'RemoveIncidentTimeline',
  GetMOXFIVEInsightOverview = 'GetMOXFIVEInsightOverview',
  RemoveMOXFIVEInsightOverview = 'RemoveMOXFIVEInsightOverview',
  UpdateMOXFIVEInsightOverview = 'UpdateMOXFIVEInsightOverview',
  UpdateMOXFIVEInsightOverviewStatus = 'UpdateMOXFIVEInsightOverviewStatus',
  CreateIOC = 'CreateIOC',
  GetIOCIndicatorDetail = 'GetIOCIndicatorDetail',
  GetIOCRemediationAndContainmentDetail = 'GetIOCRemediationAndContainmentDetail',
  ListIOC = 'ListIOC',
  RemoveIOC = 'RemoveIOC',
  UpdateIOCIndicatorDetail = 'UpdateIOCIndicatorDetail',
  UpdateIOCRemediationAndContainmentDetail = 'UpdateIOCRemediationAndContainmentDetail',
  CreateMilestoneComment = 'CreateMilestoneComment',
  ListMilestoneComments = 'ListMilestoneComments',
  RemoveMilestoneAnyComment = 'RemoveMilestoneAnyComment',
  RemoveMilestoneComment = 'RemoveMilestoneComment',
  UpdateMilestoneAnyComment = 'UpdateMilestoneAnyComment',
  UpdateMilestoneComment = 'UpdateMilestoneComment',
  CreateTaskComment = 'CreateTaskComment',
  ListTaskComments = 'ListTaskComments',
  RemoveTaskAnyComment = 'RemoveTaskAnyComment',
  RemoveTaskComment = 'RemoveTaskComment',
  UpdateTaskAnyComment = 'UpdateTaskAnyComment',
  UpdateTaskComment = 'UpdateTaskComment',
  CreateStatusUpdateComment = 'CreateStatusUpdateComment',
  ListStatusUpdateComments = 'ListStatusUpdateComments',
  RemoveStatusUpdateAnyComment = 'RemoveStatusUpdateAnyComment',
  RemoveStatusUpdateComment = 'RemoveStatusUpdateComment',
  UpdateStatusUpdateAnyComment = 'UpdateStatusUpdateAnyComment',
  UpdateStatusUpdateComment = 'UpdateStatusUpdateComment',
  CreateInvoiceComment = 'CreateInvoiceComment',
  ListInvoiceComments = 'ListInvoiceComments',
  RemoveInvoiceAnyComment = 'RemoveInvoiceAnyComment',
  RemoveInvoiceComment = 'RemoveInvoiceComment',
  UpdateInvoiceAnyComment = 'UpdateInvoiceAnyComment',
  UpdateInvoiceComment = 'UpdateInvoiceComment',
  CreateRFIComment = 'CreateRFIComment',
  ListRFIComments = 'ListRFIComments',
  RemoveRFIAnyComment = 'RemoveRFIAnyComment',
  RemoveRFIComment = 'RemoveRFIComment',
  UpdateRFIAnyComment = 'UpdateRFIAnyComment',
  UpdateRFIComment = 'UpdateRFIComment',
  CreateSupplementalComment = 'CreateSupplementalComment',
  ListSupplementalComments = 'ListSupplementalComments',
  RemoveSupplementalAnyComment = 'RemoveSupplementalAnyComment',
  RemoveSupplementalComment = 'RemoveSupplementalComment',
  UpdateSupplementalAnyComment = 'UpdateSupplementalAnyComment',
  UpdateSupplementalComment = 'UpdateSupplementalComment',
  CreateTimelineComment = 'CreateTimelineComment',
  ListTimelineComments = 'ListTimelineComments',
  RemoveTimelineAnyComment = 'RemoveTimelineAnyComment',
  RemoveTimelineComment = 'RemoveTimelineComment',
  UpdateTimelineAnyComment = 'UpdateTimelineAnyComment',
  UpdateTimelineComment = 'UpdateTimelineComment',
  CreateAssetComment = 'CreateAssetComment',
  ListAssetComments = 'ListAssetComments',
  RemoveAssetAnyComment = 'RemoveAssetAnyComment',
  RemoveAssetComment = 'RemoveAssetComment',
  UpdateAssetAnyComment = 'UpdateAssetAnyComment',
  UpdateAssetComment = 'UpdateAssetComment',
  CreateIOCComment = 'CreateIOCComment',
  ListIOCComments = 'ListIOCComments',
  RemoveIOCAnyComment = 'RemoveIOCAnyComment',
  RemoveIOCComment = 'RemoveIOCComment',
  UpdateIOCAnyComment = 'UpdateIOCAnyComment',
  UpdateIOCComment = 'UpdateIOCComment',
  CreateIncidentTimelineComment = 'CreateIncidentTimelineComment',
  ListIncidentTimelineComments = 'ListIncidentTimelineComments',
  RemoveIncidentTimelineAnyComment = 'RemoveIncidentTimelineAnyComment',
  RemoveIncidentTimelineComment = 'RemoveIncidentTimelineComment',
  UpdateIncidentTimelineAnyComment = 'UpdateIncidentTimelineAnyComment',
  UpdateIncidentTimelineComment = 'UpdateIncidentTimelineComment',
  //Resilience
  GetTrackerTimelineData = 'GetTrackerTimelineData',
  CreateObjectiveInCatalog = 'CreateObjectiveInCatalog',
  GetCatalogObjectiveDetails = 'GetCatalogObjectiveDetails',
  RemoveObjectiveFromCatalog = 'RemoveObjectiveFromCatalog',
  ListCatalogObjectives = 'ListCatalogObjectives',
  UpdateObjectiveDetails = 'UpdateObjectiveDetails',
  AddObjectiveInTracker = 'AddObjectiveInTracker',
  ListTrackerObjectives = 'ListTrackerObjectives',
  ListTrackers = 'ListTrackers',
  CreateTracker = 'CreateTracker',
  CreateObjectiveInTracker = 'CreateObjectiveInTracker',
  RemoveObjectivesFromTracker = 'RemoveObjectivesFromTracker',
  GetTrackerObjectiveGeneralDetails = 'GetTrackerObjectiveGeneralDetails',
  GetTrackerObjectiveMOXFIVENotesDetails = 'GetTrackerObjectiveMOXFIVENotesDetails',
  GetTrackerObjectiveProjectDetails = 'GetTrackerObjectiveProjectDetails',
  GetTrackerObjectiveProjectMOXID = 'GetTrackerObjectiveProjectMOXID',
  UpdateTrackerObjectiveProjectMOXID = 'UpdateTrackerObjectiveProjectMOXID',
  UpdateTrackerObjectiveGeneralDetails = 'UpdateTrackerObjectiveGeneralDetails',
  UpdateTrackerObjectivePartnersDetails = 'UpdateTrackerObjectivePartnersDetails',
  RemoveTrackerObjectivePartners = 'RemoveTrackerObjectivePartners',
  UpdateTrackerObjectiveMOXFIVENotesDetails = 'UpdateTrackerObjectiveMOXFIVENotesDetails',
  UpdateTrackerObjectiveProjectDetails = 'UpdateTrackerObjectiveProjectDetails',
  AddTrackerMember = 'AddTrackerMember',
  RemoveTrackerUsers = 'RemoveTrackerUsers',
  ListTrackerMembers = 'ListTrackerMembers',
  ListTrackerObjectivePendingForReviewStatusUpdates = 'ListTrackerObjectivePendingForReviewStatusUpdates',
  ListTrackerObjectivePublishedStatusUpdates = 'ListTrackerObjectivePublishedStatusUpdates',
  ListTrackerObjectiveInReviewStatusUpdates = 'ListTrackerObjectiveInReviewStatusUpdates',
  CreateTrackerObjectiveStatusUpdate = 'CreateTrackerObjectiveStatusUpdate',
  RemoveTrackerObjectiveStatusUpdates = 'RemoveTrackerObjectiveStatusUpdates',
  UpdatePendingForReviewStatusOfTrackerStatusUpdate = 'UpdatePendingForReviewStatusOfTrackerStatusUpdate',
  GetTrackerObjectiveStatusUpdateGeneralDetails = 'GetTrackerObjectiveStatusUpdateGeneralDetails',
  GetTrackerObjectiveStatusUpdateStatusDetails = 'GetTrackerObjectiveStatusUpdateStatusDetails',
  UpdateTrackerObjectiveStatusUpdateStatusDetails = 'UpdateTrackerObjectiveStatusUpdateStatusDetails',
  UpdateTrackerObjectiveStatusUpdateGeneralDetails = 'UpdateTrackerObjectiveStatusUpdateGeneralDetails',
  AttachPolicyToResilienceUsers = 'AttachPolicyToResilienceUsers',
  ListTrackersForOrganization = 'ListTrackersForOrganization',
  ListTrackersForOrganizationUser = 'ListTrackersForOrganizationUser',
  CreateTrackerObjectiveComment = 'CreateTrackerObjectiveComment',
  ListTrackerObjectiveComments = 'ListTrackerObjectiveComments',
  RemoveTrackerObjectiveAnyComment = 'RemoveTrackerObjectiveAnyComment',
  UpdateTrackerObjectiveAnyComment = 'UpdateTrackerObjectiveAnyComment',
  UpdateTrackerObjectiveComment = 'UpdateTrackerObjectiveComment',
  RemoveTrackerObjectiveComment = 'RemoveTrackerObjectiveComment',
  ListTrackerObjectiveStatusUpdateComments = 'ListTrackerObjectiveStatusUpdateComments',
  CreateTrackerObjectiveStatusUpdateComment = 'CreateTrackerObjectiveStatusUpdateComment',
  RemoveTrackerObjectiveStatusUpdateAnyComment = 'RemoveTrackerObjectiveStatusUpdateAnyComment',
  RemoveTrackerObjectiveStatusUpdateComment = 'RemoveTrackerObjectiveStatusUpdateComment',
  UpdateTrackerObjectiveStatusUpdateAnyComment = 'UpdateTrackerObjectiveStatusUpdateAnyComment',
  UpdateTrackerObjectiveStatusUpdateComment = 'UpdateTrackerObjectiveStatusUpdateComment',
  ListWorkstreamProgresses = 'ListWorkstreamProgresses',
  UpdateWorkstreamProgresses = 'UpdateWorkstreamProgresses',
  AddResilienceFlexibleField = 'AddResilienceFlexibleField',
  ListTrackerObjectivePublishedStatusUpdatesForObjective = 'ListTrackerObjectivePublishedStatusUpdatesForObjective',
  // dashboard
  AddDashboardWidgets =  'AddDashboardWidgets',
  AddProjectDashboardWidgets =  'AddProjectDashboardWidgets',
  ListProjectDashboardWidgets = 'ListProjectDashboardWidgets',
  RemoveProjectDashboardWidget =  'RemoveProjectDashboardWidget',
  GetProjectDashboardDetails = 'GetProjectDashboardDetails',
  GetPlatformDashboardDetails = 'GetPlatformDashboardDetails',
  // Notifications
  ListNotificationConfiguration = 'ListNotificationConfiguration',
  UpdateNotificationConfiguration = 'UpdateNotificationConfiguration',
  UpdateProjectNotificationConfiguration = 'UpdateProjectNotificationConfiguration',
  ListNotificationPreference = 'ListNotificationPreference',
  UpdateNotificationPreference = 'UpdateNotificationPreference',
  ListProjectNotificationConfiguration = 'ListProjectNotificationConfiguration',
  ListProjectNotificationPreference = 'ListProjectNotificationPreference',
  UpdateProjectNotificationPreference = 'UpdateProjectNotificationPreference',
  UpdateProjectNotificationScheduleForUser = 'UpdateProjectNotificationScheduleForUser',
  UpdateNotificationSchedule = 'UpdateNotificationSchedule',
  UpdateProjectNotificationSchedule = 'UpdateProjectNotificationSchedule',
  ListNotificationRecipients = 'ListNotificationRecipients',
  ListProjectNotificationRecipients = 'ListProjectNotificationRecipients',
  UpdateNotificationRecipients = 'UpdateNotificationRecipients',
  UpdateProjectNotificationRecipients = 'UpdateProjectNotificationRecipients',
  // Attachments
  ListClaimInvoiceAttachmentFiles = 'ListClaimInvoiceAttachmentFiles',
  UploadClaimInvoiceAttachmentFiles = 'UploadClaimInvoiceAttachmentFiles',
  RemoveClaimInvoiceAttachmentFile = 'RemoveClaimInvoiceAttachmentFile',
  DownloadClaimInvoiceAttachmentFile = 'DownloadClaimInvoiceAttachmentFile',
  DownloadClaimInvoiceAttachmentFiles = 'DownloadClaimInvoiceAttachmentFiles',
  UploadClaimRFIAttachmentFiles = 'UploadClaimRFIAttachmentFiles',
  ListClaimRFIAttachmentFiles = 'ListClaimRFIAttachmentFiles',
  DownloadClaimRFIAttachmentFiles = 'DownloadClaimRFIAttachmentFiles',
  DownloadClaimRFIAttachmentFile = 'DownloadClaimRFIAttachmentFile',
  RemoveClaimRFIAttachmentFile = 'RemoveClaimRFIAttachmentFile',
  UploadClaimSupplementalDataAttachmentFiles = 'UploadClaimSupplementalDataAttachmentFiles',
  ListClaimSupplementalDataAttachmentFiles = 'ListClaimSupplementalDataAttachmentFiles',
  DownloadClaimSupplementalDataAttachmentFiles = 'DownloadClaimSupplementalDataAttachmentFiles',
  DownloadClaimSupplementalDataAttachmentFile = 'DownloadClaimSupplementalDataAttachmentFile',
  RemoveClaimSupplementalDataAttachmentFile = 'RemoveClaimSupplementalDataAttachmentFile',
  UploadTimelineNegotiationAttachmentFiles = 'UploadTimelineNegotiationAttachmentFiles',
  ListTimelineNegotiationAttachmentFiles = 'ListTimelineNegotiationAttachmentFiles',
  DownloadTimelineNegotiationAttachmentFiles = 'DownloadTimelineNegotiationAttachmentFiles',
  DownloadTimelineNegotiationAttachmentFile = 'DownloadTimelineNegotiationAttachmentFile',
  RemoveTimelineNegotiationAttachmentFile = 'RemoveTimelineNegotiationAttachmentFile',
  //Export
  ExportAssets = 'ExportAssets',
  ExportClaimInvoices = 'ExportClaimInvoices',
  ExportRequestForInformations = 'ExportRequestForInformations',
  ExportClaimSupplementals = 'ExportClaimSupplementals',
  ExportClaimTimelineEntries = 'ExportClaimTimelineEntries',
  ExportIOC = 'ExportIOC',
  ExportMOXFIVECarrierInsights = 'ExportMOXFIVECarrierInsights',
  ExportProjectMilestones = 'ExportProjectMilestones',
  ExportProjectStatusUpdatesforMF = 'ExportProjectStatusUpdatesforMF',
  ExportProjectStatusUpdatesforNonMF = 'ExportProjectStatusUpdatesforNonMF',
  ExportProjectTasks = 'ExportProjectTasks',
  ExportIncidentOpportunities = 'ExportIncidentOpportunities',
  ExportIncidentTimelineEntries = 'ExportIncidentTimelineEntries',
  ExportIncidents = 'ExportIncidents',
  ExportIncidentsForOrganization = 'ExportIncidentsForOrganization',
  ExportIncidentsForOrganizationUser = 'ExportIncidentsForOrganizationUser',
  ImportAssets = 'ImportAssets',
  ImportTimelineEntries = 'ImportTimelineEntries',
  ImportIOC = 'ImportIOC',
  ImportClaimTimelineEntries = 'ImportClaimTimelineEntries',
  ImportStatusUpdates = 'ImportStatusUpdates',
  ImportMilestones = 'ImportMilestones',
  ImportTasks = 'ImportTasks',
  //Resilience Export
  ExportTrackersForOrganizationUser = 'ExportTrackersForOrganizationUser',
  ExportTrackersForOrganization = 'ExportTrackersForOrganization',
  ExportTrackers = 'ExportTrackers',
  ExportCatalogObjectives = 'ExportCatalogObjectives',
  ExportTrackerObjectives = 'ExportTrackerObjectives',
  ExportPublishedTrackerStatusUpdates = 'ExportPublishedTrackerStatusUpdates',
  ExportPendingForReviewTrackerStatusUpdates = 'ExportPendingForReviewTrackerStatusUpdates',
  ExportInReviewTrackerStatusUpdates = 'ExportInReviewTrackerStatusUpdates',
  ExportPublishedTrackerStatusUpdatesForObjective = 'ExportPublishedTrackerStatusUpdatesForObjective',
  ListAuditLogs = 'ListAuditLogs',
  GetAuditLogDetails = 'GetAuditLogDetails',
  //NPS
  SubmitNPSResponses = 'SubmitNPSResponses',
  GetNPSStatus = 'GetNPSStatus',
  GetNPSFormsData = 'GetNPSFormsData',
  // remove incident
  RemoveIncident = 'RemoveIncident',
  RemoveTracker = 'RemoveTracker',
  // Focus View Permissions
  GetActiveFocusView = 'GetActiveFocusView',
  ListFocusViews = 'ListFocusViews',
  RemoveFocusView = 'RemoveFocusView',
  ToggleFocusView = 'ToggleFocusView',
  UpdateFocusView = 'UpdateFocusView',
  CreateFocusView = 'CreateFocusView',
  //place request objective
  CreateCustomObjectiveRequest = 'CreateCustomObjectiveRequest',
  UpdateServiceLines = 'UpdateServiceLines',


  //Offerings
  ListOfferingsForIncident = 'ListOfferingsForIncident',
  ListProjectOfferings = 'ListProjectOfferings',
  GetProjectOfferingDetail = 'GetProjectOfferingDetail',
  AddProjectOffering = 'AddProjectOffering',
  UpdateProjectOffering = 'UpdateProjectOffering',
  RemoveProjectOffering = 'RemoveProjectOffering',

  //Offering Tasks
  ListProjectOfferingTasks = 'ListProjectOfferingTasks',
  GetProjectOfferingTaskDetail = 'GetProjectOfferingTaskDetail',
  AddProjectOfferingTask = 'AddProjectOfferingTask',
  UpdateProjectOfferingTask = 'UpdateProjectOfferingTask',
  RemoveProjectOfferingTask = 'RemoveProjectOfferingTask',

  //Announcement Banner
  ListSystemMessages = 'ListSystemMessages',
  RemoveSystemMessage = 'RemoveSystemMessage',
  UpdateSystemMessagePublishStatus = 'UpdateSystemMessagePublishStatus',
  AddSystemMessage = 'AddSystemMessage',
  GetSystemMessageDetail = 'GetSystemMessageDetail',
  UpdateSystemMessage = 'UpdateSystemMessage',
  //Automation
  ListRules = 'ListRules',
  ListDraftRules = 'ListDraftRules',
  CreateRule = 'CreateRule',
  UpdateRuleStatus = 'UpdateRuleStatus',
  GetRuleDetails = 'GetRuleDetails',
  GetRuleWorkflowDetails = 'GetRuleWorkflowDetails',
  RemoveRule = 'RemoveRule',
  UpdateRuleGeneralDetails = 'UpdateRuleGeneralDetails',
  ListProjects = 'ListProjects',
  PublishRule = 'PublishRule',
  ListTriggerComponents = 'ListTriggerComponents',
  ListCriteriaComponents = 'ListCriteriaComponents',
  ListActionComponents = 'ListActionComponents',
  RemoveRuleTrigger = 'RemoveRuleTrigger',
  RemoveRuleCriteria = 'RemoveRuleCriteria',
  RemoveRuleAction = 'RemoveRuleAction',
  UpdateTriggerForRule = 'UpdateTriggerForRule',
  UpdateCriteriaForRule = 'UpdateCriteriaForRule',
  UpdateActionForRule = 'UpdateActionForRule',
  GetTriggerDetailsOfRule = 'GetTriggerDetailsOfRule',
  GetCriteriaDetailsOfRule = 'GetCriteriaDetailsOfRule',
  GetActionDetailsOfRule = 'GetActionDetailsOfRule',
  ListRuleVersions = 'ListRuleVersions',
  ListTriggerComponentField = 'ListTriggerComponentFields',
  ListTriggerComponentFieldValues = 'ListTriggerComponentFieldValues',
  ListCriteriaComponentFields = 'ListCriteriaComponentFields',
  ListCriteriaComponentFieldValues = 'ListCriteriaComponentFieldValues',
  ListActionComponentFields = 'ListActionComponentFields',
  ListActionComponentFieldValues = 'ListActionComponentFieldValues',
  //Export Status Updates
  ExportTrackerObjectiveStatusUpdatesForMF = 'ExportTrackerObjectiveStatusUpdatesForMF1',
  ExportTrackerObjectiveStatusUpdatesForNonMF = 'ExportTrackerObjectiveStatusUpdatesForNonMF',
  ListObjectiveClientsForPartner = 'ListObjectiveClientsForPartner',
  //Marketplace
  ListMarketplaceObjectivesByCategory = 'ListMarketplaceObjectivesByCategory',
  SearchMarketplace = 'SearchMarketplace',
  ListMarketplacePartners = 'ListMarketplacePartners',
  GetMarketplacePartnerDetail = 'GetMarketplacePartnerDetail',
  ListPartnerRequestsForMarketplaceObjectives = 'ListPartnerRequestsForMarketplaceObjectives',
  UpdatePartnerRequestStatusForMarketplaceObjective = 'UpdatePartnerRequestStatusForMarketplaceObjective',
  RemovePartnerRequestForMarketplaceObjective = 'RemovePartnerRequestForMarketplaceObjective',
  ListPartnersForMarketplaceObjective = 'ListPartnersForMarketplaceObjective',
  ListReviewsForMarketplaceObjective = 'ListReviewsForMarketplaceObjective',
  ListClientsForMarketplaceObjective = 'ListClientsForMarketplaceObjective',
  PostReviewForObjective = 'PostReviewForObjective',
  ListReviewsForPartnerObjectivesListing = 'ListReviewsForPartnerObjectivesListing',
  ListClientsForPartner = 'ListClientsForPartner',
  ListClientObjectivesForPartner = 'ListClientObjectivesForPartner',
  UpdateMarketplaceObjectiveIcon = 'UpdateMarketplaceObjectiveIcon',
  GetOrganizationEnvironmentGeneralDetail = 'GetOrganizationEnvironmentGeneralDetail',
  GetOrganizationEnvironmentActiveDirectoryDetail = 'GetOrganizationEnvironmentActiveDirectoryDetail',
  GetOrganizationEnvironmentBackupDetail = 'GetOrganizationEnvironmentBackupDetail',
  GetOrganizationEnvironmentSolutionsDetail = 'GetOrganizationEnvironmentSolutionsDetail',
  GetOrganizationEnvironmentEmailDetail = 'GetOrganizationEnvironmentEmailDetail',
  UpdateOrganizationEnvironmentGeneralDetail = 'UpdateOrganizationEnvironmentGeneralDetail',
  UpdateOrganizationEnvironmentActiveDirectoryDetail = 'UpdateOrganizationEnvironmentActiveDirectoryDetail',
  UpdateOrganizationEnvironmentBackupDetail = 'UpdateOrganizationEnvironmentBackupDetail',
  UpdateOrganizationEnvironmentSolutionsDetail = 'UpdateOrganizationEnvironmentSolutionsDetail',
  UpdateOrganizationEnvironmentEmailDetail = 'UpdateOrganizationEnvironmentEmailDetail',
  ListTrackerObjectiveStatusUpdatesForNonMF = 'ListTrackerObjectiveStatusUpdatesForNonMF',
  ListTrackerObjectiveStatusUpdatesForMF = 'ListTrackerObjectiveStatusUpdatesForMF',
  ListOfferingsForTrackerObjectives = 'ListOfferingsForTrackerObjectives',
  GetRoadmapEconomicsOverview = 'GetRoadmapEconomicsOverview',
  GetRoadmapEconomicsWorkStreams = 'GetRoadmapEconomicsWorkStreams',
  ListProjectOfferingsForTracker = 'ListProjectOfferingsForTracker',
  GetOfferingForTrackerObjective = 'GetOfferingForTrackerObjective',
  UpdateOfferingForTrackerObjective = 'UpdateOfferingForTrackerObjective',
  RemoveOfferingFromTrackerObjective = 'RemoveOfferingFromTrackerObjective',
  AddOfferingForTrackerObjective = 'AddOfferingForTrackerObjective',
  ListProjectOfferingTasksForTracker = 'ListProjectOfferingTasksForTracker',
  GetOfferingTaskDetailsForTrackerObjective = 'GetOfferingTaskDetailsForTrackerObjective',
  UpdateOfferingTaskForTrackerObjective = 'UpdateOfferingTaskForTrackerObjective',
  RemoveOfferingTaskFromTrackerObjective = 'RemoveOfferingTaskFromTrackerObjective',
  AddOfferingTaskForTrackerObjective = 'AddOfferingTaskForTrackerObjective',
  ListOfferingTasksForTrackerObjective = 'ListOfferingTasksForTrackerObjective',
  ListObjectiveListingForPartner = 'ListObjectiveListingForPartner',
  ListPartnerRequestsForMarketplaceObjectivesForPartner = 'ListPartnerRequestsForMarketplaceObjectivesForPartner',
  AddEngagedPartiesToIncident = 'AddEngagedPartiesToIncident',
  ListEngagedParties = 'ListEngagedParties',
  DeleteEngagedParty = 'DeleteEngagedParty',
  ListPartnersForTrackerObjective = 'ListPartnersForTrackerObjective',
  ExportOrganizationEnvironmentDetail = 'ExportOrganizationEnvironmentDetail',
  CreatePartnerRequestForMarketplaceObjective = 'CreatePartnerRequestForMarketplaceObjective',
  AddPartnerToTrackerObjective = 'AddPartnerToTrackerObjective',
  RemovePartnerFromTrackerObjective = 'RemovePartnerFromTrackerObjective',

  UpdateMilestones = 'UpdateMilestones',
  UpdateStatusUpdates = 'UpdateStatusUpdates',

  // status update v3
  ListStatusUpdates = 'ListStatusUpdates',
  ListPublishedStatusUpdates = 'ListPublishedStatusUpdates',
  ListActivityFeedWidgetEvents = 'ListActivityFeedWidgetEvents',
  ListActivityListWidgetRecords = 'ListActivityListWidgetRecords',
  ListGlobalWidgets = 'ListGlobalWidgets',
  ListStatusUpdateAssociatedWidgets = 'ListStatusUpdateAssociatedWidgets',
  ListTaskSnapshots = 'ListTaskSnapshots',
  ListIOCSnapshots = 'ListIOCSnapshots',
  ListTimelineSnapshots = 'ListTimelineSnapshots',
  GetStatusUpdateDetail = 'GetStatusUpdateDetail',
  GetNotesWidgetDetail = 'GetNotesWidgetDetail',
  GetGlobalWidgetMetadata = 'GetGlobalWidgetMetadata',
  GetActivityListWidgetRecordDetail = 'GetActivityListWidgetRecordDetail',
  GetStatusUpdateEmailsStatistics = 'GetStatusUpdateEmailsStatistics',
  GetStatusUpdateLayoutDetail = 'GetStatusUpdateLayoutDetail',
  GetNotesWidgetImages = 'GetNotesWidgetImages',
  UpdateStatusUpdateFeatureActiveVersion = 'UpdateStatusUpdateFeatureActiveVersion',
  UpdateStatusUpdateWidgetOrder = 'UpdateStatusUpdateWidgetOrder',
  CreateGlobalWidget = 'CreateGlobalWidget',
  UpdateGlobalWidget = 'UpdateGlobalWidget',
  RemoveGlobalWidget = 'RemoveGlobalWidget',
  UpdateStatusUpdateAssociatedWidgets = 'UpdateStatusUpdateAssociatedWidgets',
  CreateActivityListWidgetRecord = 'CreateActivityListWidgetRecord',
  CreateActivityListWidgetCustomRecord = 'CreateActivityListWidgetCustomRecord',
  UpdateActivityListWidgetCustomRecord = 'UpdateActivityListWidgetCustomRecord',
  UpdateActivityListWidgetRecordOrder = 'UpdateActivityListWidgetRecordOrder',
  RemoveActivityListWidgetRecord = 'RemoveActivityListWidgetRecord',
  PublishStatusUpdate = 'PublishStatusUpdate',
  SendStatusUpdateViaEmail = 'SendStatusUpdateViaEmail',
  AddTaskSnapshot = 'AddTaskSnapshot',
  RemoveTaskSnapshot = 'RemoveTaskSnapshot',
  AddIOCSnapshot = 'AddIOCSnapshot',
  RemoveIOCSnapshot = 'RemoveIOCSnapshot',
  AddTimelineSnapshot = 'AddTimelineSnapshot',
  RemoveTimelineSnapshot = 'RemoveTimelineSnapshot',
  CloneStatusUpdate = 'CloneStatusUpdate',
  UpdateStatusUpdateLayout = 'UpdateStatusUpdateLayout',
  RemoveActivityListWidgetRecords = 'RemoveActivityListWidgetRecords',
  RemoveStatusUpdateDraft = 'RemoveStatusUpdateDraft',
  RemoveStatusUpdate = 'RemoveStatusUpdate',
  UploadNotesWidgetImages = 'UploadNotesWidgetImages',
  RemoveNotesWidgetImage = 'RemoveNotesWidgetImage',
  GetIncidentInitialEntryPointDetails = 'GetIncidentInitialEntryPointDetails',
  UpdateIncidentInitialEntryPointDetails = 'UpdateIncidentInitialEntryPointDetails',
  UpdateUserRoleForIncident = 'UpdateUserRoleForIncident',
  UpdateNotesWidgetDetail = 'UpdateNotesWidgetDetail',
  // Threat Actor Actions

  ListThreatActors = 'ListThreatActors',
  AddMOXFIVEInsightFlexibleField = 'AddMOXFIVEInsightFlexibleField',
  UploadTempAttachments = 'UploadTempAttachments',
  AddThreatActor = 'AddThreatActor',
  GetThreatActorGeneralDetail = 'GetThreatActorGeneralDetail',
  GetThreatActorChannelsDetail = 'GetThreatActorChannelsDetail',
  DownloadThreatActorHighlightAttachment = 'DownloadThreatActorHighlightAttachment',
  ListThreatActorHighlightAttachments = 'ListThreatActorHighlightAttachments',
  ListTargetedRegions = 'ListTargetedRegions',
  UpdateThreatActorGeneralDetails = 'UpdateThreatActorGeneralDetails',
  UpdateThreatActorChannelsDetails = 'UpdateThreatActorChannelsDetails',
  UpdateThreatActorThumbImage = 'UpdateThreatActorThumbImage',
  AddThreatActorAttachments = 'AddThreatActorAttachments',
  AddThreatActorHighlightAttachments = 'AddThreatActorHighlightAttachments',
  RemoveThreatActorHighlightAttachments = 'RemoveThreatActorHighlightAttachments',
  RemoveThreatActors = 'RemoveThreatActors',
  RemoveThreatActorHighlights = 'RemoveThreatActorHighlights',
  GetThreatActorHighlightAttachmentFile = 'GetThreatActorHighlightAttachmentFile',
  AddThreatActorHighlight = 'AddThreatActorHighlight',
  ListThreatActorHighlights = 'ListThreatActorHighlights',
  ListHighlightRequestsForThreatActor = 'ListHighlightRequestsForThreatActor',
  GetThreatActorStats = 'GetThreatActorStats',
  UpdateThreatActorStats = 'UpdateThreatActorStats',
  UpdateThreatActorHighlight = 'UpdateThreatActorHighlight',
  UpdateThreatActorHighlightStatus = 'UpdateThreatActorHighlightStatus',
  UpdateThreatActorStatsConfiguration = 'UpdateThreatActorStatsConfiguration',
  ListOrganizationsOfIncidents = 'ListOrganizationsOfIncidents',
  ListOrganizationsForUserOfIncidents = 'ListOrganizationsForUserOfIncidents',
  ListIncidentsOfOrganization = 'ListIncidentsOfOrganization',
  ListIncidentsOfOrganizationUser = 'ListIncidentsOfOrganizationUser',
  ListOrganizationsOfResilience = 'ListOrganizationsOfResilience',
  ListOrganizationsForUserOfResilience = 'ListOrganizationsForUserOfResilience',
  ListObjectivesOfOrganization = 'ListObjectivesOfOrganization',
  ListObjectivesOfOrganizationUser = 'ListObjectivesOfOrganizationUser',



  // NEW AUTH ACTIONS
  ListUserLoggedinDevicesOfOrganization = 'ListUserLoggedinDevicesOfOrganization',
  ResetUserMFAEnrollmentsOfOrganization = 'ResetUserMFAEnrollmentsOfOrganization',
  ListUserMFAEnrollmentsOfOrganization =  'ListUserMFAEnrollmentsOfOrganization',
  AddUserToOrganization = 'AddUserToOrganization',
  UnlockUserOfOrganization = 'UnlockUserOfOrganization',
  UpdateUserEmailOfOrganization = 'UpdateUserEmailOfOrganization',
  LogoutUserFromLoggedinDevicesOfOrganization = 'LogoutUserFromLoggedinDevicesOfOrganization',
  ListAuthenticationLogs = 'ListAuthenticationLogs',
  CreateOragnizationConnection = 'CreateOragnizationConnection',
  RemoveOrganizationConnection = 'RemoveOrganizationConnection',
  UpdateOrganizationConnection = 'UpdateOrganizationConnection',
  ListOrganizationConnections = 'ListOrganizationConnections',
  GetOrganizationConnectionDetails = 'GetOrganizationConnectionDetails',
  DownloadOrganizationConnectionFile = 'DownloadOrganizationConnectionFile',
  ListEnterpriseConnectionTypes = 'ListEnterpriseConnectionTypes',
  ListEnterpriseConnectionTypeFields = 'ListEnterpriseConnectionTypeFields',
  UploadTemporaryFiles = 'UploadTemporaryFiles',

}
