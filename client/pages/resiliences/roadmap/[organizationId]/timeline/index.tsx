import React from 'react';

import {
  ResilienceRoadMapWrapper,
} from '../../../../../src/modules/resiliences/component/roadmap/roadmap-tabs/resilience-roadmap-wrapper/resilience-roadmap-wrapper';
import Seo from '../../../../../src/modules/shared/components/seo/seo';
import { RESILIENCE_ROUTE } from '../../../../../src/constants/organization-type-tab-mapping';

export default function ResilienceTimeLinePage({ organizationId  }) {
  return (
    <>
      <Seo siteTitle="Roadmap Timeline"/>
        <ResilienceRoadMapWrapper organizationId={organizationId} pageName={RESILIENCE_ROUTE.TIMELINE} />

    </>
  );
}

export function getServerSideProps(context) {
  return {
    props: {
      organizationId: context.query.organizationId,
    },
  };
}
