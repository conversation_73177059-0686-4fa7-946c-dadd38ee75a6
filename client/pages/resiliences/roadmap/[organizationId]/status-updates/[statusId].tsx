import React from 'react';
import {
  ResilienceRoadMapWrapper,
} from '../../../../../src/modules/resiliences/component/roadmap/roadmap-tabs/resilience-roadmap-wrapper/resilience-roadmap-wrapper';
import { RESILIENCE_ROUTE } from '../../../../../src/constants/organization-type-tab-mapping';
import Seo from '../../../../../src/modules/shared/components/seo/seo';

export default function OrganizationResiliencePage({ organizationId, statusId }) {
  return (
    <>
      <Seo siteTitle="Roadmap Status Update Details" />
        <ResilienceRoadMapWrapper organizationId={organizationId} pageName={RESILIENCE_ROUTE.STATUS_UPDATES} statusUpdateId={statusId} />
    </>

  );
}

export function getServerSideProps(context) {
  return {
    props: {
      organizationId: context.query.organizationId,
      statusId: context.query.statusId,
    },
  };
}
