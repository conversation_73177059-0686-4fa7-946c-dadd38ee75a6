import React from 'react';
import Seo from '../../../../../src/modules/shared/components/seo/seo';

import {
  ResilienceRoadMapWrapper,
} from '../../../../../src/modules/resiliences/component/roadmap/roadmap-tabs/resilience-roadmap-wrapper/resilience-roadmap-wrapper';
import { RESILIENCE_ROUTE } from '../../../../../src/constants/organization-type-tab-mapping';

export default function ResilienceStatusUpdatesView({ organizationId }) {


  return (
        <>
            <Seo siteTitle="Roadmap Status Updates" />

                <ResilienceRoadMapWrapper organizationId={organizationId} pageName={RESILIENCE_ROUTE.STATUS_UPDATES} />
        </>
  );
}
export function getServerSideProps(context) {
  return {
    props: {
      organizationId: context.query.organizationId,
    },
  };
}
