import Seo from '../../../src/modules/shared/components/seo/seo';
import React from 'react';
import CanIHavePermission from '../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../src/enums/actions.enum';
import RoadmapListing from '../../../src/modules/resiliences/component/roadmap/roadmap-listing/roadmap-listing';

export default function CreateResilienceStatusUpdate() {
  return (
        <>
            <Seo siteTitle="Roadmap" />
            <CanIHavePermission of={[Actions.ListTrackers, Actions.ListTrackersForOrganizationUser, Actions.ListTrackersForOrganization]} any={true}>
              <RoadmapListing/>
            </CanIHavePermission>
        </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
