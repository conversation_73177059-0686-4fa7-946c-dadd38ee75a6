import Seo from '../../../src/modules/shared/components/seo/seo';
import React from 'react';
import TrackerAddEdit from '../../../src/modules/resiliences/component/tracker/tracker-add-edit/tracker-add-edit';
import CanIHavePermission from '../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../src/enums/actions.enum';

export default function CreateResilienceStatusUpdate() {
  return (
        <>
            <Seo siteTitle="Roadmap" />
            <CanIHavePermission of={[Actions.CreateTracker]} >
              <TrackerAddEdit />
            </CanIHavePermission>
        </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
