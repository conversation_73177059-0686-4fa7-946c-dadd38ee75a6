import React, { useEffect } from 'react';
import type { ReactElement, ReactNode } from 'react';
import type { NextPage } from 'next';
import type { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import { Provider } from 'react-redux';

import { useAppDispatch } from '../src/redux/hooks';
import { loadingSessionData } from '../src/redux/slices/sidebar';
import { store } from '../src/redux/store';
import { authPaths, noHeaderSidebarPaths } from '../src/constants/constant';
import Layout from '../src/modules/layout/layout';
import '../src/styles/signin-mfa.scss';
import '../src/styles/global.scss';
import '../src/styles/status-update-layout.scss';
import '../src/styles/status-update-presenter-layout.scss';
import '../src/styles/grid.scss';
import '../src/styles/dragdrop.scss';
import '../src/styles/datepicker_overwrite.scss';
import '../src/styles/form.scss';
import '../src/styles/model.scss';
import '../src/styles/incidentcommon.scss';
import '../src/styles/_table.scss';
import '../src/styles/daterangepicker_style.scss';
import '../src/styles/daterangepicker_default.scss';
import '../src/styles/resilience_common.scss';
import '../src/styles/webnotification.scss';
import '../node_modules/react-grid-layout/css/styles.css';
import '../node_modules/react-resizable/css/styles.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import '../src/styles/react-multi-email.scss';
import '../src/styles/miltestone-task.scss';
import '../src/styles/auth.scss';
import AppLoading from '../src/modules/shared/components/app-loading/app-loading';
import CheckLogin from '../src/modules/shared/components/check-login/check-login';
import NotificationWrapper from '../src/modules/shared/components/notification/notification';
import WebNotification from '../src/modules/shared/components/web-notification/web-notification';
import DisplayAnnouncementBannerComponent
  from '../src/modules/platform-settings/platform-messages/announcement-banner/display-announcement-banner/display-announcement-banner';
import { AppWrapper } from '../src/modules/app-wrapper/app-wrapper';


type NextPageWithLayout = NextPage & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};


function SetBreadCrum() {
  const dispatch = useAppDispatch();

  useEffect(() => {
    const obj: any = {};

    if (localStorage.getItem('organizationDetail') !== null) {
      obj.organizationDetail = JSON.parse(localStorage.getItem('organizationDetail'));
    }

    if (localStorage.getItem('userDetail') !== null) {
      obj.userDetail = JSON.parse(localStorage.getItem('userDetail'));
    }

    if (localStorage.getItem('policyDetail') !== null) {
      obj.policyDetail = JSON.parse(localStorage.getItem('policyDetail'));
    }

    if (Object.keys(obj).length > 0){
      dispatch(loadingSessionData(obj));
    }
  }, []);

  return <></>;
}


export default function App({ Component, pageProps }: AppPropsWithLayout) {
  const router = useRouter();
  const isAuthPath = () => authPaths.some((path) => {
    return router.pathname.indexOf(path) !== -1;
  });
  const isHideHeaderSidebar = () => noHeaderSidebarPaths.some((path) => {
    return router.pathname.indexOf(path) !== -1;
  });
  const is404NotFoundPath = () => {
    return router.pathname.indexOf('/404') === 0;
  };

  const renderComponent = () => (isAuthPath() || is404NotFoundPath() || isHideHeaderSidebar()
    ? <AppLoading><Component {...pageProps} /></AppLoading>
    : (
          <>
              <AppLoading>
                  <Layout>
                      <Component {...pageProps} />
                  </Layout>
              </AppLoading>
          </>
    ));
  const renderAnnouncementBannerComponent = () => (isAuthPath() || is404NotFoundPath() || isHideHeaderSidebar() ? null : <DisplayAnnouncementBannerComponent />);
  const renderInitComponent = () => {
    return (
        <>
            <NotificationWrapper/>
            <SetBreadCrum />
            <CheckLogin/>
            <WebNotification />
            { renderAnnouncementBannerComponent() }
        </>
    );
  };
  return (
      <Provider store={store}>

          <AppWrapper>

                { renderInitComponent() }
                <div className="__main none" id={'main'}>
                    {renderComponent()}
                </div>
          </AppWrapper>
      </Provider>

  );
}
