import Dashboard from '../src/modules/dashboard/component/dashboard';
import Seo from '../src/modules/shared/components/seo/seo';
import { Actions } from '../src/enums/actions.enum';
import CanIHavePermission from '../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';

export default function Home() {
  return (
        <>
            <Seo siteTitle="Dashboard" />
            <CanIHavePermission of={[Actions.GetPlatformDashboardDetails]}>
              <Dashboard/>
            </CanIHavePermission>
        </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
