import React from 'react';
import Document, {
  Html, Head, Main, NextScript,
} from 'next/document';

export default class MyDocument extends Document {

  render() {

    return (
            <Html lang="en">
                <Head>
                    <link
                        rel="stylesheet"
                        href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
                    />
                    <link rel="icon" href=""/>
                    <script
                        dangerouslySetInnerHTML={{
                          __html: `
                        window.dataLayer = window.dataLayer || [];
                        window.dataLayer.push({
                            'environment': (function() {
                                var hostname = window.location.hostname;
                                if (hostname.includes('dev')) return 'Dev';
                                if (hostname.includes('test')) return 'Test';
                                if (hostname.includes('demo')) return 'Demo';
                                if (hostname === 'platform.moxfive.com') return 'Production';
                                return 'Dev';
                            })()
                        });
                        `,
                        }}
                    />
                    {/* Google Tag Manager - Global Site Tag */}
                    <script
                        id="gtm-script"
                        dangerouslySetInnerHTML={{
                          __html: `
                          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                            })(window,document,'script','dataLayer','${process.env.NEXT_PUBLIC_GTM_ID}');
                        `,
                        }}
                    ></script>
                    {/* Pendo Script */}
                    <script
                        id="pendo-script"
                        dangerouslySetInnerHTML={{
                          __html: `
                              (function(apiKey){
    (function(p,e,n,d,o){var v,w,x,y,z;o=p[d]=p[d]||{};o._q=o._q||[];
    v=['initialize','identify','updateOptions','pageLoad','track'];for(w=0,x=v.length;w<x;++w)(function(m){
        o[m]=o[m]||function(){o._q[m===v[0]?'unshift':'push']([m].concat([].slice.call(arguments,0)));};})(v[w]);
        y=e.createElement(n);y.async=!0;y.src='https://cdn.pendo.io/agent/static/'+apiKey+'/pendo.js';
        z=e.getElementsByTagName(n)[0];z.parentNode.insertBefore(y,z);})(window,document,'script','pendo');
})('${process.env.NEXT_PUBLIC_PENDO_API_KEY}');
                            `,
                        }}
                    />
                    {/* Google Map API */}
                    <script
                        src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places&v=weekly`}
                        defer
                    ></script>
                </Head>
                <body>
                {/* Google Tag Manager (noscript) */}
                <noscript
                    dangerouslySetInnerHTML={{
                      __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.NEXT_PUBLIC_GTM_ID}" height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
                    }}
                ></noscript>
                <div id="place-service-div"></div>
                <Main/>
                <NextScript/>
                </body>
            </Html>
    );
  }
}

// `getInitialProps` belongs to `_document` (instead of `_app`),
// it's compatible with server-side generation (SSG).
MyDocument.getInitialProps = async (ctx) => {
  // Render app and page and get the context of the page with collected side effects.
  const originalRenderPage = ctx.renderPage;

  ctx.renderPage = () => originalRenderPage({});

  const initialProps = await Document.getInitialProps(ctx);

  return {
    ...initialProps,
    // Styles fragment is rendered after the app and page rendering finish.
    styles: [...React.Children.toArray(initialProps.styles)],
  };
};
