import React from 'react';
import Seo from '../../../src/modules/shared/components/seo/seo';
import { CanIHaveNewAuthMethod } from '../../../src/modules/auth/component/can-i-have-new-auth-method';
import {
  AuthenticationConnectionListWrapper,
} from '../../../src/modules/authentication/connection-list/list/authentication-connection-list-wrapper';

export default function AuthenticationCom() {
  return (
        <>
            <Seo siteTitle="Authentication" />

            <CanIHaveNewAuthMethod isShowNothingExist={true}>
                <AuthenticationConnectionListWrapper/>
            </CanIHaveNewAuthMethod>
        </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
