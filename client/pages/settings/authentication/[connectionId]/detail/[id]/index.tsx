import React from 'react';
import Seo from '../../../../../../src/modules/shared/components/seo/seo';
import { ConnectionDetail } from '../../../../../../src/modules/authentication/connection-detail/connection-detail';
import { Actions } from '../../../../../../src/enums/actions.enum';
import CanIHavePermission
  from '../../../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { CanIHaveNewAuthMethod } from '../../../../../../src/modules/auth/component/can-i-have-new-auth-method';


export default function AuthenticationConnectionDetail() {
  return (
        <>
            <CanIHavePermission of={[Actions.GetOrganizationConnectionDetails, Actions.ListEnterpriseConnectionTypeFields]}>
                <Seo siteTitle="Connection Detail"/>
                <CanIHaveNewAuthMethod isShowNothingExist={true}>

                        <ConnectionDetail/>
                </CanIHaveNewAuthMethod>
            </CanIHavePermission>

        </>
  );
}

export function getServerSideProps() {
  return {
    props: {},
  };
}
