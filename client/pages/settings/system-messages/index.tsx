import { Actions } from '../../../src/enums/actions.enum';
import AnnouncementBannerList from '../../../src/modules/platform-settings/platform-messages/announcement-banner/announcement-banner-list';
import CanIHavePermission from '../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import Seo from '../../../src/modules/shared/components/seo/seo';

export default function AnnouncementBanner() {
  return (
        <>
            <Seo siteTitle="System Messages" />
              <CanIHavePermission of={[Actions.ListSystemMessages]}>
         <AnnouncementBannerList/>
              </CanIHavePermission>
        </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
