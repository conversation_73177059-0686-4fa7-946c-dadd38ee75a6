import { Actions } from '../../../src/enums/actions.enum';
import AnnouncementBannerAddEdit from '../../../src/modules/platform-settings/platform-messages/announcement-banner/announcement-banner-add-edit';
import CanIHavePermission from '../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import Seo from '../../../src/modules/shared/components/seo/seo';

export default function AnnouncementBannerAdd() {
  return (
        <>
            <Seo siteTitle="System Messages" />
            <CanIHavePermission of={[Actions.AddSystemMessage]}>
              <AnnouncementBannerAddEdit announcementId={null}/>
            </CanIHavePermission>
            </>

  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
