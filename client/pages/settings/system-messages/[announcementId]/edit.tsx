import React from 'react';
import { Actions } from '../../../../src/enums/actions.enum';
import AnnouncementBannerAddEdit from '../../../../src/modules/platform-settings/platform-messages/announcement-banner/announcement-banner-add-edit';
import CanIHavePermission from '../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import Seo from '../../../../src/modules/shared/components/seo/seo';

export default function AnnouncementBannerEdit({ announcementId }) {
  return (
        <>
            <Seo siteTitle="System Messages Edit"/>
            <CanIHavePermission of={[Actions.GetSystemMessageDetail, Actions.UpdateSystemMessage]} >
              <AnnouncementBannerAddEdit announcementId={announcementId}/>
            </CanIHavePermission>
        </>

  );
}
export function getServerSideProps(context) {
  return {
    props: {
      announcementId: context.query.announcementId,
    },
  };
}
