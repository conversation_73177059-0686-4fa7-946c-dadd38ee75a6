import React from 'react';
import AnnouncementBannerAddEdit from '../../../../src/modules/platform-settings/platform-messages/announcement-banner/announcement-banner-add-edit';
import Seo from '../../../../src/modules/shared/components/seo/seo';

export default function AnnouncementBannerEdit({ announcementId }) {
  return (
        <>
            <Seo siteTitle="Announcement Banner Edit"/>
            {/* TODO: Need to Add Permission */}
            <AnnouncementBannerAddEdit announcementId={announcementId} isClone={true}/>
        </>

  );
}
export function getServerSideProps(context) {
  return {
    props: {
      announcementId: context.query.announcementId,
    },
  };
}
