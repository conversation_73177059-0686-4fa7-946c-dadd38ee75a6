import React from 'react';
import { Actions } from '../../../../src/enums/actions.enum';
import ManagePlatformNotificationList from '../../../../src/modules/notifications/manage-platform-notification/list';
import CanIHavePermission from '../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import Seo from '../../../../src/modules/shared/components/seo/seo';

export default function NotificationConfiguration() {
  return (
        <>
            <Seo siteTitle="Manage Platform Notification"/>
            <CanIHavePermission of={[Actions.ListNotificationConfiguration]}>
                <ManagePlatformNotificationList/>
            </CanIHavePermission>
        </>
  );
}

export function getServerSideProps() {
  return {
    props: {},
  };
}
