import React from 'react';
import { Actions } from '../../../../src/enums/actions.enum';
import ManageNotificationsPreferencesList from '../../../../src/modules/notifications/manage-platform-notification/preferences-list';
import CanIHavePermission from '../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import Seo from '../../../../src/modules/shared/components/seo/seo';

export default function NotificationPreferences() {
  return (
        <>
            <Seo siteTitle="Manage Platform Notifications Preferences"/>
            <CanIHavePermission of={[Actions.ListNotificationPreference]}>
                <ManageNotificationsPreferencesList/>
            </CanIHavePermission>
        </>
  );
}

export function getServerSideProps() {
  return {
    props: {},
  };
}
