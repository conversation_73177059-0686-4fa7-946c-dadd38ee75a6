import React from 'react';
import CreateUpdateRule from '../../../../src/modules/automation/create-update-rule/create-update-rule';
import Seo from '../../../../src/modules/shared/components/seo/seo';
import CanIHavePermission from '../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../../src/enums/actions.enum';

export default function CreateRules() {
  return (
    <>
      <Seo siteTitle="Automation Rules" />
      <CanIHavePermission of={[Actions.CreateRule]} any={true} >
        <CreateUpdateRule ruleId={null}/>
      </CanIHavePermission>
    </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
