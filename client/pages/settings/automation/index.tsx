import React from 'react';
import PlatformAutomation from '../../../src/modules/automation/automation';
import Seo from '../../../src/modules/shared/components/seo/seo';
import CanIHavePermission from '../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../src/enums/actions.enum';

export default function IncidentProjectStatusMilestonesView() {
  return (
    <>
      <Seo siteTitle="Automation Rules" />
      <CanIHavePermission of={[Actions.ListRules, Actions.ListDraftRules]} any={true} >
        <PlatformAutomation/>
      </CanIHavePermission>
    </>
  );
}
export function getServerSideProps() {
  return {
    props: {},
  };
}
