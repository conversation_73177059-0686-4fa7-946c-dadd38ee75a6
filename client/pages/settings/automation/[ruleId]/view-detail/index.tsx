import React from 'react';
import Seo from '../../../../../src/modules/shared/components/seo/seo';
import ViewRule from '../../../../../src/modules/automation/view-rule/view-rule';

export default function CreateRules({ ruleId }) {
  return (
    <>
      <Seo siteTitle="Automation Rule Details" />
      <ViewRule ruleId={ruleId}/>
    </>
  );
}
export function getServerSideProps(context) {
  return {
    props: {
      ruleId: context.query.ruleId,
    },
  };
}
