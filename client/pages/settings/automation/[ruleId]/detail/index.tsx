import React from 'react';
import CreateUpdateRule from '../../../../../src/modules/automation/create-update-rule/create-update-rule';
import Seo from '../../../../../src/modules/shared/components/seo/seo';
import CanIHavePermission from '../../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../../../src/enums/actions.enum';

export default function CreateRules({ ruleId }) {
  return (
    <>
      <Seo siteTitle="Automation Rule Details" />
      <CanIHavePermission of={[Actions.GetRuleDetails]} any={true} >
        <CreateUpdateRule ruleId={ruleId}/>
      </CanIHavePermission>
    </>
  );
}
export function getServerSideProps(context) {
  return {
    props: {
      ruleId: context.query.ruleId,
    },
  };
}
