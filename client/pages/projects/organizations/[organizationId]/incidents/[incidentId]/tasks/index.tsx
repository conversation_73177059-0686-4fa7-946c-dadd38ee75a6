import React from 'react';
import Seo from '../../../../../../../src/modules/shared/components/seo/seo';
import CanIHavePermission from '../../../../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../../../../../src/enums/actions.enum';
import { IncidentWrapper } from '../../../../../../../src/modules/incident/component/incident-wrapper/incident-wrapper';
import { MilestoneTask } from '../../../../../../../src/modules/incident/component/incident-detail/milestone-task/milestone-task';

export default function IncidentProjectStatusTasksView({ incidentId }) {
  return (
    <>
      <Seo siteTitle="Tasks" />
      <CanIHavePermission of={[Actions.ListProjectTasks]} incident={incidentId}>
        <IncidentWrapper incidentId={incidentId}>
          <MilestoneTask incidentId={incidentId} />
        </IncidentWrapper>
      </CanIHavePermission>
    </>
  );
}
export function getServerSideProps(context) {
  return {
    props: {
      incidentId: context.query.incidentId,
    },
  };
}
