import React from 'react';
import Seo from '../../../../../../src/modules/shared/components/seo/seo';
import CanIHavePermission from '../../../../../../src/modules/shared/components/can-i-have-permissions/can-i-have-permission';
import { Actions } from '../../../../../../src/enums/actions.enum';
import { OrganizationDetailWrapper } from '../../../../../../src/modules/organization/component/organization-detail-new/organization-detail-new-wrapper/organization-detail-new-wrapper';
import { ORGANIZATION_PROFILE_OPTION } from '../../../../../../src/constants/organization-type-tab-mapping';
import {
  OrganizationMemberUpperAndSideMenu,
} from '../../../../../../src/modules/organization/component/organization-detail-new/tabs-detail/members/detail/organization-member-upper-and-side-menu';

export default function OrganizationMembersPage({ userId, organizationId }) {
  return (
    <>
      <Seo siteTitle="Organization Members Details" />
      <CanIHavePermission of={[Actions.GetOrganizationUserDetail]}>
        <OrganizationDetailWrapper organizationId={organizationId} page={ORGANIZATION_PROFILE_OPTION.MEMBERS}>
            <OrganizationMemberUpperAndSideMenu userId={userId} organizationId={organizationId} isProjectsModule={true}/>
        </OrganizationDetailWrapper>
      </CanIHavePermission>
    </>

  );
}

export function getServerSideProps(context) {
  return {
    props: {
      userId: context.query.memberId,
      organizationId: context.query.organizationId,
    },
  };
}
