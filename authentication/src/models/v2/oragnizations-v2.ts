import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { ReferenceValueV2 } from "../../interfaces/v2/reference-value-v2";
import { OrganizationBillingAddress, OrganizationLocation } from "../../interfaces";
import { OrganizationTypeDoc } from "./../organization-type";
import { referenceValueMongooseSchemaV2 } from "../../util/v2/reference-value-mongoose-schema-v2";
import { organizationWatcher } from "../../watchers/v2/organizations/organization-name-sync/organization-name-sync.watcher";

// An interface that describes the properties to define a new organization
interface OrganizationV2Attrs {
    azureId: string,

    // Company Information
    name: string,
    bucketName?: string | null,
    organizationTypes: ReferenceValueV2[],
    industry?: ReferenceValueV2 | null,
    serviceLines?: ReferenceValueV2[] | null,
    website?: string,
    highLevelCompanyInformation?: string,
    descriptionOfEnvironment?: string,
    officeLocations?: OrganizationLocation[],
    numberOfEmployees?: number,
    numberOfITStaff?: number,
    itStaffLocation?: OrganizationLocation[],
    activePartner?: boolean,
    profile?: string | null,
    favicon?: string | null,
    imageAndLogoSame?: boolean,

    // Hotline details
    moxfiveHotline?: boolean,
    hotlineEmail?: string,
    hotlinePhoneNumber?: string,

    // Contact details
    msaSignatureDate?: string,
    billingContactName?: string,
    billingContactEmail?: string,
    billingContactPhone?: string,
    billingAddresses?: OrganizationBillingAddress[]

    // Partner details
    partnerType?: ReferenceValueV2,
    onboardedDate?: string,
    coverageStates?: ReferenceValueV2[],
    offerings?: ReferenceValueV2[],
    partnerEula?: string,
    partnerTermsConditions?: string,
    inboundRequestInfo?: string,
    numberOfPMs?: number,
    numberOfLeads?: number,
    numberOfEngineers?: number,
    moxfivePMSponsor?: ReferenceValueV2,
    moxfiveTASponsor?: ReferenceValueV2,
    moxfiveSalesSponsor?: ReferenceValueV2,
    languages?: ReferenceValueV2[],
    shortDescription?: string | null;

    owner: string[]
    member: string[]
}

// An interface that describes the properties that Organization Document has
interface OrganizationV2Doc extends mongoose.Document {
    azureId: string,

    // Company Information
    name: string,
    bucketName?: string | null,
    organizationTypes: ReferenceValueV2[] | OrganizationTypeDoc[],
    industry?: ReferenceValueV2 | null,
    serviceLines?: ReferenceValueV2[] | null,
    website?: string,
    highLevelCompanyInformation?: string,
    descriptionOfEnvironment?: string,
    officeLocations?: OrganizationLocation[],
    numberOfEmployees?: number,
    numberOfITStaff?: number,
    itStaffLocation?: OrganizationLocation[],
    activePartner?: boolean,
    profile?: string | null,
    favicon?: string | null,
    imageAndLogoSame?: boolean,

    // Hotline details
    moxfiveHotline?: boolean,
    hotlineEmail?: string,
    hotlinePhoneNumber?: string,

    // Contact details
    msaSignatureDate?: string,
    billingContactName?: string,
    billingContactEmail?: string,
    billingContactPhone?: string,
    billingAddresses?: OrganizationBillingAddress[]

    // Partner details
    partnerType?: ReferenceValueV2,
    onboardedDate?: string,
    coverageStates?: ReferenceValueV2[],
    offerings?: ReferenceValueV2[],
    partnerEula?: string,
    partnerTermsConditions?: string,
    inboundRequestInfo?: string,
    numberOfPMs?: number,
    numberOfLeads?: number,
    numberOfEngineers?: number,
    moxfivePMSponsor?: ReferenceValueV2,
    moxfiveTASponsor?: ReferenceValueV2,
    moxfiveSalesSponsor?: ReferenceValueV2,
    languages?: ReferenceValueV2[],
    shortDescription: string | null

    createdAt: string;
    updatedAt: string;
    isEnabled: boolean;
    version: number;
    owner: string[];
    member: string[];
}

// An interface that describes the properties that a Organization model has
interface OrganizationModel extends mongoose.Model<OrganizationV2Doc> {
    build(attrs: OrganizationV2Attrs): OrganizationV2Doc,
    findUserAlreadyInAnyOrganization(userIds: string[]): Promise<OrganizationV2Doc | null>,
    findUserAlreadyInOtherOrganization(userIds: string[]): Promise<OrganizationV2Doc[] | null>
}

const billingAddressV2Schema = new mongoose.Schema({
    addressline1: {
        type: String,
        required: true
    },
    addressline2: {
        type: String,
        default: null
    },
    city: {
        type: String,
        required: true
    },
    cityShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        required: true
    },
    stateShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true
    },
    countryShortName: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const locationV2Schema = new mongoose.Schema({
    addressline1: {
        type: String,
        required: true
    },
    addressline2: {
        type: String,
        default: null
    },
    city: {
        type: String,
        required: true
    },
    cityShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        required: true
    },
    stateShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true
    },
    countryShortName: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const organizationV2Schema = new mongoose.Schema(
    {
        azureId: {
            type: String,
            // unique: true,
            required: true
        },

        // Company information
        profile: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Files",
            default: null
        },
        favicon: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Files",
            default: null
        },
        imageAndLogoSame: {
            type: Boolean,
            default: false
        },
        name: {
            type: String,
            required: true
        },
        bucketName: {
            type: String,
            default: null
        },
        organizationTypes: {
            type: [referenceValueMongooseSchemaV2("OrganizationType")],
            required: true
        },
        industry: {
            type: referenceValueMongooseSchemaV2(),
            default: undefined
        },
        serviceLines: {
            type: [referenceValueMongooseSchemaV2()],
            default: undefined
        },
        website: String,
        highLevelCompanyInformation: String,
        descriptionOfEnvironment: String,
        officeLocations: {
            type: [locationV2Schema],
            default: undefined
        },
        numberOfEmployees: Number,
        numberOfITStaff: Number,
        itStaffLocation: {
            type: [locationV2Schema],
            default: undefined
        },
        activePartner: Boolean,

        // Hotline details
        moxfiveHotline: Boolean,
        hotlineEmail: String,
        hotlinePhoneNumber: String,

        // Contact details
        msaSignatureDate: Date,
        billingContactName: String,
        billingContactEmail: String,
        billingContactPhone: String,
        billingAddresses: {
            type: [billingAddressV2Schema],
            default: undefined
        },

        // Partner details
        partnerType: {
            type: referenceValueMongooseSchemaV2(),
            default: undefined
        },
        onboardedDate: Date,
        coverageStates: {
            type: [referenceValueMongooseSchemaV2()],
            default: undefined
        },
        offerings: {
            type: [referenceValueMongooseSchemaV2()],
            default: undefined
        },
        partnerEula: String,
        partnerTermsConditions: String,
        inboundRequestInfo: String,
        numberOfPMs: Number,
        numberOfLeads: Number,
        numberOfEngineers: Number,
        moxfivePMSponsor: {
            type: referenceValueMongooseSchemaV2("UsersV2"),
        },
        moxfiveTASponsor: {
            type: referenceValueMongooseSchemaV2("UsersV2"),
        },
        moxfiveSalesSponsor: {
            type: referenceValueMongooseSchemaV2("UsersV2"),
        },
        languages: {
            type: [referenceValueMongooseSchemaV2()],
            default: undefined
        },
        shortDescription: {
            type: String,
            default: null
        },
        createdAt: {
            type: Date,
            required: true,
            default: Date.now
        },
        updatedAt: {
            type: Date,
            required: true,
            default: Date.now
        },
        isEnabled: {
            type: Boolean,
            required: true,
            default: true
        },
        owner: [
            {
                type: String,
            }
        ],
        member: [
            {
                type: String,
            }
        ]
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

organizationV2Schema.set("versionKey", "version");
organizationV2Schema.plugin(updateIfCurrentPlugin);

organizationV2Schema.statics.build = (attrs: OrganizationV2Attrs) => {
    return new OrganizationV2(attrs);
};

organizationV2Schema.statics.findUserAlreadyInAnyOrganization = async (userIds: string[]) => {
    const existingUsers = await OrganizationV2.findOne({
        $or: [
            { owner: { $in: userIds } },
            { member: { $in: userIds } }
        ]
    }).lean().exec();

    return existingUsers;
};

organizationV2Schema.statics.findUserAlreadyInOtherOrganization = async (userIds: string[]) => {
    const existingUsers = await OrganizationV2.find({
        $or: [
            { owner: { $in: userIds } },
            { member: { $in: userIds } }
        ]
    }).lean().exec();

    return existingUsers;
};

organizationV2Schema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

const OrganizationV2 = mongoose.model<OrganizationV2Doc, OrganizationModel>("OrganizationsV2", organizationV2Schema);

export { OrganizationV2, OrganizationV2Attrs, OrganizationV2Doc };
OrganizationV2.watch().on("change", organizationWatcher);
