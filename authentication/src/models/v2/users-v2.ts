import mongoose, { ClientSession } from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { UserAzureDetails } from "../../interfaces";
import { natsWrapper } from "../../nats-wrapper";
import { UserCreatedPublisher } from "../../events/publishers/user-created-publisher";
import { generateSearchKeys } from "@moxfive-llc/common";
import { getSearchFields, getUserName } from "../../util";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { UserLocation } from "../../interfaces/user-location";
import { referenceValueMongooseSchemaV2 } from "../../util/v2/reference-value-mongoose-schema-v2";
import { ReferenceValueV2 } from "../../interfaces/v2/reference-value-v2";
import { userUpdatedPublisherV2Wrapper } from "../../util/v2/user-updated-publisher-wrapper";
import { OrganizationV2Doc } from "./oragnizations-v2";
import { userV2ToV1Sync } from "../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../enums/operation-types.enum";
import { MemberDetail } from "../../interfaces/add-user-detail-v3";
import { LockTypesEnum } from "../../enums/lock-types.enum";

interface IUserDevices {
    id: mongoose.Types.ObjectId,
    device: string,
    passwordLogin: boolean,
    loginDateTime?: string,
    expirationDateTime?: string | number | Date
}

// An interface that describes the properties to define a new User
interface UserV2Attrs {
    azureId: string,
    email: string,
    name?: string,
    displayName?: string,
    firstName?: string,
    lastName?: string,
    userLocation?: UserLocation | null,
    officePhone?: string,
    isEnabled?: boolean,
    policyIds?: string[] | null,
    jobTitle?: string,
    lastSignIn?: string,
    organization?: ReferenceValueV2,
    organizationTypes?: ReferenceValueV2[] | null,
    isOwner?: boolean | null,
    role?: ReferenceValueV2 | null,
    keys: string[],
    isEmailVerified?: boolean,
    externalAuth0LoginUserId?: string | null,
    emailVerificationToken?: string | null,
    emailVerificationTokenExpiry?: string | number | null,
}

interface UserEventAttr {
    id: string;
    version: number;
}

// An interface that describes the properties that User Document has
interface UserV2Doc extends mongoose.Document {
    id: string;
    azureId: string,
    email: string,
    name: string,
    displayName: string,
    isEnabled: boolean,
    firstName: string,
    lastName: string,
    userLocation: UserLocation | null,
    officePhone: string,
    policyIds: string[] | null,
    createdAt: string,
    updatedAt: string,
    version: number,
    jobTitle: string,
    lastSignIn: string,
    organization: ReferenceValueV2 | null,
    organizationTypes: ReferenceValueV2[] | null,
    isOwner: boolean | null,
    role: ReferenceValueV2 | null,
    keys: string[],
    applicationPolicyIds: ApplicationPolicyIdObj[],
    externalAuth0LoginUserId: string | null,
    externalIDPLoginUserId: string | null,
    isEmailVerified: boolean,
    isAccountSetupDone: boolean,
    devices: IUserDevices[],
    allowedLoginTokens: mongoose.Types.ObjectId[] | string[],
    emailVerificationToken: string | null,
    emailVerificationTokenExpiry: string | number | Date | null,
    forgotPasswordToken: string | null,
    forgotPasswordTokenExpiry: string | number | Date | null,
    passwordSetupToken: string | null,
    passwordSetupTokenExpiry: string | number | Date | null,
    actionToken: string | null,
    actionTokenExpiry: string | number | Date | null,
    forgotPasswordRequests: string[] | number[],
    emailVerificationRequests: string[] | number[],
    failedLoginAttempts: string[] | number[],
    lockType: LockTypesEnum | null,
    isAccountLocked: boolean,
    accountLockedAt: string | number | null;
}

// An interface that describes the properties that a User Model has
interface UserV2Model extends mongoose.Model<UserV2Doc> {
    build(attrs: UserV2Attrs): UserV2Doc,
    findByEvent(event: UserEventAttr): Promise<UserV2Doc>,
    insertUsersIfNotExist(users: UserAzureDetails[], organization: OrganizationV2Doc): Promise<UserV2Doc[]>
    insertUsersIfNotExistV2(users: UserAzureDetails[], organization: OrganizationV2Doc, session: ClientSession | null): Promise<UserV2Doc[]>,
    insertUserIfNotExistV3(user: MemberDetail, organization: OrganizationV2Doc, session: ClientSession | null): Promise<UserV2Doc>,
    getUserByAzureId({ azureId }: { azureId: string }): Promise<UserV2Doc | null>;
    getUserByEmail({ email }: { email: string }): Promise<UserV2Doc | null>;
    getUserById({ id }: { id: string }): Promise<UserV2Doc | null>;
    getUsersByPolicyId({ policyId }: { policyId: string }): Promise<UserV2Doc[] | null>
}

const applicationPolicySchema = new mongoose.Schema({
    applicationType: {
        type: String,
        enum: ["Incident", "Resilience"],
        required: true
    },
    applicationId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "applicationType"
    },
    policyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Policy"
    }
}, { _id: false });

const locationSchema = new mongoose.Schema({
    addressline1: {
        type: String,
        default: null
    },
    addressline2: {
        type: String,
        default: null
    },
    country: {
        type: String,
        default: null
    },
    countryShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        default: null
    },
    stateShortName: {
        type: String,
        default: null
    },
    city: {
        type: String,
        default: null
    },
    cityShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const userDevicesSchema = new mongoose.Schema(
    {
        id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
        },
        device: {
            type: String,
            required: true
        },
        passwordLogin: {
            type: Boolean,
            required: true
        },
        loginDateTime: {
            type: Date,
            default: Date.now
        },
        expirationDateTime: {
            type: Date,
            required: true
        }
    }, { _id: false });

const userV2Schema = new mongoose.Schema(
    {
        azureId: {
            type: String,
            required: true,
            // unique: true
        },
        email: {
            type: String,
            required: true,
            unique: true
        },
        name: {
            type: String,
            default: null
        },
        displayName: {
            type: String,
            required: true
        },
        isEnabled: {
            type: Boolean,
            default: true,
        },
        firstName: {
            type: String,
            default: null
        },
        lastName: {
            type: String,
            default: null
        },
        userLocation: {
            type: locationSchema,
            default: null
        },
        officePhone: {
            type: String,
            default: null
        },
        jobTitle: {
            type: String,
            default: null
        },
        policyIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Policy",
            default: null
        },
        applicationPolicyIds: {
            type: [applicationPolicySchema],
            default: []
        },
        organization: {
            type: referenceValueMongooseSchemaV2("OrganizationsV2"),
            default: null
        },
        organizationTypes: {
            type: [referenceValueMongooseSchemaV2("OrganizationType")],
            default: null
        },
        isOwner: {
            type: Boolean,
            default: null
        },
        role: {
            type: referenceValueMongooseSchemaV2(),
            default: null
        },
        keys: {
            type: [String],
            default: []
        },
        lastSignIn: {
            type: Date,
            default: null
        },
        externalAuth0LoginUserId: {
            type: String,
            default: null
        },
        externalIDPLoginUserId: {
            type: String,
            default: null
        },
        devices: {
            type: [userDevicesSchema],
            default: []
        },
        allowedLoginTokens: {
            type: [mongoose.Schema.Types.ObjectId],
            default: []
        },
        isEmailVerified: {
            type: Boolean,
            default: false
        },
        isAccountSetupDone: {
            type: Boolean,
            default: false
        },
        emailVerificationToken: {
            type: String,
            default: null
        },
        emailVerificationTokenExpiry: {
            type: Date,
            default: null
        },
        forgotPasswordToken: {
            type: String,
            default: null
        },
        forgotPasswordTokenExpiry: {
            type: Date,
            default: null
        },
        passwordSetupToken: {
            type: String,
            default: null
        },
        passwordSetupTokenExpiry: {
            type: Date,
            default: null
        },
        actionToken: {
            type: String,
            default: null
        },
        actionTokenExpiry: {
            type: Date,
            default: null
        },
        forgotPasswordRequests: {
            type: [Date],
            default: []
        },
        emailVerificationRequests: {
            type: [Date],
            default: []
        },
        failedLoginAttempts: {
            type: [Date],
            default: []
        },
        isAccountLocked: {
            type: Boolean,
            default: false
        },
        accountLockedAt: {
            type: Date,
            default: null
        },
        lockType: {
            type: String,
            default: null
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedAt: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

userV2Schema.set("versionKey", "version");
userV2Schema.plugin(updateIfCurrentPlugin);

userV2Schema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

// userV2Schema.post("save", function (doc: UserV2Doc, next) {
//     // eslint-disable-next-line no-invalid-this
//     userUpdatedPublisherV2Wrapper(doc);
//     next();
// });
//

userV2Schema.statics.build = (attrs: UserV2Attrs) => {
    return new UserV2(attrs);
};

userV2Schema.statics.findByEvent = async (event: UserEventAttr) => {
    return await UserV2.findOne({ _id: event.id, version: event.version - 1 });
};

userV2Schema.statics.getUserById = async ({ id }: { id: string }) => {
    return await UserV2.findById(id);
};

userV2Schema.statics.getUserByAzureId = async ({ azureId }: { azureId: string }) => {
    return await UserV2.findOne({ azureId: azureId });
};

userV2Schema.statics.getUserByEmail = async ({ email }: { email: string }) => {
    return await UserV2.findOne({ email }).lean().exec();
};

userV2Schema.statics.getUsersByPolicyId = async ({ policyId }: { policyId: string }) => {
    return await UserV2.find({ policyIds: policyId }).lean().exec();
};

userV2Schema.statics.insertUsersIfNotExist = async (users: UserAzureDetails[], organization: OrganizationV2Doc) => {
    const newUsers: UserV2Attrs[] = [];
    const changedUsers: UserV2Doc[] = [];

    const userDetails = await UserV2.find({ azureId: { $in: users.map(user => user.id) } });

    for (const user of users) {
        // Check if user is already present or not
        const existingUser = userDetails.find(item => item.azureId === user.id);

        // If not then insert in newUsers aray
        if (!existingUser) {
            const searchFields = getSearchFields(user.givenName as string, user.surname as string, user.displayName as string, user.mail);
            const searchKeys = generateSearchKeys(searchFields);

            newUsers.push({
                azureId: user.id,
                email: user.mail,
                displayName: user.displayName,
                firstName: user.givenName,
                lastName: user.surname,
                name: getUserName({
                    firstName: user.givenName ?? null,
                    lastName: user.surname ?? null,
                    displayName: user.displayName ?? ""
                }),
                userLocation: {
                    addressline1: user.streetAddress,
                    country: user.country,
                    state: user.state,
                    city: user.city,
                    zip: user.postalCode
                },
                officePhone: user.mobilePhone,
                isEnabled: user.accountEnabled,
                jobTitle: user.jobTitle,
                organization: {
                    id: String(organization._id),
                    value: organization.name
                },
                organizationTypes: organization.organizationTypes as ReferenceValueV2[],
                isOwner: organization.owner.includes(user.id),
                keys: searchKeys as string[]
            });
        }
        else {
            existingUser.organization = {
                id: String(organization._id),
                value: organization.name
            };
            existingUser.organizationTypes = organization.organizationTypes as ReferenceValueV2[];
            existingUser.isOwner = organization.owner.includes(user.id);
            // eslint-disable-next-line no-await-in-loop
            await existingUser.save();
            // eslint-disable-next-line no-await-in-loop
            await userUpdatedPublisherV2Wrapper(existingUser);

            changedUsers.push(existingUser);
        }
    }

    // Insert new users in DB
    if (newUsers.length) {
        const users = await UserV2.insertMany(newUsers, { ordered: false }) as unknown as UserV2Doc[];
        await Promise.all(users.map(async (user: UserV2Doc) => {
            changedUsers.push(user);

            const data = {
                id: user.id,
                version: user.version,
                displayName: user.displayName,
                email: user.email,
                isEnabled: user.isEnabled,
                firstName: user.firstName,
                lastName: user.lastName,
                name: user.name,
                organizationId: user.organization?.id ?? null,
                keys: user.keys,
                jobTitle: user.jobTitle,
                officePhone: user.officePhone,
                role: user.role?.value ?? null,
                allowedLoginTokens: (user.allowedLoginTokens as string[]) ?? []
            };

            await new UserCreatedPublisher(natsWrapper.client).publish(data);
        }));
    }
    return changedUsers;
};

userV2Schema.statics.insertUsersIfNotExistV2 = async (users: UserAzureDetails[], organization: OrganizationV2Doc, session: ClientSession | null) => {
    const newUsers: UserV2Attrs[] = [];

    const changedUsers: UserV2Doc[] = [];

    const userDetails = await UserV2.find({ azureId: { $in: users.map(user => user.id) } }).session(session);

    for (const user of users) {
        // Check if user is already present or not
        const existingUser = userDetails.find(item => item.azureId === user.id);

        // If not then insert in newUsers aray
        if (!existingUser) {
            const searchFields = getSearchFields(user.givenName as string, user.surname as string, user.displayName as string, user.mail);
            const searchKeys = generateSearchKeys(searchFields);

            newUsers.push({
                azureId: user.id,
                email: user.mail,
                displayName: user.displayName,
                firstName: user.givenName,
                lastName: user.surname,
                name: getUserName({
                    firstName: user.givenName ?? null,
                    lastName: user.surname ?? null,
                    displayName: user.displayName ?? ""
                }),
                userLocation: {
                    addressline1: user.streetAddress,
                    country: user.country,
                    state: user.state,
                    city: user.city,
                    zip: user.postalCode
                },
                officePhone: user.mobilePhone,
                isEnabled: user.accountEnabled,
                jobTitle: user.jobTitle,
                organization: {
                    id: String(organization._id),
                    value: organization.name
                },
                organizationTypes: organization.organizationTypes as ReferenceValueV2[],
                isOwner: organization.owner.includes(user.id),
                keys: searchKeys as string[]
            });
        }
    }

    // Insert new users in DB
    if (newUsers.length) {
        const users = await UserV2.insertMany(newUsers, { ordered: false, session }) as unknown as UserV2Doc[];
        await Promise.all(users.map(async (user: UserV2Doc) => {
            changedUsers.push(user);

            // Sync in User V1
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.INSERT,
                session
            });
        }));
    }
    return changedUsers;
};

userV2Schema.statics.insertUserIfNotExistV3 = async (user: MemberDetail, organization: OrganizationV2Doc, session: ClientSession | null) => {
    let newUser: UserV2Attrs | null = null;

    let changedUser: UserV2Doc | null = null;

    const userDetail = await UserV2.findOne({ email: user.email }).session(session);

    // Check if user is already present or not
    const existingUser = userDetail ? true : false;

    // If not then insert in newUsers aray
    if (!existingUser) {
        const searchFields = getSearchFields(user.firstName as string, user.lastName as string, user.displayName as string, user.email);
        const searchKeys = generateSearchKeys(searchFields);

        newUser = {
            // azureId: generateRandomString(12),
            azureId: " ",
            email: user.email,
            displayName: user.displayName,
            firstName: user.firstName,
            lastName: user.lastName,
            name: getUserName({
                firstName: user.firstName ?? null,
                lastName: user.lastName ?? null,
                displayName: user.displayName ?? ""
            }),
            userLocation: user.userLocation,
            officePhone: user.officePhone,
            isEnabled: user.accountEnabled,
            jobTitle: user.jobTitle,
            organization: {
                id: String(organization._id),
                value: organization.name
            },
            role: user.role ?? null,
            organizationTypes: organization.organizationTypes as ReferenceValueV2[],
            isOwner: null,
            keys: searchKeys as string[],
            isEmailVerified: user.isEmailVerified ?? false,
            externalAuth0LoginUserId: user.externalAuth0LoginUserId ?? null,
            emailVerificationToken: user.emailVerificationToken ?? null,
            emailVerificationTokenExpiry: user.emailVerificationTokenExpiry ?? null
        };
    }

    // Insert new users in DB
    if (newUser) {
        const user = await UserV2.build(newUser).save({ session: session });

        changedUser = user ? user : null;

        // Sync in User V1
        await userV2ToV1Sync({
            user: user.toObject(),
            operationType: OperationTypesEnums.INSERT,
            session
        });
    }
    return changedUser ? changedUser : null;
};

const UserV2 = mongoose.model<UserV2Doc, UserV2Model>("UsersV2", userV2Schema);

export { UserV2, UserV2Attrs, UserV2Doc };
