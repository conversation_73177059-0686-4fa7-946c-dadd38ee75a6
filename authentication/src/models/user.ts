import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { UserAzureDetails } from "../interfaces";
import { natsWrapper } from "../nats-wrapper";
import { UserCreatedPublisher } from "../events/publishers/user-created-publisher";
import { generateSearchKeys } from "@moxfive-llc/common";
import { userUpdatedPublisherWrapper } from "../util/user-updated-publisher-wrapper";
import { getSearchFields, getUserName } from "../util";
import { ApplicationPolicyIdObj } from "@moxfive-llc/common/build/interfaces";
import { UserLocation } from "../interfaces/user-location";

// An interface that describes the properties to define a new User
interface UserAttrs {
  azureId: string,
  email: string,
  name?: string,
  displayName?: string,
  firstName?: string,
  lastName?: string,
  userLocation?: UserLocation | null,
  officePhone?: string,
  isEnabled?: boolean,
  policyIds?: string[] | null,
  jobTitle?: string,
  lastSignIn?: string,
  organizationId?: string,
  role?: string | null,
  allowedLoginTokens: string[],
  keys: string[]
}

interface UserEventAttr {
    id: string;
    version: number;
}

// An interface that describes the properties that User Document has
interface UserDoc extends mongoose.Document {
  id: string;
  azureId: string,
  email: string,
  name: string | null,
  displayName: string,
  isEnabled: boolean,
  firstName: string,
  lastName: string,
    userLocation?: UserLocation | null,
  officePhone: string,
  policyIds?: string[] | null,
  createdAt: string,
  updatedAt: string,
  isOwner?: boolean,
  version: number,
  jobTitle: string,
  lastSignIn: string,
  organizationId: string | null,
  role: string | null,
  keys: string[],
  allowedLoginTokens: string[]
  applicationPolicyIds: ApplicationPolicyIdObj[]
}

// An interface that describes the properties that a User Model has
interface UserModel extends mongoose.Model<UserDoc> {
    build(attrs: UserAttrs): UserDoc,
    findByEvent(event: UserEventAttr): Promise<UserDoc>,
    insertUsersIfNotExist(users: UserAzureDetails[], organizationId: string): Promise<UserDoc[]>
    getUserByAzureId({ azureId }: { azureId: string }): Promise<UserDoc | null>;
    getUserByEmail({ email }: { email: string }): Promise<UserDoc | null>;
    getUserById({ id }: { id: string }): Promise<UserDoc | null>;
    getUsersByPolicyId({ policyId }: { policyId: string }): Promise<UserDoc[] | null>
}

const applicationPolicySchema = new mongoose.Schema({
    applicationType: {
        type: String,
        enum: ["Incident", "Resilience"],
        required: true
    },
    applicationId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "applicationType"
    },
    policyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Policy"
    }
}, { _id: false });

const locationSchema = new mongoose.Schema({
    addressline1: {
        type: String,
        default: null
    },
    addressline2: {
        type: String,
        default: null
    },
    country: {
        type: String,
        default: null
    },
    countryShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        default: null
    },
    stateShortName: {
        type: String,
        default: null
    },
    city: {
        type: String,
        default: null
    },
    cityShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const userSchema = new mongoose.Schema(
    {
        azureId: {
            type: String,
            required: true,
            // unique: true
        },
        email: {
            type: String,
            required: true,
            unique: true
        },
        name: {
            type: String,
            default: null
        },
        displayName: {
            type: String,
            required: true
        },
        isEnabled: {
            type: Boolean,
            default: true,
        },
        firstName: {
            type: String,
            default: null
        },
        lastName: {
            type: String,
            default: null
        },
        userLocation: {
            type: locationSchema,
            default: null
        },
        officePhone: {
            type: String,
            default: null
        },
        jobTitle: {
            type: String,
            default: null
        },
        policyIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "Policy",
            default: null
        },
        applicationPolicyIds: {
            type: [applicationPolicySchema],
            default: []
        },
        organizationId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Organization",
            default: null
        },
        role: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "UserFlexibleFields",
            default: null
        },
        keys: {
            type: [String],
            default: []
        },
        allowedLoginTokens: {
            type: [mongoose.Schema.Types.ObjectId],
            default: []
        },
        lastSignIn: {
            type: Date,
            default: null
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        updatedAt: {
            type: Date,
            default: Date.now
        }
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

userSchema.set("versionKey", "version");
userSchema.plugin(updateIfCurrentPlugin);

userSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

userSchema.statics.build = (attrs: UserAttrs) => {
    return new User(attrs);
};

userSchema.statics.findByEvent = async (event: UserEventAttr) => {
    return await User.findOne({ _id: event.id, version: event.version - 1 });
};

userSchema.statics.getUserById = async ({ id }: { id: string }) => {
    return await User.findById(id);
};

userSchema.statics.getUserByAzureId = async ({ azureId }: { azureId: string }) => {
    return await User.findOne({ azureId: azureId });
};

userSchema.statics.getUserByEmail = async ({ email }: { email: string }) => {
    return await User.findOne({ email }).lean().exec();
};

userSchema.statics.getUsersByPolicyId = async ({ policyId }: { policyId: string }) => {
    return await User.find({ policyIds: policyId }).lean().exec();
};

userSchema.statics.insertUsersIfNotExist = async (users: UserAzureDetails[], organizationId: string) => {
    const newUsers: UserAttrs[] = [];
    const changedUsers: UserDoc[] = [];

    for (const user of users) {
        // Check if user is already present or not
        // eslint-disable-next-line no-await-in-loop
        const existingUser = await User.findOne({ azureId: user.id });

        // If not then insert in newUsers aray
        if (!existingUser) {
            const searchFields = getSearchFields(user.givenName as string, user.surname as string, user.displayName as string, user.mail);
            const searchKeys = generateSearchKeys(searchFields);

            newUsers.push({
                azureId: user.id,
                email: user.mail,
                displayName: user.displayName,
                firstName: user.givenName,
                lastName: user.surname,
                name: getUserName({
                    firstName: user.givenName ?? null,
                    lastName: user.surname ?? null,
                    displayName: user.displayName ?? ""
                }),
                userLocation: {
                    addressline1: user.streetAddress,
                    country: user.country,
                    state: user.state,
                    city: user.city,
                    zip: user.postalCode
                },
                officePhone: user.mobilePhone,
                isEnabled: user.accountEnabled,
                jobTitle: user.jobTitle,
                organizationId: organizationId,
                allowedLoginTokens: [],
                keys: searchKeys as string[]
            });
        }
        else {
            existingUser.organizationId = organizationId;
            // eslint-disable-next-line no-await-in-loop
            await existingUser.save();
            // eslint-disable-next-line no-await-in-loop
            await userUpdatedPublisherWrapper(existingUser);

            changedUsers.push(existingUser);
        }
    }

    // Insert new users in DB
    if (newUsers.length > 0) {
        const users = await User.insertMany(newUsers, { ordered: false }) as unknown as UserDoc[];
        await Promise.all(users.map(async (user: UserDoc) => {
            changedUsers.push(user);

            const data = {
                id: user.id,
                version: user.version,
                displayName: user.displayName,
                email: user.email,
                isEnabled: user.isEnabled,
                firstName: user.firstName,
                lastName: user.lastName,
                name: user.name,
                organizationId: user.organizationId,
                keys: user.keys,
                jobTitle: user.jobTitle,
                officePhone: user.officePhone,
                allowedLoginTokens: user.allowedLoginTokens,
                role: user.role
            };

            await new UserCreatedPublisher(natsWrapper.client).publish(data);
        }));
    }
    return changedUsers;
};

const User = mongoose.model<UserDoc, UserModel>("User", userSchema);
export { User, UserAttrs, UserDoc };
