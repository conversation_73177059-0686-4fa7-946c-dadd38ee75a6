import mongoose from "mongoose";
import { updateIfCurrentPlugin } from "mongoose-update-if-current";
import { OrganizationBillingAddress, OrganizationLocation } from "../interfaces";
import { OrganizationTypeDoc } from "./organization-type";

// An interface that describes the properties to define a new organization
interface OrganizationAttrs {
    azureId: string,

    // Company Information
    name: string,
    bucketName?: string | null,
    organizationTypeIds: string[],
    industry?: string[] | null,
    serviceLines?: string[] | null,
    website?: string,
    highLevelCompanyInformation?: string,
    descriptionOfEnvironment?: string,
    officeLocations?: OrganizationLocation[],
    numberOfEmployees?: number,
    numberOfITStaff?: number,
    itStaffLocation?: OrganizationLocation[],
    activePartner?: boolean,
    profile?: string | null,
    favicon?: string | null,
    imageAndLogoSame?: boolean,

    // Hotline details
    moxfiveHotline?: boolean,
    hotlineEmail?: string,
    hotlinePhoneNumber?: string,

    // Contact details
    msaSignatureDate?: string,
    billingContactName?: string,
    billingContactEmail?: string,
    billingContactPhone?: string,
    billingAddresses?: OrganizationBillingAddress[]

    // Partner details
    partnerType?: string[],
    onboardedDate?: string,
    coverageStates?: string[],
    offerings?: string[],
    partnerEula?: string,
    partnerTermsConditions?: string,
    inboundRequestInfo?: string,
    numberOfPMs?: number,
    numberOfLeads?: number,
    numberOfEngineers?: number,
    moxfivePMSponsor?: string,
    moxfiveTASponsor?: string,
    moxfiveSalesSponsor?: string,
    languages?: string[],
    shortDescription?: string | null;

    owner: string[]
}

// An interface that describes the properties that Organization Document has
interface OrganizationDoc extends mongoose.Document {
    azureId: string,

    // Company Information
    name: string,
    bucketName?: string | null,
    organizationTypeIds: string[] | OrganizationTypeDoc[],
    industry?: string[] | null,
    serviceLines?: string[] | null,
    website?: string,
    highLevelCompanyInformation?: string,
    descriptionOfEnvironment?: string,
    officeLocations?: OrganizationLocation[],
    numberOfEmployees?: number,
    numberOfITStaff?: number,
    itStaffLocation?: OrganizationLocation[],
    activePartner?: boolean,
    profile?: string | null,
    favicon?: string | null,
    imageAndLogoSame?: boolean,

    // Hotline details
    moxfiveHotline?: boolean,
    hotlineEmail?: string,
    hotlinePhoneNumber?: string,

    // Contact details
    msaSignatureDate?: string,
    billingContactName?: string,
    billingContactEmail?: string,
    billingContactPhone?: string,
    billingAddresses?: OrganizationBillingAddress[]

    // Partner details
    partnerType?: string[],
    onboardedDate?: string,
    coverageStates?: string[],
    offerings?: string[],
    partnerEula?: string,
    partnerTermsConditions?: string,
    inboundRequestInfo?: string,
    numberOfPMs?: number,
    numberOfLeads?: number,
    numberOfEngineers?: number,
    moxfivePMSponsor?: string,
    moxfiveTASponsor?: string,
    moxfiveSalesSponsor?: string,
    languages?: string[],
    shortDescription: string | null

    createdAt: string;
    updatedAt: string;
    isEnabled: boolean;
    version: number;
    owner: string[];
    member: string[];
}

// An interface that describes the properties that a Organization model has
interface OrganizationModel extends mongoose.Model<OrganizationDoc> {
    build(attrs: OrganizationAttrs): OrganizationDoc,
    findUserAlreadyInAnyOrganization(userIds: string[]): Promise<OrganizationDoc | null>,
    findUserAlreadyInOtherOrganization(userIds: string[]): Promise<OrganizationDoc[] | null>
}

const billingAddressSchema = new mongoose.Schema({
    addressline1: {
        type: String,
        required: true
    },
    addressline2: {
        type: String,
        default: null
    },
    city: {
        type: String,
        required: true
    },
    cityShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        required: true
    },
    stateShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true
    },
    countryShortName: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const locationSchema = new mongoose.Schema({
    addressline1: {
        type: String,
        required: true
    },
    addressline2: {
        type: String,
        default: null
    },
    city: {
        type: String,
        required: true
    },
    cityShortName: {
        type: String,
        default: null
    },
    state: {
        type: String,
        required: true
    },
    stateShortName: {
        type: String,
        default: null
    },
    zip: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true
    },
    countryShortName: {
        type: String,
        default: null
    },
    latitude: {
        type: Number,
        default: null
    },
    longitude: {
        type: Number,
        default: null
    }
}, { _id: false });

const organizationSchema = new mongoose.Schema(
    {
        azureId: {
            type: String,
            // unique: true,
            required: true
        },

        // Company information
        profile: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Files",
            default: null
        },
        favicon: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Files",
            default: null
        },
        imageAndLogoSame: {
            type: Boolean,
            default: false
        },
        name: {
            type: String,
            required: true
        },
        bucketName: {
            type: String,
            default: null
        },
        organizationTypeIds: {
            type: [mongoose.Schema.Types.ObjectId],
            ref: "OrganizationType",
            required: true
        },
        industry: {
            type: [mongoose.Schema.Types.ObjectId],
            default: undefined
        },
        serviceLines: {
            type: [mongoose.Schema.Types.ObjectId],
            default: null
        },
        website: String,
        highLevelCompanyInformation: String,
        descriptionOfEnvironment: String,
        officeLocations: {
            type: [locationSchema],
            default: undefined
        },
        numberOfEmployees: Number,
        numberOfITStaff: Number,
        itStaffLocation: {
            type: [locationSchema],
            default: undefined
        },
        activePartner: Boolean,

        // Hotline details
        moxfiveHotline: Boolean,
        hotlineEmail: String,
        hotlinePhoneNumber: String,

        // Contact details
        msaSignatureDate: Date,
        billingContactName: String,
        billingContactEmail: String,
        billingContactPhone: String,
        billingAddresses: {
            type: [billingAddressSchema],
            default: undefined
        },

        // Partner details
        partnerType: {
            type: [mongoose.Schema.Types.ObjectId],
            default: undefined
        },
        onboardedDate: Date,
        coverageStates: {
            type: [mongoose.Schema.Types.ObjectId],
            default: undefined
        },
        offerings: {
            type: [mongoose.Schema.Types.ObjectId],
            default: undefined
        },
        partnerEula: String,
        partnerTermsConditions: String,
        inboundRequestInfo: String,
        numberOfPMs: Number,
        numberOfLeads: Number,
        numberOfEngineers: Number,
        moxfivePMSponsor: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User"
        },
        moxfiveTASponsor: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User"
        },
        moxfiveSalesSponsor: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User"
        },
        languages: {
            type: [mongoose.Schema.Types.ObjectId],
            default: undefined
        },
        shortDescription: {
            type: String,
            default: null
        },
        createdAt: {
            type: Date,
            required: true,
            default: Date.now
        },
        updatedAt: {
            type: Date,
            required: true,
            default: Date.now
        },
        isEnabled: {
            type: Boolean,
            required: true,
            default: true
        },
        owner: [
            {
                type: String,
            }
        ],
        member: [
            {
                type: String,
            }
        ]
    },
    {
        toJSON: {
            transform(doc, ret) {
                ret.id = ret._id;
                delete ret._id;
            },
        },
    }
);

organizationSchema.set("versionKey", "version");
organizationSchema.plugin(updateIfCurrentPlugin);

organizationSchema.statics.build = (attrs: OrganizationAttrs) => {
    return new Organization(attrs);
};

organizationSchema.statics.findUserAlreadyInAnyOrganization = async (userIds: string[]) => {
    const existingUsers = await Organization.findOne({
        $or: [
            { owner: { $in: userIds } },
            { member: { $in: userIds } }
        ]
    }).lean().exec();

    return existingUsers;
};

organizationSchema.statics.findUserAlreadyInOtherOrganization = async (userIds: string[]) => {
    const existingUsers = await Organization.find({
        $or: [
            { owner: { $in: userIds } },
            { member: { $in: userIds } }
        ]
    }).lean().exec();

    return existingUsers;
};

organizationSchema.pre("save", function (done) {
    this.updatedAt = new Date();
    done();
});

const Organization = mongoose.model<OrganizationDoc, OrganizationModel>("Organization", organizationSchema);

export { Organization, OrganizationAttrs, OrganizationDoc };
