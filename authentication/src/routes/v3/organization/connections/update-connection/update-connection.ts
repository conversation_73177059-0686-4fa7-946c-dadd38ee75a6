import express, { NextFunction, Request, Response } from "express";
import {
    BodyInvalidBadRequestError,
    copyFileFromTempToDestination,
    currentUser,
    deleteFile,
    downloadFile,
    File,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InternalServerError,
    InvalidFileError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    TemporaryFile,
    validateRequest
} from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../../services/mongo-transaction";
import { OrganizationV2 } from "../../../../../models/v2/oragnizations-v2";
import { Connections } from "../../../../../models/connections";
import { isMOXFIVEUser, AUTHENTICATION_MAIN_CONTAINER, TEMPORARY_BUCKET, isValidMongoObjectId } from "../../../../../util";
import { Auth0Service } from "../../../../../services/auth0";
import { MetaDataConnectionFields } from "../../../../../models/meta-data-connection-fields";
import { MetadataConnectionType } from "../../../../../models/metadata-connection-type";
import fs from "fs";
import { validateFieldsMiddleware } from "../../../../../validations/connection.validation";
import { connectionPathParamValidation } from "../../../../../validations/connection-path-param.validation";
import { getDateUsingMonthsFromToday } from "../../../../../util/get-date-using-months-from-today";
import { connectionTypeAndOrganizationBodyValidation } from "../../../../../validations/v3/organizations/connectionType-organization-body.validation";
import { AtlasService } from "../../../../../services/atlas";
import { SearchIndex } from "../../../../../enums/search-index";
import { processUpdatedDataForAtlasSync } from "../../../../../util/v2/process-updated-data-for-atlas-sync";

const router = express.Router();

router.put("/v1/authentication/connections/:connectionId",
    responseHandler,
    currentUser,
    requireAuth,
    connectionPathParamValidation,
    connectionTypeAndOrganizationBodyValidation,
    validateFieldsMiddleware,
    validateRequest,
    // eslint-disable-next-line max-statements
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();
        try {
            // Check user has permission to add users of organization
            const hasPermission = await hasGlobalAction(req, "UpdateOrganizationConnection");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };
            req.action = "UpdateConnectionForOrganization";
            if (!req.currentUser) {
                throw new InternalServerError();
            }

            const loggedInUser = {
                id: req.currentUser.id,
                value: req.currentUser.userName ?? ""
            };

            const { connectionId } = req.params;

            // Step 1: Find organization and if it's not present then throw NotFoundError
            const connection = await Connections.findById(connectionId).session(mongoTransaction.session);
            if (!connection) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_NOT_FOUND, "Connection not found.");
            }

            const { organizationId, connectionTypeId, data } = req.body;
            if(connectionTypeId && !isValidMongoObjectId(connectionTypeId)) {
                throw new BodyInvalidBadRequestError([
                    { name: "connectionTypeId", value: connectionTypeId, message: `Connection Type Id must be valid` }
                ]);
            }

            // Step 2: Find organization and if it's not present then throw NotFoundError
            const organization = await OrganizationV2.findById(String(organizationId)).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            if (String(connection.organization.id) !== organizationId) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_NOT_FOUND, "Connection not found.");
            }

            // Step 3: Find connection type and if it's not present then throw NotFoundError
            const connectionType = await MetadataConnectionType.findById(String(connectionTypeId)).session(mongoTransaction.session);
            if (!connectionType) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_TYPE_NOT_FOUND, "Connection type not found.");
            }

            // Case: if User only of same org or moxfive user can create connection
            if ((req.currentUser && req.currentUser.organizationId !== String(connection.organization.id)) && !isMOXFIVEUser({ req })) {
                throw new InsufficientPrivilagesError();
            }

            // Step 3: Fetch connection type fields and if not present throw error
            const connectionTypeFields = await MetaDataConnectionFields.find({ "connectionType.id": String(connectionTypeId) }).session(mongoTransaction.session);
            if (!connectionTypeFields.length) {
                throw new InternalServerError();
            }

            // Step 4:Call auth0 service to add connection and process data as per options should be passed to in proper format
            const { enforceAuthentication, isActive, domains, clientSecretExpiry, configurationFile }: {
                enforceAuthentication: boolean,
                isActive: boolean,
                domains: string[] | null,
                clientSecretExpiry: string | null,
                configurationFile?: string | null
            } = data;

            let configurationFileDetail: { id: string, value: string } | null = null;
            let mainFileId: string | null = null;
            // This connection id will be used while creating the connection
            const options: Record<string, any> = {};

            if (configurationFile) {
                const tempFile = await TemporaryFile.findById(configurationFile).session(mongoTransaction.session);
                let fileExtension: string | null = null;
                // If temporary file is found means new file uploaded with new temp id
                if (tempFile) {
                    const splitedFileName = tempFile.originalFileName.split(".");
                    fileExtension = splitedFileName[splitedFileName.length - 1].toLowerCase();
                    const validationField = connectionTypeFields.find(field => field.name === "configurationFile");
                    if (validationField && !validationField.validations.some(validate => validate.value === fileExtension)) {
                        throw new InvalidFileError("Invalid file type is passed.");
                    }
                    const fileIds = await copyFileFromTempToDestination(
                        {
                            temporaryContainerName: TEMPORARY_BUCKET,
                            destinationContainerName: AUTHENTICATION_MAIN_CONTAINER,
                            tempFileIds: [configurationFile],
                            parentId: String(connection._id),
                            AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING,
                            entity: connectionType.name,
                            entityId: String(connection._id),
                        }
                    );
                    if (fileIds.length) {
                        mainFileId = fileIds[0];
                        // Preparing data with main file id and with original name which was saved in connection db
                        configurationFileDetail = {
                            id: mainFileId,
                            value: tempFile.originalFileName
                        };

                        // Check if configuration file is found remove the file as new file is added
                        // eslint-disable-next-line max-depth
                        if (connection.configurationFile) {
                            await deleteFile({
                                parent: AUTHENTICATION_MAIN_CONTAINER,
                                parentId: String(connection._id),
                                entity: connection.connectionType.value,
                                entityId: String(connection._id),
                                fileId: String(connection.configurationFile.id),
                                AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                            });
                        }
                    }
                }

                // If not found temp file then it should be present in main container as old file id is passed
                else {
                    const fileDetail = await File.findById(connection.configurationFile!.id).session(mongoTransaction.session);
                    if (!fileDetail) {
                        throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "Configuration file not found.");
                    }
                    const splitedFileName = fileDetail.originalFileName.split(".");
                    fileExtension = splitedFileName[splitedFileName.length - 1].toLowerCase();
                    mainFileId = connection.configurationFile!.id;
                    configurationFileDetail = {
                        id: mainFileId,
                        value: fileDetail.originalFileName
                    };
                }

                // Downloading file from main container to process its content
                const { filePath } = await downloadFile({
                    parent: AUTHENTICATION_MAIN_CONTAINER,
                    parentId: String(connection._id),
                    entity: connectionType.name,
                    entityId: String(connection._id),
                    fileId: mainFileId!,
                    AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                });

                // eslint-disable-next-line no-sync, security/detect-non-literal-fs-filename
                const content = fs.readFileSync(filePath, "utf8");
                // pem x509
                if (fileExtension === "pem") {
                    options["signingCert"] = Buffer.from(content).toString("base64");
                    options["subject"] = {
                        "commonName": configurationFileDetail!.value
                    };
                }
                // xml
                else if (fileExtension === "xml") {
                    options["fedMetadataXml"] = content;
                    options["fedMetadataName"] = configurationFileDetail!.value;
                    // eslint-disable-next-line no-sync, security/detect-non-literal-fs-filename
                    const stats = fs.statSync(filePath);
                    options["fedMetadataSize"] = stats.size;
                }
                // json
                else if (fileExtension === "json") {
                    const jsonContent = JSON.parse(content);
                    options["oidc_metadata"] = jsonContent;
                }
            }

            let isDomain = false;
            const metaData: Record<string, any> = {};
            // Static fields which should be avoided to insert in the connection in auth0
            const platformFields = ["enforceAuthentication", "isActive", "configurationFile", "domains", "clientSecretExpiry"];
            connectionTypeFields.forEach(fields => {
                if (data.hasOwnProperty(fields.name) && fields.key && !platformFields.includes(fields.name)) {
                    options[fields.key] = data[fields.name];
                }
                if (fields.isDomain) {
                    isDomain = fields.isDomain;
                }

                if (data.hasOwnProperty(fields.name) && !fields.key && !platformFields.includes(fields.name)) {
                    metaData[fields.name] = JSON.stringify(data[fields.name]);

                    // Find its respective field and its value if it exists then add it to metaData
                    connectionTypeFields.forEach(field => {
                        if (field.conditions && field.conditions.length) {
                            field.conditions.forEach(condition => {
                                if (data[fields.name] === condition.value && !platformFields.includes(field.name)) {
                                    if (data.hasOwnProperty(fields.name)) {
                                        metaData[field.name] = JSON.stringify(data[field.name]);
                                    }
                                }
                            });
                        }
                    });
                }
            });

            const targetConnection: any[] = [];

            if (Object.keys(options).length) {
                // Remove the connection first
                const connectionName = `${String(organization._id)}-${Date.now()}`;
                const auth0Service = new Auth0Service(req);
                const tokenDetail = await auth0Service.fetchApplicationAccessToken();

                // Add default data to options
                for (const [key, value] of Object.entries(connectionType.defaultData ?? {})) {
                    if (!options.hasOwnProperty(key)) {
                        options[String(key)] = value;
                    }
                }

                const detail = await auth0Service.addConnection({
                    connectionName,
                    strategy: connectionType.strategy,
                    options,
                    metaData,
                    accessToken: tokenDetail.accessToken
                });

                let connectionIdFromAuth0 = "";
                let domainUrl: string | null = null;

                if (detail) {
                    ({ connectionIdFromAuth0, domainUrl } = detail);
                    if (domainUrl) {
                        // Add "https://" prefix if not present
                        if (!domainUrl.startsWith("https://")) {
                            domainUrl = `https://${domainUrl}`;
                        }

                        // Removing suffix for okta discovery url only
                        const suffix = "/.well-known/openid-configuration";
                        if (domainUrl.endsWith(suffix)) {
                            domainUrl = domainUrl.slice(0, -suffix.length);
                        }
                    }
                }

                if (connectionIdFromAuth0 && connectionIdFromAuth0.length) {
                    await auth0Service.removeOrganizationConnection({
                        accessToken: tokenDetail.accessToken,
                        connectionId: connection.externalConnectionId
                    });
                    connection.externalConnectionId = connectionIdFromAuth0;
                }

                // If user have passed expire months then find exact date from today and store it in db
                const clientSecretExpiryDate = clientSecretExpiry ? getDateUsingMonthsFromToday(Number(clientSecretExpiry)) : null;
                connection.connectionName = connectionName;
                connection.enforceAuthentication = enforceAuthentication ?? connection.enforceAuthentication;
                connection.configurationFile = (connection.configurationFile && (configurationFile === String(connection.configurationFile.id))) ?
                    connection.configurationFile : configurationFileDetail ?? null;
                connection.domains = domains && domains.length ? domains : null;
                connection.isActive = isActive;
                connection.clientSecretExpiration = clientSecretExpiry ? Number(clientSecretExpiry) : null;
                connection.clientSecretExpirationDate = clientSecretExpiryDate ?? null;
                connection.idpDomain = isDomain && domainUrl ? domainUrl : null;
                connection.updatedBy = loggedInUser;

                await connection.save({ session: mongoTransaction.session });

                targetConnection.push({
                    type: TargetType.CONNECTION,
                    details: {
                        id: String(connection._id),
                        name: connectionType.name
                    }
                });
            }

            // Step 6: Commit transaction
            await mongoTransaction.commitTransaction();
            const connectionDetailCreated: any = {
                organization: {
                    id: organization._id,
                    value: organization.name
                },
                connectionType: {
                    id: connectionType._id,
                    value: connectionType.name
                },
                enforceAuthentication: enforceAuthentication ?? null,
                isActive: isActive
            };

            // Check whether the details sync with the atlas search index
            await AtlasService.waitTillDataSyncToSearchIndex({
                collection: Connections,
                searchIndex: SearchIndex.CONNECTIONS_DEFAULT,
                docId: String(connection._id),
                updatedDoc: processUpdatedDataForAtlasSync({ data: connectionDetailCreated }),
                explicitTimeOut: 5000
            });

            // Step 7: Send Response
            res.sendResponse({
                meta: {
                    message: "Connection updated successfully."
                },
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    },
                    ...targetConnection
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateOrganizationConnection");
            console.error(error);
            next(error);
        }

    });

export { router as updateConnectionRouter };
