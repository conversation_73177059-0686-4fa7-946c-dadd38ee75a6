import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest,
    InsufficientPrivilagesError,
    InternalServerError,
    hasGlobalAction
} from "@moxfive-llc/common";

import { OrganizationV2 } from "../../../../../models/v2/oragnizations-v2";
import { Connections } from "../../../../../models/connections";
import { Auth0Service } from "../../../../../services/auth0";
import { MetaDataConnectionFields } from "../../../../../models/meta-data-connection-fields";
import { connectionPathParamValidation } from "../../../../../validations/connection-path-param.validation";
import { isMOXFIVEUser } from "../../../../../util";
import { getReaminingDaysUsingDate } from "../../../../../util/get-remaining-days-using-date";
import { MetadataConnectionType } from "../../../../../models/metadata-connection-type";

const router = express.Router();

router.get("/v1/authentication/connections/:connectionId",
    responseHandler,
    currentUser,
    requireAuth,
    connectionPathParamValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Step 1: Check user has permission to get connection details for specific organization
            const hasPermission = await hasGlobalAction(req, "GetOrganizationConnectionDetails");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };
            req.action = "GetConnectionDetail";
            const { connectionId } = req.params;

            // Step 2: Check for connection, if not exist then throw an error
            const connection = await Connections.findById(connectionId).lean().exec();
            if (!connection) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_NOT_FOUND, "Connection not found.");
            }

            // Step 3: Check for the organization, if it doesn't exist throw an error
            const organization = await OrganizationV2.findOne({ _id: String(connection.organization.id) }, { azureId: 0, version: 0 }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step: Fetch connection type for getting the logo
            const connectionType = await MetadataConnectionType.findById(connection.connectionType.id).lean().exec();
            if (!connectionType) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_TYPE_NOT_FOUND, "Connection type not found.");
            }

            // Case: if User only of same org or moxfive user can fetch connection
            if ((req.currentUser && req.currentUser.organizationId !== String(connection.organization.id)) && !isMOXFIVEUser({ req })) {
                throw new InsufficientPrivilagesError();
            }

            // Step 4: Fetch connection details which will be used to show in data field of response
            const auth0Service = new Auth0Service(req);
            const tokenDetail = await auth0Service.fetchApplicationAccessToken();
            const data = await auth0Service.getOrganizationConnectionDetails({
                accessToken: tokenDetail.accessToken,
                connectionId: connection.externalConnectionId
            });

            // Fetching fields data with its name in our platform
            const connectionFields = await MetaDataConnectionFields.find({ "connectionType.id": String(connection.connectionType.id), neededInPlatform: true });
            if (!connectionFields.length) {
                throw new InternalServerError();
            }
            const fieldsDetailNeededInPlatform: Record<string, any> = {};

            connectionFields.forEach(field => {
                if (data.options.hasOwnProperty(field.key)) {
                    fieldsDetailNeededInPlatform[field.name] = data.options[field.key];
                }
                else if (field.defaultValue || field.defaultValue === false) {
                    fieldsDetailNeededInPlatform[field.name] = field.defaultValue;
                }
                else if (data.metadata.hasOwnProperty(field.name)) {
                    fieldsDetailNeededInPlatform[field.name] = JSON.parse(data.metadata[field.name]);
                }
            });

            // Process how many month user picked and how many days left
            let clientSecretExpirationInDays: string | null = null;
            if (connection.clientSecretExpiration && connection.clientSecretExpirationDate) {
                const days = getReaminingDaysUsingDate(connection.clientSecretExpirationDate);

                clientSecretExpirationInDays = connection.clientSecretExpiration === 1 ? `${connection.clientSecretExpiration} month - ${days} day left` :
                    `${connection.clientSecretExpiration} months - ${days} days left`;
            }

            // Step 5: Process the response from connection
            const response: any = {
                id: String(connection._id),
                organization: {
                    id: String(connection.organization.id),
                    value: connection.organization.value
                },
                connectionType: {
                    id: String(connection.connectionType.id),
                    value: connection.connectionType.value,
                    logo: connectionType.logo ?? ""
                },
                ...fieldsDetailNeededInPlatform,
                domains: connection.domains,
                configurationFile: connection.configurationFile,
                enforceAuthentication: connection.enforceAuthentication,
                status: connection.isActive,
                clientSecretExpiry: connection.clientSecretExpiration ? String(connection.clientSecretExpiration) : null,
                clientSecretExpirationDate: clientSecretExpirationInDays,
                isActive: connection.isActive,
                createdBy: connection.createdBy.value ?? "",
                updatedBy: connection.updatedBy.value ?? "",
                createdAt: connection.createdAt,
                updatedAt: connection.updatedAt
            };

            if (response["configurationFile"] === null) {
                delete response["configurationFile"];
            }

            // Step 5: Send Response
            res.sendResponse(response, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationConnectionDetails");
            console.error(error);
            next(error);
        }
    });

export { router as getConnectionDetailsRouter };
