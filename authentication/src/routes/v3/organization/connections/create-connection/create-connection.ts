import express, { NextFunction, Request, Response } from "express";
import {
    BasicResourceValueUnacceptableConflictError,
    BodyInvalidBadRequestError,
    ConflictErrorCodes,
    copyFileFromTempToDestination,
    currentUser,
    downloadFile,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InternalServerError,
    InvalidFileError,
    NotFoundCode,
    requireAuth,
    ResourceAlreadyExistBadRequestError,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    TemporaryFile,
    validateRequest
} from "@moxfive-llc/common";
import { MongoTransaction } from "../../../../../services/mongo-transaction";
import { OrganizationV2 } from "../../../../../models/v2/oragnizations-v2";
import { Connections, ConnectionsDoc } from "../../../../../models/connections";
import { isMOXFIVEUser, AUTHENTICATION_MAIN_CONTAINER, TEMPORARY_BUCKET, isValidMongoObjectId } from "../../../../../util";
import { Auth0Service } from "../../../../../services/auth0";
import { validateFieldsMiddleware } from "../../../../../validations/connection.validation";
import { MetaDataConnectionFields } from "../../../../../models/meta-data-connection-fields";
import { MetadataConnectionType } from "../../../../../models/metadata-connection-type";
import fs from "fs";
import mongoose from "mongoose";
import { connectionTypeAndOrganizationBodyValidation } from "../../../../../validations/v3/organizations/connectionType-organization-body.validation";
import { getDateUsingMonthsFromToday } from "../../../../../util/get-date-using-months-from-today";
import { AtlasService } from "../../../../../services/atlas";
import { SearchIndex } from "../../../../../enums/search-index";
import { processUpdatedDataForAtlasSync } from "../../../../../util/v2/process-updated-data-for-atlas-sync";

const router = express.Router();

router.post("/v1/authentication/connections",
    responseHandler,
    currentUser,
    requireAuth,
    connectionTypeAndOrganizationBodyValidation,
    validateFieldsMiddleware,
    validateRequest,
    // eslint-disable-next-line max-statements
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // Check user has permission to add users of organization
            const hasPermission = await hasGlobalAction(req, "CreateOragnizationConnection");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // For unit testing purpose
            // req.currentUser = {
            //     userName: "Vijay Singh",
            //     id: "654b6a59c579fdbb9fe0edad",
            //     organizationId: "6230167610800339e6807fc2",
            //     email: "<EMAIL>"
            // };
            // req.action = "CreateOragnizationConnection";

            if (!req.currentUser) {
                throw new InternalServerError();
            }

            const loggedInUser = {
                id: req.currentUser.id,
                value: req.currentUser.userName ?? ""
            };

            const { organizationId, connectionTypeId, data } = req.body;

            if(connectionTypeId && !isValidMongoObjectId(connectionTypeId)) {
                throw new BodyInvalidBadRequestError([
                    { name: "connectionTypeId", value: connectionTypeId, message: `Connection Type Id must be valid` }
                ]);
            }

            // Case: User only of same org or moxfive user can create connection
            if (req.currentUser.organizationId !== organizationId && !isMOXFIVEUser({ req })) {
                throw new InsufficientPrivilagesError();
            }

            // Step 1: Find organization and if it's not present then throw NotFoundError
            const organization = await OrganizationV2.findById(String(organizationId)).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Find connection type and if it's not present then throw NotFoundError
            const connectionType = await MetadataConnectionType.findById(String(connectionTypeId)).session(mongoTransaction.session);
            if (!connectionType) {
                throw new ResourceNotFoundError(NotFoundCode.CONNECTION_TYPE_NOT_FOUND, "Connection type not found.");
            }

            // Case: If 5 connections already present in the platform then throw error that cannot create connection please contact mf
            const connections = await Connections.find().session(mongoTransaction.session);
            const oktaConnections: string[] = [];
            const otherConnections: string[] = [];
            connections.forEach(connection => {
                if (String(connection.connectionType.value) === "Okta Workforce") {
                    oktaConnections.push(String(connection.organization.id));
                }
                else {
                    otherConnections.push(String(connection.organization.id));
                }
            });
            if (otherConnections.length >= 5) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.CONNECTION_LIMIT_EXCEEDED,
                    "At a time only 5 enterprise connections can be created."
                );
            }
            // Checking if connection already exist then user is allowed to create only one connection for one organization
            const alreadyExistConnection = connections.find(connection => String(connection.organization.id) === organizationId);
            if (alreadyExistConnection && (oktaConnections.includes(String(alreadyExistConnection.organization.id)) ||
                otherConnections.includes(String(alreadyExistConnection.organization.id)))) {
                throw new ResourceAlreadyExistBadRequestError("Connection", alreadyExistConnection.connectionType.value, "Connection already exist.");
            }

            // Step 3: Fetch connection type fields and if not present throw error
            const connectionTypeFields = await MetaDataConnectionFields.find({ "connectionType.id": String(connectionTypeId) }).session(mongoTransaction.session);
            if (!connectionTypeFields.length) {
                throw new InternalServerError();
            }

            // Step 4:Call auth0 service to add connection and process data as per options should be passed to in proper format
            const { enforceAuthentication, isActive, domains, clientSecretExpiry, configurationFile }: {
                enforceAuthentication: boolean,
                isActive: boolean,
                domains: string[] | null,
                clientSecretExpiry: string | null,
                configurationFile?: string | null
            } = data;

            let configurationFileDetail: { id: string, value: string } | null = null;
            let mainFileId: string | null = null;
            // This connection id will be used while creating the connection
            const newConnectionId = String(new mongoose.Types.ObjectId());
            const options: Record<string, any> = {};
            if (configurationFile) {
                const tempFile = await TemporaryFile.findById(configurationFile).session(mongoTransaction.session);
                if (!tempFile) {
                    throw new ResourceNotFoundError(NotFoundCode.FILE_NOT_FOUND, "File not found.");
                }
                const splitedFileName = tempFile.originalFileName.split(".");
                const fileExtension = splitedFileName[splitedFileName.length - 1].toLowerCase();
                const validationField = connectionTypeFields.find(field => field.name === "configurationFile");
                if (validationField && !validationField.validations.some(validate => validate.value === fileExtension)) {
                    throw new InvalidFileError("Invalid file type provided.");
                }
                const fileIds = await copyFileFromTempToDestination(
                    {
                        temporaryContainerName: TEMPORARY_BUCKET,
                        destinationContainerName: AUTHENTICATION_MAIN_CONTAINER,
                        tempFileIds: [configurationFile],
                        parentId: newConnectionId,
                        AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING,
                        entity: connectionType.name,
                        entityId: newConnectionId,
                    }
                );

                if (fileIds.length) {
                    mainFileId = fileIds[0];
                    // Preparing data with main file id and with original name which was saved in connection db
                    configurationFileDetail = {
                        id: mainFileId,
                        value: tempFile.originalFileName
                    };
                    // Downloading file from main container to process its content
                    const { filePath } = await downloadFile({
                        parent: AUTHENTICATION_MAIN_CONTAINER,
                        parentId: newConnectionId,
                        entity: connectionType.name,
                        entityId: newConnectionId,
                        fileId: mainFileId,
                        AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                    });

                    // eslint-disable-next-line no-sync, security/detect-non-literal-fs-filename
                    const content = fs.readFileSync(filePath, "utf8");
                    // pem x509
                    if (fileExtension === "pem") {
                        options["signingCert"] = Buffer.from(content).toString("base64");
                        options["subject"] = {
                            "commonName": tempFile.originalFileName
                        };
                    }
                    // xml
                    else if (fileExtension === "xml") {
                        options["fedMetadataXml"] = content;
                        options["fedMetadataName"] = tempFile.originalFileName;
                        // eslint-disable-next-line no-sync, security/detect-non-literal-fs-filename
                        const stats = fs.statSync(filePath);
                        options["fedMetadataSize"] = stats.size;
                    }
                    // json
                    else if (fileExtension === "json") {
                        const jsonContent = JSON.parse(content);
                        options["oidc_metadata"] = jsonContent;
                    }
                }
            }

            let isDomain = false;
            const metaData: Record<string, any> = {};
            // Static fields which should be avoided to insert in the connection in auth0
            const platformFields = ["enforceAuthentication", "isActive", "configurationFile", "domains", "clientSecretExpiry"];
            connectionTypeFields.forEach(fields => {
                if (data.hasOwnProperty(fields.name) && fields.key && !platformFields.includes(fields.name)) {
                    options[fields.key] = data[fields.name];
                }
                if (fields.isDomain) {
                    isDomain = fields.isDomain;
                }

                if (data.hasOwnProperty(fields.name) && !fields.key && !platformFields.includes(fields.name)) {
                    metaData[fields.name] = JSON.stringify(data[fields.name]);

                    // Find its respective field and its value if it exists then add it to metaData
                    connectionTypeFields.forEach(field => {
                        if (field.conditions && field.conditions.length) {
                            field.conditions.forEach(condition => {
                                if (data[fields.name] === condition.value && !platformFields.includes(field.name)) {
                                    if (data.hasOwnProperty(fields.name)) {
                                        metaData[field.name] = JSON.stringify(data[field.name]);
                                    }
                                }
                            });
                        }
                    });
                }
            });

            let connectionDetails: ConnectionsDoc | null = null;
            const targetConnection: any[] = [];

            if (Object.keys(options).length) {
                const connectionName = `${String(organization._id)}-${Date.now()}`;

                // Add default data to options
                for (const [key, value] of Object.entries(connectionType.defaultData ?? {})) {
                    if (!options.hasOwnProperty(key)) {
                        options[String(key)] = value;
                    }
                }

                const auth0Service = new Auth0Service(req);
                const tokenDetail = await auth0Service.fetchApplicationAccessToken();
                const detail = await auth0Service.addConnection({
                    connectionName,
                    strategy: connectionType.strategy,
                    options,
                    metaData,
                    accessToken: tokenDetail.accessToken
                });

                let connectionIdFromAuth0 = "";
                let domainUrl: string | null = null;

                if (detail) {
                    ({ connectionIdFromAuth0, domainUrl } = detail);
                    if (domainUrl) {
                        // Add "https://" prefix if not present
                        if (!domainUrl.startsWith("https://")) {
                            domainUrl = `https://${domainUrl}`;
                        }

                        const suffix = "/.well-known/openid-configuration";
                        if (domainUrl.endsWith(suffix)) {
                            domainUrl = domainUrl.slice(0, -suffix.length);
                        }
                    }
                }

                // If user have passed expire months then find exact date from today and store it in db
                const clientSecretExpiryDate = clientSecretExpiry ? getDateUsingMonthsFromToday(Number(clientSecretExpiry)) : null;

                if (connectionIdFromAuth0) {
                    connectionDetails = await Connections.build({
                        _id: newConnectionId,
                        connectionName,
                        organization: {
                            id: String(organization._id),
                            value: organization.name
                        },
                        connectionType: {
                            id: String(connectionType._id),
                            value: connectionType.name
                        },
                        configurationFile: configurationFileDetail,
                        externalConnectionId: connectionIdFromAuth0,
                        domains: domains && domains.length ? domains : null,
                        enforceAuthentication: enforceAuthentication ?? null,
                        isActive: isActive,
                        clientSecretExpiration: clientSecretExpiry ? Number(clientSecretExpiry) : null,
                        clientSecretExpirationDate: clientSecretExpiryDate,
                        idpDomain: isDomain && domainUrl ? domainUrl : null,
                        createdBy: loggedInUser,
                        updatedBy: loggedInUser
                    }).save({ session: mongoTransaction.session });

                    // Todo: if required to remove those users from auth0 database to enable idp for them
                    // Case: Remove all the users login credibility from the auth0 database now users will be able to login using enterprise connection
                    // const users = await UserV2.find({ "organization.id": organizationId });
                    // if (users.length) {
                    //     for (const user of users) {
                    //         if (user.externalAuth0LoginUserId && (isBusinessEmail(user.email) || (!isBusinessEmail(user.email) && enforceAuthentication))) {
                    //             await Auth0Service.removeUserInAuth0({
                    //                 auth0UserId: user.externalAuth0LoginUserId,
                    //                 accessToken: tokenDetail.accessToken
                    //             });
                    //             user.externalAuth0LoginUserId = null;
                    //             await user.save();
                    //         }
                    //     }
                    // }

                    targetConnection.push({
                        type: TargetType.CONNECTION,
                        details: {
                            id: String(connectionDetails._id),
                            name: connectionType.name
                        }
                    });
                }
            }

            // Step 6: Commit transaction
            await mongoTransaction.commitTransaction();
            const connectionDetailCreated: any = {
                organization: {
                    id: organization._id,
                    value: organization.name
                },
                connectionType: {
                    id: connectionType._id,
                    value: connectionType.name
                },
                enforceAuthentication: enforceAuthentication ?? null,
                isActive: isActive
            };

            // Check whether the details sync with the atlas search index
            await AtlasService.waitTillDataSyncToSearchIndex({
                collection: Connections,
                searchIndex: SearchIndex.CONNECTIONS_DEFAULT,
                docId: String(newConnectionId),
                updatedDoc: processUpdatedDataForAtlasSync({ data: connectionDetailCreated }),
                explicitTimeOut: 5000
            });

            // Step 7: Send Response
            res.sendResponse({
                id: String(newConnectionId),
                meta: {
                    message: "Connection created successfully."
                },
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    },
                    ...targetConnection
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.CreateOragnizationConnection");
            console.error(error);
            next(error);
        }

    });

export { router as createConnectionRouter };
