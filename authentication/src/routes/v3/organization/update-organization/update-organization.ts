/* eslint-disable max-len */
import express, { NextFunction, Request, Response } from "express";
import {
    validateRequest,
    currentUser,
    requireAuth,
    hasGlobalAction,
    InsufficientPrivilagesError,
    ResourceNotFoundError,
    NotFoundCode,
    responseHandler,
    TargetType,
    MissingParametersError,
    ConflictErrorCodes,
    BasicResourceValueUnacceptableConflictError,
    ReferenceValueV2
} from "@moxfive-llc/common";
// import {
//     UpdateOrganizationNameAzureRequestData,
// } from "../../../interfaces";
// import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { OrganizationFields } from "../../../../services/organization-fields";
import {
    pickFromObject,
    intersectTwoObjects,
    isMOXFIVEUser,
    sanitizeURI,
    sanitizeEmail,
    unSanitizeURI, unSanitizeEmail
} from "../../../../util";
import { OrganizationType } from "../../../../models/organization-type";
import { MakeSingleSectionFieldsFlexibleFields } from "../../../../util/make-single-section-fields-flexible-fields";
import { authenticationFields } from "../../../../util/authentication-fields";
import { Incident } from "../../../../models/incident";
import { OrganizationV2, OrganizationV2Doc } from "../../../../models/v2/oragnizations-v2";
import { AuditLogV2 } from "../../../../services/v2/audit-log";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../../../util/v2/organization-updated-publisher-wrapper";
import { MongoTransaction } from "../../../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../../enums/operation-types.enum";
import { flexibleFieldValidationV2 } from "../../../../util/v2/flexible-field-validation-v2";
import { organizationValidation } from "../../../../validations/v3/organizations/organization.validation";
import { organizationPathParamValidation } from "../../../../validations/general/organizaiton-path-params-validation";

const router = express.Router();

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace Express {
        interface Request {
            organizationV2?: OrganizationV2Doc;
            organizationFields?: Set<string>;
            addOptionalFieldsIfNotPresent: Set<string>;
            fieldsToBeDeleted?: string[];
            isOrganizationTypesProvided?: boolean;
            organizationFlexibleFields?: {
                mandatory: Set<string>,
                validateFields: Set<string>
            }
        }
    }
}

const fetchOrganizationTypeMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // Check user has permission to update organization
        // const hasPermission = await hasGlobalAction(req, "UpdateOrganization");
        // if (!hasPermission) {
        //     throw new InsufficientPrivilagesError();
        // }

        console.info("fetchOrganizationTypeMiddleware start");

        const organization = await OrganizationV2.findById(req.params.organizationId);

        // If organization not found then throw not found error
        if (!organization) {
            throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
        }

        req.organizationV2 = organization;
        req.activePartner = organization.activePartner;

        // Check if organizationTypes is already present in the request body
        if (req.body.organizationTypes) {
            // Check if user is MOXFIVE user
            isMOXFIVEUser({
                req,
                throwError: true
            });

            // If given orgId is not same as MOXFIVE ID then true otherwise false
            req.isOrganizationTypesProvided = req.params.organizationId !== process.env.MOXFIVE_ID;
        }
        else {
            req.body.organizationTypes = organization.organizationTypes.map(record => String(record.id));
        }
        console.info("fetchOrganizationTypeMiddleware end");

        next();
    }
    catch (error) {
        next(error);
    }
};

const checkUpdatedOrganizationTypesMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    console.info("checkUpdatedOrganizationTypesMiddleware start");
    if (req.isOrganizationTypesProvided) {
        // Step 1: Preapre oldOrganizationTypes & provided organization types
        const oldOrganizationTypes: Set<string> = new Set();
        req.organizationV2?.organizationTypes.forEach(type => {
            oldOrganizationTypes.add(String(type.id));
        });

        const providedOrganizationTypes: Set<string> = new Set(req.body.organizationTypes);

        // Step 2: Filter out which org type is new added
        const newAddedOrganizationTypes: string[] = [];

        providedOrganizationTypes.forEach(type => {
            if (!oldOrganizationTypes.has(type)) {
                newAddedOrganizationTypes.push(type);
            }
        });

        // Step 3: Filter out which org type is deleted
        const deletedOrganizationTypes: string[] = [];

        oldOrganizationTypes.forEach(type => {
            if (!providedOrganizationTypes.has(type)) {
                deletedOrganizationTypes.push(type);
            }
        });

        // Step 4: If no new org type added or removed then make isOrganizationTypesProvided to false and do next
        if (newAddedOrganizationTypes.length === 0 && deletedOrganizationTypes.length === 0) {
            req.isOrganizationTypesProvided = false;
        }
        else {
            const mandatoryFields: string[] = [];
            const fieldsToBeDeleted: string[] = [];
            let fields: string[] = [];
            let uniqueFields: Set<string>;
            const addOptionalFieldsIfNotPresent: Set<string> = new Set();

            // Step 5: If any new organization type is added
            if (newAddedOrganizationTypes.length > 0) {
                // Fetch newAddedOrganizationTypes info
                const newAddedOrganizationTypesInfo = await OrganizationType.find({ _id: { $in: newAddedOrganizationTypes } });
                const newAddedOrganizationTypesNames = newAddedOrganizationTypesInfo.map(orgType => orgType.name);

                // Loop through newly added org types and find out fields and optional fields
                newAddedOrganizationTypesNames.forEach(organizationType => {
                    const orgTypeFields = OrganizationFields.fields[String(organizationType)];

                    if (orgTypeFields && orgTypeFields.length > 0) {
                        orgTypeFields.forEach(field => {
                            // If field is required then add it to fields, if not & it is not there in body as well as not in organization details then add it in optionalFields set
                            if (field.isRequired) {
                                fields.push(field.name);
                            }
                            else if ((!req.organizationV2 || req.organizationV2.get(field.name) === undefined) && !req.body.hasOwnProperty(field.name)) {
                                addOptionalFieldsIfNotPresent.add(field.name);
                            }
                        });
                    }
                });

                // Filter out Unique fields and loop through all fields if field is not present in existing details then add that field in mandatoryFields
                uniqueFields = new Set(fields);
                uniqueFields.forEach(field => {
                    if (!req.organizationV2 || req.organizationV2.get(field) === undefined) {
                        mandatoryFields.push(field);
                    }
                });

                // Loop through all mandatoryFields and check whether it is present in the request body or not
                const missingFields: string[] = [];
                mandatoryFields.forEach(field => {
                    if (!req.body.hasOwnProperty(field)) {
                        missingFields.push(field);
                    }
                });

                // If any mandatory fields are missing then throw error
                if (missingFields.length > 0) {
                    throw new MissingParametersError(missingFields);
                }
            }

            // Step 6: If any org type is removed
            if (deletedOrganizationTypes.length > 0) {
                // Filter out which fields need to delete
                fields = [];
                const deletedOrganizationTypesInfo = await OrganizationType.find({ _id: { $in: deletedOrganizationTypes } });
                const deletedOrganizationTypesName = deletedOrganizationTypesInfo.map(orgType => orgType.name);

                deletedOrganizationTypesName.forEach(organizationType => {
                    const orgTypeFields = OrganizationFields.fields[String(organizationType)];

                    if (orgTypeFields) {
                        const orgTypeFieldsName = orgTypeFields.map(field => field.name);
                        fields = [...fields, ...orgTypeFieldsName];
                    }
                });

                uniqueFields = new Set(fields);
                uniqueFields.forEach(field => {
                    if (!req.organizationFields?.has(field)) {
                        fieldsToBeDeleted.push(field);
                    }
                });
            }

            req.fieldsToBeDeleted = fieldsToBeDeleted;
            req.addOptionalFieldsIfNotPresent = addOptionalFieldsIfNotPresent;
        }
    }
    console.info("checkUpdatedOrganizationTypesMiddleware end");
    next();
};

const canAllTheFieldsUpdatedMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const fieldsMustUpdatedByMOXFIVEOnly = ["activePartner", "moxfiveHotline", "hotlineEmail", "hotlinePhoneNumber", "msaSignatureDate", "onboardedDate", "numberOfPMs", "numberOfLeads", "numberOfEngineers", "moxfivePMSponsor", "moxfiveTASponsor", "moxfiveSalesSponsor", "offerings", "partnerType"];
        const isMOXFIVE = isMOXFIVEUser({ req });

        // If user is not MOXFIVE then only check
        if (!isMOXFIVE) {
            fieldsMustUpdatedByMOXFIVEOnly.forEach(field => {
                if (req.body.hasOwnProperty(field)) {
                    throw new InsufficientPrivilagesError();
                }
            });
        }
        next();
    }
    catch (error) {
        next(error);
    }
};

const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.organizations);

router.put("/v3/authentication/organizations/:organizationId",
    responseHandler,
    // currentUser,
    // requireAuth,
    fetchOrganizationTypeMiddleware,
    OrganizationFields.checkOrganizationTypesMiddleware,
    checkUpdatedOrganizationTypesMiddleware,
    organizationPathParamValidation,
    organizationValidation(true),
    validateRequest,
    flexibleFieldValidationV2(flexibleFieldsNameKey),
    canAllTheFieldsUpdatedMiddleware,
    OrganizationFields.moxfiveSponsorsMiddlewareV2,

    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        await mongoTransaction.startTransaction();

        try {
            // Check if user has a ability to update the organization
            // const hasPermission = await hasGlobalAction(req, "UpdateOrganization");
            // if (!hasPermission) {
            //     throw new InsufficientPrivilagesError();
            // }

            const { organizationId } = req.params;
            const { organizationTypes: organizationTypeIds } = req.body;
            const orgTypesMap: Map<string, string> = req.organizationIdToNameMap;

            // Step 1: Find organization
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session) as OrganizationV2Doc;

            // Step 2: Fetch details of vendor based on organization type
            const vendorDetails = pickFromObject(organization, Array.from(req.organizationFields!), false);

            // Step 3: Find the difference between existing details and the request given
            if (req.addOptionalFieldsIfNotPresent) {
                req.addOptionalFieldsIfNotPresent.forEach(field => {
                    req.body[String(field)] = null;
                });
            }
            // unSanitized values
            const unSanitizedValues = {
                website: req.body.website,
                hotlineEmail: req.body.hotlineEmail,
                billingContactEmail: req.body.billingContactEmail,
            };

            if (req.body.website) {
                req.body.website = sanitizeURI(req.body.website);
            }
            if (req.body.hotlineEmail) {
                req.body.hotlineEmail = sanitizeEmail(req.body.hotlineEmail);
            }
            if (req.body.billingContactEmail) {
                req.body.billingContactEmail = sanitizeEmail(req.body.billingContactEmail);
            }

            const updatedData = intersectTwoObjects(vendorDetails, req.body);

            // If service lines has been changed then continue
            if (updatedData.hasOwnProperty("serviceLines") && vendorDetails.serviceLines && vendorDetails.serviceLines.length) {
                // Prepare which service lines got removed
                const newServiceLines = new Set((updatedData.serviceLines ?? []).map((serviceLine: ReferenceValueV2) => serviceLine.id));
                const removedServiceLines = vendorDetails.serviceLines.filter((serviceLine: ReferenceValueV2) => !newServiceLines.has(String(serviceLine.id)));

                // If there are any removed service lines then find whether in any incident it is attached or not, if it is attached then throw an error
                if (removedServiceLines.length) {
                    const incidentWithExistingServiceLines = await Incident.findOne({ clientId: organizationId, serviceLines: { $in: removedServiceLines.map((serviceLine: ReferenceValueV2) => serviceLine.id) } }).lean().exec();
                    if (incidentWithExistingServiceLines) {
                        throw new BasicResourceValueUnacceptableConflictError(
                            ConflictErrorCodes.INCIDENT_EXIST_FOR_THAT_INCIDENT_SUPPORT,
                            "The existing values of Service Lines cannot be removed from an organization for which Incidents are already exists. You must change the value in the existing Incidents first and then remove the value from Organization."
                        );
                    }
                }
            }

            // Step 4: Update isOrganizationTypesProvided & delete fields if needed
            if (req.isOrganizationTypesProvided) {
                organization.organizationTypes = req.body.organizationTypes;

                // If any fields needs to delete then delete it
                if (req.fieldsToBeDeleted && req.fieldsToBeDeleted.length > 0) {
                    req.fieldsToBeDeleted.forEach(field => {
                        organization.set(field, undefined);
                    });
                }
            }

            //Step 5: If no organization type is provided and changes and no other data is changed then send 204
            if (!req.isOrganizationTypesProvided && Object.keys(updatedData).length === 0) {
                // return res.status(204).send();
                return res.sendResponse({
                    meta: {
                        message: "Organization updated successfully"
                    }
                }, {});
            }

            // Step 6: If name or description has updated then update organization info in microsoft azure
            // let isAzureOrgInfoUpdated = false;
            // const updatedOrgAzureData: UpdateOrganizationNameAzureRequestData = {};

            // if (updatedData.name) {
            //     isAzureOrgInfoUpdated = true;
            //     updatedOrgAzureData.displayName = updatedData.name;
            // }

            // if (updatedData.description) {
            //     isAzureOrgInfoUpdated = true;
            //     updatedOrgAzureData.description = updatedData.description;
            // }

            // if (isAzureOrgInfoUpdated) {
            //     // Get access token
            //     const token = await microsoftGraphAPI.getAccessToken();

            //     // Update organization name
            //     await microsoftGraphAPI.updateOrganization({
            //         token,
            //         orgId: organization.azureId,
            //         data: updatedOrgAzureData
            //     });
            // }

            // Step 7: Update organization details
            Object.assign(organization, updatedData);
            organization.organizationTypes = organizationTypeIds.map((typeId: string) => ({
                id: typeId,
                value: orgTypesMap.get(String(typeId)) ?? ""
            }));

            await organization.save({ session: mongoTransaction.session });

            // Sync in Organization V1
            await organizationV2ToV1Sync({
                organization: organization.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Event
            await OrganizationUpdatedPublisherWrapperV2(organization);

            // Prepare data for audit log
            const data: any = { ...updatedData };
            const oldData: any = { ...vendorDetails };

            // Populate un-sanitized values
            if (data.website) {
                data.website = unSanitizedValues.website;

                if (oldData.website) {
                    oldData.website = unSanitizeURI(oldData.website);
                }
            }
            if (data.hotlineEmail) {
                data.hotlineEmail = unSanitizedValues.hotlineEmail;

                if (oldData.hotlineEmail) {
                    oldData.hotlineEmail = unSanitizeEmail(oldData.hotlineEmail);
                }
            }
            if (data.billingContactEmail) {
                data.billingContactEmail = unSanitizedValues.billingContactEmail;

                if (oldData.billingContactEmail) {
                    oldData.billingContactEmail = unSanitizeEmail(oldData.billingContactEmail);
                }
            }

            // if (req.isOrganizationTypesProvided) {
            //     const organizationTypes = await OrganizationType.find().select("name");

            //     const orgTypesMap: Map<string, string> = new Map();
            //     organizationTypes.forEach(type => {
            //         orgTypesMap.set(String(type._id), type.name);
            //     });

            //     data.organizationTypeIds = (organization.organizationTypeIds as string[]).map((type: string) => {
            //         return {
            //             id: String(type),
            //             name: orgTypesMap.get(String(type)) || ""
            //         };
            //     });

            //     oldData.organizationTypeIds = oldOrgTypes.map((type: string) => {
            //         return {
            //             id: String(type),
            //             name: orgTypesMap.get(String(type)) || ""
            //         };
            //     });
            // }

            // Populate MOXFIVE PM, TA and Sales sponsors
            // if (data.moxfivePMSponsor || data.moxfiveTASponsor || data.moxfiveSalesSponsor) {
            //     const userIds: string[] = [];
            //     if (data.moxfivePMSponsor) {
            //         userIds.push(data.moxfivePMSponsor);

            //         if (oldData.moxfivePMSponsor) {
            //             userIds.push(oldData.moxfivePMSponsor);
            //         }
            //     }
            //     if (data.moxfiveTASponsor) {
            //         userIds.push(data.moxfiveTASponsor);

            //         if (oldData.moxfiveTASponsor) {
            //             userIds.push(oldData.moxfiveTASponsor);
            //         }
            //     }
            //     if (data.moxfiveSalesSponsor) {
            //         userIds.push(data.moxfiveSalesSponsor);

            //         if (oldData.moxfiveSalesSponsor) {
            //             userIds.push(oldData.moxfiveSalesSponsor);
            //         }
            //     }

            //     const usersMap = await getUsersByIds(userIds);
            //     if (data.moxfivePMSponsor) {
            //         data.moxfivePMSponsor = {
            //             id: String(data.moxfivePMSponsor),
            //             name: usersMap.get(String(data.moxfivePMSponsor)) || ""
            //         };

            //         if (oldData.moxfivePMSponsor) {
            //             oldData.moxfivePMSponsor = {
            //                 id: String(oldData.moxfivePMSponsor),
            //                 name: usersMap.get(String(oldData.moxfivePMSponsor)) || ""
            //             };
            //         }
            //     }
            //     if (data.moxfiveTASponsor) {
            //         data.moxfiveTASponsor = {
            //             id: String(data.moxfiveTASponsor),
            //             name: usersMap.get(String(data.moxfiveTASponsor)) || ""
            //         };

            //         if (oldData.moxfiveTASponsor) {
            //             oldData.moxfiveTASponsor = {
            //                 id: String(oldData.moxfiveTASponsor),
            //                 name: usersMap.get(String(oldData.moxfiveTASponsor)) || ""
            //             };
            //         }
            //     }
            //     if (data.moxfiveSalesSponsor) {
            //         data.moxfiveSalesSponsor = {
            //             id: String(data.moxfiveSalesSponsor),
            //             name: usersMap.get(String(data.moxfiveSalesSponsor)) || ""
            //         };

            //         if (oldData.moxfiveSalesSponsor) {
            //             oldData.moxfiveSalesSponsor = {
            //                 id: String(oldData.moxfiveSalesSponsor),
            //                 name: usersMap.get(String(oldData.moxfiveSalesSponsor)) || ""
            //             };
            //         }
            //     }
            // }

            const modifiedProperties = AuditLogV2.prepareModifiedProperties({
                data,
                oldData,
                target: TargetType.ORGANIZATION
            });

            // Step 8: Send Response
            res.sendResponse({
                meta: {
                    message: "Organization updated successfully"
                }
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    }
                ],
                modifiedProperties
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateOrganizationV3");
            console.error(error);
            next(error);
        }
    });

export { router as updateOrganizationV3Router };
