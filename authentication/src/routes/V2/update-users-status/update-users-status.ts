import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    ExternalServerError,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InternalServerError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    SucceededPartially,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { UpdateUsersStatusValidation } from "./update-users-status.validation";
import { UserV2, UserV2Doc } from "../../../models/v2/users-v2";
import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { User } from "../../../models/user";

const router = express.Router();

router.put("/v2/authentication/users/status",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    UpdateUsersStatusValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            const { isEnabled, users: userIds }: { isEnabled: boolean, users: string[] } = req.body;

            // Step 1: Check user has permission to update users status
            const hasPermission = await hasGlobalAction(req, "UpdateUsersStatus");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            if(!req.currentUser) {
                throw new InternalServerError();
            }

            // If userIds includes logged in user id then throw an error as user can't change it's own status
            if (userIds.includes(req.currentUser.id)) {
                throw new InvalidActionError("You cannot change status of your own account.");
            }

            // Step 2: If any user is not found then throw NotFoundError
            const users = await UserV2.find({ _id: { $in: userIds } }).session(mongoTransaction.session);
            if (!users || (users && !users.length)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Prepare user details map
            const userDetailsMap = new Map();
            users.forEach(user => {
                userDetailsMap.set(String(user._id), {
                    id: String(user._id),
                    name: user.name,
                    email: user.email,
                    azureId: user.azureId
                });
            });

            const validUsers: UserV2Doc[] = [];
            const inValidUserIds: string[] = [];

            // Loop throuh all userIds
            userIds.forEach(userId => {
                // If userDetailsMap has that user and find record of that user from users and push it to validUsers otherwise add entry in inValidUserIds
                if (userDetailsMap.has(userId)) {
                    validUsers.push(users.find(user => user.id === userId)!);
                }
                else {
                    inValidUserIds.push(userId);
                }
            });

            // Step 3: Check which users needs to be updated
            const usersToChange = validUsers.filter(user => {
                return user.isEnabled !== isEnabled;
            });

            // Prepare azureIds of usersToChanges
            const userAzureIds = usersToChange.map(user => user.azureId);

            const updatedUsers: UserV2Doc[] = [];
            const failedUsers: any = [];

            // Step 4: If there are any users needs to be updated in azure, update in azure as well user doc
            if (userAzureIds.length) {
                // Fetch access token and update user status in azure
                const accessToken = await microsoftGraphAPI.getAccessToken();
                const updateStatusResponse = await microsoftGraphAPI.updateMultipleUsersStatus({ accessToken, users: userAzureIds, status: isEnabled });

                // Filter out which user's status updated and which didn't
                usersToChange.forEach(user => {
                    if (updateStatusResponse[user.azureId].status) {
                        updatedUsers.push(user);
                    }
                    else {
                        failedUsers.push(updateStatusResponse[user.azureId].body?.error);
                    }
                });

                // If no user is updated then throw internal server error
                if (failedUsers.length === userIds.length) {
                    throw new ExternalServerError(failedUsers);
                }

                //  Update users status
                await Promise.all(updatedUsers.map(async user => {
                    user.isEnabled = isEnabled;
                    await user.save({ session: mongoTransaction.session });
                    await User.findByIdAndUpdate(String(user._id), { isEnabled: Boolean(isEnabled), version: Number(user.version) }, { session: mongoTransaction.session });
                }));
            }

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Update publish user update
            await Promise.all(updatedUsers.map(async user => {
                await userUpdatedPublisherV2Wrapper(user);
            }));

            // Step 5: If partial users got updated then throw error with email ids
            const errors = [];
            if (inValidUserIds.length) {
                errors.push({
                    attributes: inValidUserIds,
                    message: "Users do not exist."
                });
            }

            if (failedUsers.length) {
                failedUsers.forEach((failure: any) => errors.push(failure));
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users status failed to update.");
            }

            // Step 6: Send Response
            res.sendResponse({
                meta: {
                    message: "Users status updated successfully"
                }
            }, updatedUsers.length ? {
                targets: updatedUsers.map(user => {
                    const userDetails = userDetailsMap.get(user.id);
                    return {
                        type: TargetType.USER,
                        details: userDetails || {}
                    };
                }),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "isEnabled",
                    oldValue: JSON.stringify(!isEnabled),
                    newValue: JSON.stringify(isEnabled)
                }]
            } : {});
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateUsersStatusV2");
            console.error(error);
            next(error);
        }
    });

export { router as updateUsersStatusV2Router };
