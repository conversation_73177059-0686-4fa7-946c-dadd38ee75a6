import {
    currentUser,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHand<PERSON>
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { UserDetailsResponse } from "../../../interfaces";
import { UserV2 } from "../../../models/v2/users-v2";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
import { ReferenceValueV2 } from "../../../interfaces/v2/reference-value-v2";
import { Connections } from "../../../models/connections";

const router = express.Router();

router.get(
    "/v2/authentication/users/me",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            req.action = "GetUserProfile";
            // Fetch details of logged in user, if user not found then throw an error
            const userId = req.currentUser?.id;

            const userDetails = await UserV2.findById(userId, {
                "email": 1,
                "displayName": 1,
                "isEnabled": 1,
                "firstName": 1,
                "lastName": 1,
                "userLocation": 1,
                "officePhone": 1,
                "jobTitle": 1,
                "organizationId": 1,
                "role": 1,
                "lastSignIn": 1,
                "createdAt": 1,
                "updatedAt": 1,
                "name": 1,
                "organization": 1,
                "isOwner": 1,
                "isEmailVerified": 1,
                "isAccountSetupDone": 1,
                "isAccountLocked": 1,
                "externalAuth0LoginUserId": 1,
            });
            if (!userDetails) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Fetch user's organization
            let userOrgDetails = await OrganizationV2.findById(userDetails.organization?.id).lean().exec();
            // If organization is not found
            if (!userOrgDetails) {
                // Fetch organization in which user is part of
                userOrgDetails = await OrganizationV2.findOne({ $or: [{ owner: userDetails.azureId }, { member: userDetails.azureId }] }).lean().exec();
                // If ay organization found then update user's organization, organizationTypes, isOwner and save it
                if(userOrgDetails) {
                    userDetails.organization = {
                        id: String(userOrgDetails._id),
                        value: userOrgDetails.name
                    };
                    userDetails.organizationTypes = userOrgDetails.organizationTypes as ReferenceValueV2[];
                    userDetails.isOwner =  userOrgDetails.owner.includes(userDetails.azureId);

                    await userDetails.save();
                    await userUpdatedPublisherV2Wrapper(userDetails);
                }
            }

            // If user's organization is not found then throw an error
            if(!userOrgDetails) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const connectionDetail = await Connections.findOne({ "organization.id": userDetails.organization?.id }, { connectionType: 1 }).lean().exec();
            const userDetailsResponse: UserDetailsResponse = {
                ...userDetails.toJSON(),
                connection: connectionDetail ? {
                    id: String(connectionDetail._id),
                    connectionType: connectionDetail.connectionType
                } : null,
                isOwner: userDetails.isOwner ?? undefined,
                organization: userOrgDetails ? {
                    id: String(userOrgDetails._id),
                    name: userOrgDetails.name,
                    organizationTypes: (userOrgDetails.organizationTypes as ReferenceValueV2[]).map(type => type.value)
                } : undefined,
                passwordAuthentication: Boolean(userDetails.externalAuth0LoginUserId)
            };

            userDetailsResponse["organization"] = {
                id: String(userOrgDetails._id),
                name: userOrgDetails.name,
                organizationTypes: (userOrgDetails.organizationTypes as ReferenceValueV2[]).map(type => type.value)
            };

            delete userDetailsResponse.externalAuth0LoginUserId;
            delete userDetailsResponse.azureId;

            res.sendResponse(userDetailsResponse, {});
        }
        catch (error) {
            console.error("Authentication.GetUserProfileV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as getUserProfileV2Router };
