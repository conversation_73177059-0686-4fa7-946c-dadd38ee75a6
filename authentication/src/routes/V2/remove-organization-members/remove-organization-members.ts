/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    ExternalServerError,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    SucceededPartially,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { removeOrganizationMembersValidation } from "./remove-organization-members.validation";
import { userDeletedPublisherWrapper } from "../../../util";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../../util/v2/organization-updated-publisher-wrapper";
import { UserV2, UserV2Doc } from "../../../models/v2/users-v2";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../enums/operation-types.enum";
import { userV2ToV1Sync } from "../../../util/user-v2-to-v1-sync";

const router = express.Router();

router.delete("/v2/authentication/organizations/:organizationId/members",
    responseHandler,
    currentUser,
    requireAuth,
    removeOrganizationMembersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Check user has permission to remove members from organization
            const hasPermission = await hasGlobalAction(req, "RemoveMembersOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { members }: { members: string[] } = req.body;
            const { organizationId } = req.params;

            if (members.includes(req.currentUser?.id!)) {
                throw new InvalidActionError("You cannot remove yourself from the organization.");
            }

            // Step 1: Find organization and if it's not present then throw NotFoundError, if not enabled then throw BadRequestError
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Fetch user details from user ids, if any id is not valid throw new error
            const usersDetails = await UserV2.find({
                _id: { $in: members }
            }).session(mongoTransaction.session);
            if (!usersDetails || !usersDetails.length) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Prepare user details map
            const userDetailsMap = new Map();
            const userIds: string[] = [];

            usersDetails.forEach(user => {
                userIds.push(String(user._id));

                userDetailsMap.set(String(user.azureId), {
                    id: String(user._id),
                    name: user.name,
                    email: user.email,
                    azureId: user.azureId
                });
            });

            const validUsers: UserV2Doc[] = [];
            const inValidUserIds: string[] = [];

            members.forEach(userId => {
                if (userIds.includes(userId)) {
                    validUsers.push(usersDetails.find(user => user.id === userId)!);
                }
                else {
                    inValidUserIds.push(userId);
                }
            });

            // Make azure Ids
            const userAzureIds = validUsers.map(user => {
                return user.azureId;
            });

            // Step 3: Check whether this members are there as memebers in that org
            const org = await OrganizationV2.findOne({
                _id: organizationId,
                member: { $all: userAzureIds }
            }).session(mongoTransaction.session).lean().exec();
            if (!org) {
                // throw new BadRequestError("All the members provided are not in organization");
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Step 4: Get the authorization token
            const token = await microsoftGraphAPI.getAccessToken();

            // Step 5 : Remove members from organization microsoft azure
            const removeUsersResponse = await microsoftGraphAPI.removeUsersFromOrganization({ token, orgId: organization.azureId, users: userAzureIds });

            // Step 6:  Filter out removed members azureIds and not removed members
            const removedUserAzureIds: string[] = [];
            const notRemovedUsers: any = [];

            validUsers.forEach(user => {
                if (removeUsersResponse[user.azureId].status) {
                    removedUserAzureIds.push(user.azureId);
                }
                else {
                    notRemovedUsers.push(removeUsersResponse[user.azureId].body?.error!);
                }
            });

            if (notRemovedUsers.length === members.length) {
                throw new ExternalServerError(notRemovedUsers);
            }

            // Step 7 : Remove members from organization
            organization.member = organization.member.filter(member => {
                return !removedUserAzureIds.includes(member);
            });

            await organization.save({ session: mongoTransaction.session });

            // Sync data in v1 organization
            await organizationV2ToV1Sync({
                organization: organization.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            const usersDeleteEventData: {id: string}[] = [];

            await Promise.all(removedUserAzureIds.map(async azureId => {
                const user = await UserV2.findOne({ azureId: azureId }).session(mongoTransaction.session);
                if (user) {
                    usersDeleteEventData.push({
                        id: user.id
                    });

                    await user.deleteOne({ session: mongoTransaction.session });

                    // Sync data in user v1
                    await userV2ToV1Sync({
                        user: user.toObject(),
                        operationType: OperationTypesEnums.DELETE,
                        session: mongoTransaction.session
                    });
                }
            }));

            // Commit transaction
            await mongoTransaction.commitTransaction();

            // Publish NATS Events
            await OrganizationUpdatedPublisherWrapperV2(organization);

            await Promise.all(usersDeleteEventData.map(async data => {
                await userDeletedPublisherWrapper(data);
            }));

            // Step 8: If some members are not removed then throw internal server error
            const errors = [];
            if (inValidUserIds.length) {
                errors.push({
                    attributes: inValidUserIds,
                    message: "Users do not exist."
                });
            }

            if (notRemovedUsers.length) {
                notRemovedUsers.forEach((err: any) => {
                    errors.push(err);
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more members are failed to remove from the specified organization.");
            }

            // Step 9: Send response
            res.sendResponse({
                meta: {
                    message: "Organization members removed successfully"
                }
            }, {
                targets: removedUserAzureIds.map(azureId => {
                    const user = userDetailsMap.get(azureId);

                    return {
                        type: TargetType.USER,
                        details: user || {}
                    };
                }),
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.RemoveOrganizationMembersV2");
            console.error(error);
            next(error);
        }
    });

export { router as removeOrganizationMembersV2Router };
