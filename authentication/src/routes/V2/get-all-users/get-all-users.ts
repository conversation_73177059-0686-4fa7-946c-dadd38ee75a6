import {
    createPagination,
    currentUser,
    FilterFields,
    getPage,
    hasGlobalAction,
    InsufficientPrivilages<PERSON><PERSON>r,
    <PERSON>AP<PERSON><PERSON><PERSON><PERSON>,
    PageResponseObj,
    PaginationEntity,
    requireAuth,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { getAllUsersValidation } from "./get-all-users.validation";
import { authenticationFields } from "../../../util/authentication-fields";
import { UserV2 } from "../../../models/v2/users-v2";
import { SearchIndex } from "../../../enums/search-index";
import { fetchModuleFilterFieldsBasedOnPermissionV2 } from "../../../util/fetch-module-filter-fields-based-on-permission-v2";
import { FeatureVersion } from "../../../models/feature-versions";

const router = express.Router();

const { columnsMapping, filterFieldMapping } = ListAPIHelper.prepareColumnAndFilterFieldMappingsForModule(authenticationFields.usersV2);
const facetQueryMapping = ListAPIHelper.prepareFacetQueryMappings(authenticationFields.usersV2);
const sectionWiseFacets = ListAPIHelper.fetchFacetFieldsBySectionsForModule(authenticationFields.usersV2);

router.get(
    "/v2/authentication/users",
    responseHandler,
    currentUser,
    requireAuth,
    getAllUsersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };

            // Step 1: Check user has permission to read all users
            const hasPermission = await hasGlobalAction(req, "ListUsers");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Case: Check which version of authentication is enabled
            const version = await FeatureVersion.findOne({ name: "authentication" }).lean().exec();

            const fieldsToRemove = new Set<string>();

            if (version && version.currentVersion === "v1") {
                fieldsToRemove.add("isEmailVerified");
                fieldsToRemove.add("isAccountSetupDone");
                fieldsToRemove.add("isAccountLocked");
            }
            else if (version && version.currentVersion === "v2") {
                fieldsToRemove.add("isOwner");
            }

            if (version) {
                // Remove fields from all mappings
                for (const field of fieldsToRemove) {
                    delete columnsMapping.commonBase[String(field)];
                    delete filterFieldMapping.commonBase[String(field)];
                    delete facetQueryMapping[String(field)];
                }

                sectionWiseFacets.commonBase = sectionWiseFacets.commonBase.filter(field => !fieldsToRemove.has(field));
            }

            const { search, filter, sort, skipToken } = req.query;

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            if (skipToken) {
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_USERS);
                return res.sendResponse(response ?? {}, {});
            }

            const sectionWiseActions: { name: string, path: any, action?: string }[] = [
                {
                    name: "users",
                    path: authenticationFields.usersV2.commonBase
                },
                {
                    name: "modifiedAt",
                    path: authenticationFields.modifiedAt
                }
            ];

            // Step 3: Fetch the fields by permissions assigned
            const { userColumns, userFacets, userFilters } = ListAPIHelper.prepareUserColumnsFiltersAndFacetsBasedOnPermissions({
                assignedActions: new Set(),
                sectionWiseActions,
                columnsMapping,
                filterFieldMapping,
                facets: sectionWiseFacets
            });

            // Step 4: Prepare the sort queries
            const { atlasSort, regularSort, sortProcessingStages } = ListAPIHelper.prepareUserSortQuery({ path: authenticationFields.usersV2, sort: sort as string, userColumns });

            // Step 5: Prepare filters based on the filter reqest
            const { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters } = ListAPIHelper.filterParserMust({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters } = ListAPIHelper.filterParserMustNot({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { blankFilters, blankAppliedFilters } = ListAPIHelper.filterParserBlank({ filter: filter as string, fieldMapping: userFilters });

            // Step 6: Prepare applied filters
            const appliedFilters = { ...mustAppliedFilters, ...mustNotAppliedFilters, ...blankAppliedFilters };

            let matchQuery: any = null;
            let facetMatchQuery: any = null;
            if (blankFilters.length || matchFilters.length || matchNotFilters.length) {
                matchQuery = { $and: [...blankFilters, ...matchFilters, ...matchNotFilters] };
            }
            if (blankFilters.length || facetMatchFilters.length || facetMatchNotFilters.length) {
                facetMatchQuery = { $and: [...blankFilters, ...facetMatchFilters, ...facetMatchNotFilters] };
            }

            const commonMust: any[] = [];

            if (search) {
                commonMust.push({
                    text: {
                        query: search,
                        path: "keys"
                    }
                });
            }

            // Step 7: Prepare Data query using fields, filters and sortings
            const dataQuery = ListAPIHelper.prepareDataQuery({
                collection: UserV2,
                searchIndex: SearchIndex.USERS_DEFAULT,
                must: [...commonMust, ...mustFilters],
                mustNot: mustNotFilters,
                matchQuery,
                projection: userColumns,
                atlasSort,
                regularSort,
                sortProcessingStages
            });

            // Step 8: Prepare facet queries to get counts for all the flexible type fields
            const facetsQuery = ListAPIHelper.prepareFacetQuery({
                collection: UserV2,
                searchIndex: SearchIndex.USERS_DEFAULT,
                must: [...commonMust, ...mustFiltersForFacets],
                mustNot: mustNotFiltersForFacets,
                facetMatchQuery,
                facetFilter,
                userFacets,
                facetQueryMapping
            });

            // Step 9: Get all the values for flexible type fields
            const fieldPromise = fetchModuleFilterFieldsBasedOnPermissionV2({
                sections: sectionWiseActions,
                assignedActions: new Set()
            });

            const [data, facets, fields] = await Promise.all([dataQuery, facetsQuery, fieldPromise]);

            let updatedFields: FilterFields[] = [];
            updatedFields = fields.filter(field => !fieldsToRemove.has(field.name));

            // Step 10: Prepare the flexible field value counts and quick filters
            ListAPIHelper.prepareQuickFilters({ facetsWithFields: facets[0], fields: updatedFields, appliedFilters });

            // Step 11: For applied filters get the selected values
            ListAPIHelper.prepareAppliedFiltersWithValues({ appliedFilters, fields: updatedFields });

            // Step 12: Create pagination and send the response
            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_USERS,
                updatedFields,
                appliedFilters,
                data
            );

            if (response) {
                return res.sendResponse(response, {});
            }
            return res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Authentication.GetAllUsersV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as getAllUsersV2Router };
