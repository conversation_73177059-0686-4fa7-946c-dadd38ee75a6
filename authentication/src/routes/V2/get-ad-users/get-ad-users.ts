import { parse } from "url";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    requireAuth,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";

import { GetUsersListParams, GetUsersListResponse } from "../../../interfaces";
import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { getADusersValidation } from "./get-ad-users.validation";

const router = express.Router();

router.get(
    "/v2/authentication/users/ad",
    responseHandler,
    currentUser,
    requireAuth,
    getADusersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Check user has permission to get all AD users
            const hasPermission = await hasGlobalAction(req, "GetUsersOfActiveDirectory");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { search, top, skipToken } = req.query as GetUsersListParams;

            const accessToken = await microsoftGraphAPI.getAccessToken();

            let usersList: GetUsersListResponse;
            if (search) {
                usersList = await microsoftGraphAPI.searchUsers({ accessToken, search: search, skipToken, top });
            }
            else {
                usersList = await microsoftGraphAPI.getUsers({ accessToken, top, skipToken });
            }

            const respObj = {
                totalRows: usersList.data["@odata.count"],
                data: usersList.data.value,
                skipToken: ""
            };
            if (usersList.data["@odata.nextLink"]) {
                respObj["skipToken"] = parse(usersList.data["@odata.nextLink"], true).query["$skiptoken"] as string;
            }

            res.sendResponse(respObj, {});
        }
        catch (error) {
            console.error("Authentication.GetADUsersV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as getADUsersV2Router };
