import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    generateSearch<PERSON><PERSON><PERSON>,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";

import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { UpdateUserDetailsParams } from "../../../interfaces";
import { getSearchFields, getUserName, intersectTwoObjects, isMOXFIVEUser, pickFromObject } from "../../../util";
import { updateUserDetailsValidation } from "./update-user-details.validation";
import { UserV2 } from "../../../models/v2/users-v2";
import { flexibleFieldValidationV2 } from "../../../util/v2/flexible-field-validation-v2";
import { MakeSingleSectionFieldsFlexibleFields } from "../../../util/make-single-section-fields-flexible-fields";
import { authenticationFields } from "../../../util/authentication-fields";
import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
import { AuditLogV2 } from "../../../services/v2/audit-log";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../enums/operation-types.enum";

const router = express.Router();

const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.users);

router.put(
    "/v2/authentication/users/:userId",
    responseHandler,
    currentUser,
    requireAuth,
    updateUserDetailsValidation,
    validateRequest,
    flexibleFieldValidationV2(flexibleFieldsNameKey),
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            req.action = "UpdateUserDetails";

            const { userId } = req.params;
            const isMoxfiveUser = isMOXFIVEUser({
                req,
                throwError: false
            });

            // If provided userId is not same as logged in user then throw an error
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            // If role property has been provided and user is not moxfive user then throw an error
            if (req.body.hasOwnProperty("role") && !isMoxfiveUser) {
                throw new InsufficientPrivilagesError();
            }

            // if (req.body.hasOwnProperty("jobTitle") || req.body.hasOwnProperty("companyName")) {
            //     await canUpdateUserSpecificField(req.currentUser?.organizationId!, req.currentUser?.id!);
            // }

            // Fetch user details, if not found then throw an error
            const user = await UserV2.findById(userId).session(mongoTransaction.session);
            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Check if provided user is moxfive user, if it is then throw error. Means only Non-MF users can be updated
            const isProvidedUserMoxfiveUser = isMOXFIVEUser({
                req,
                throwError: false,
                organizationId: String(user.organization?.id)
            });

            if(isProvidedUserMoxfiveUser) {
                throw new InvalidActionError("This operation is not allowed.");
            }

            // Fetch data only which are updated
            const userData: UpdateUserDetailsParams = pickFromObject(
                user,
                ["firstName", "lastName", "displayName", "userLocation", "officePhone", "jobTitle", "role"]
            );

            const updatedData: UpdateUserDetailsParams = intersectTwoObjects(userData, req.body);

            // If there is no data updated then return
            if (!(Object.keys(updatedData).length)) {
                await mongoTransaction.abortTransaction();

                return res.sendResponse({
                    meta: {
                        message: "User updated successfully"
                    }
                }, {});
            }

            // Get Microsoft Access Token and fetch update user details
            const accessToken = await microsoftGraphAPI.getAccessToken();
            await microsoftGraphAPI.updateUserDetails({ accessToken, azureId: user.azureId, ...updatedData });

            // If firstName, lastName or displayName updated then update the name & regenerate keys
            if(updatedData.firstName || updatedData.lastName || updatedData.displayName) {
                // Get username
                updatedData.name = getUserName({
                    firstName: updatedData.firstName ?? user.firstName,
                    lastName: updatedData.lastName ?? user.lastName,
                    displayName: updatedData.displayName ?? user.displayName,
                });

                // Generate keys and save it
                const searchFields = getSearchFields(
                    updatedData.firstName || user.firstName,
                    updatedData.lastName || user.lastName,
                    updatedData.displayName || user.displayName,
                    user.email
                );
                const searchKeys = generateSearchKeys(searchFields);
                user.keys = searchKeys as string[];
            }

            Object.assign(user, updatedData);

            await user.save({ session: mongoTransaction.session });

            // Add user entry in v1 as well
            await userV2ToV1Sync({
                user: user.toObject(),
                operationType: OperationTypesEnums.UPDATE,
                session: mongoTransaction.session
            });

            // Commit transaction
            await mongoTransaction.commitTransaction();

            await userUpdatedPublisherV2Wrapper(user);

            // Fetch modified properties for audit log
            const data = { ...updatedData };
            const oldData = { ...userData };

            const modifiedProperties = AuditLogV2.prepareModifiedProperties({
                data,
                oldData,
                target: TargetType.USER
            });

            res.sendResponse({
                meta: {
                    message: "User updated successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user.id),
                            name: user.name,
                            email: user.email,
                            azureId: user.azureId
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organization?.id),
                modifiedProperties
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.UpdateUserDetailsV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as updateUserDetailsV2Router };
