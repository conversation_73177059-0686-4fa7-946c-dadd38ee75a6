/* eslint-disable max-statements */
import express, { NextFunction, Request, Response } from "express";
import {
    createPagination,
    currentUser,
    FilterFields,
    getPage,
    hasGlobalAction,
    InsufficientPrivilages<PERSON>rror,
    ListAPIHelper,
    NotFoundCode,
    PageResponseObj,
    PaginationEntity,
    require<PERSON>uth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import { getOrganizationUsersValidation } from "../../get-organization-users/get-organization-users.validation";
import { authenticationFields } from "../../../util/authentication-fields";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { fetchModuleFilterFieldsBasedOnPermissionV2 } from "../../../util/fetch-module-filter-fields-based-on-permission-v2";
import { SearchIndex } from "../../../enums/search-index";
import { UserV2 } from "../../../models/v2/users-v2";
import { convertToObjectId } from "../../../util/common";
import { FeatureVersion } from "../../../models/feature-versions";

const router = express.Router();

const { columnsMapping, filterFieldMapping } = ListAPIHelper.prepareColumnAndFilterFieldMappingsForModule(authenticationFields.organizationUsersV2);
const facetQueryMapping = ListAPIHelper.prepareFacetQueryMappings(authenticationFields.organizationUsersV2);
const sectionWiseFacets = ListAPIHelper.fetchFacetFieldsBySectionsForModule(authenticationFields.organizationUsersV2);

router.get(
    "/v2/authentication/organizations/:organizationId/users",
    responseHandler,
    currentUser,
    requireAuth,
    getOrganizationUsersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };

            // Step 1: Check user has permission to read all users
            const hasPermission = await hasGlobalAction(req, "ListOrganizationUsers");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Case: Check which version of authentication is enabled
            const version = await FeatureVersion.findOne({ name: "authentication" }).lean().exec();

            const fieldsToRemove = new Set<string>();

            if (version && version.currentVersion === "v1") {
                fieldsToRemove.add("isEmailVerified");
                fieldsToRemove.add("isAccountSetupDone");
                fieldsToRemove.add("isAccountLocked");
            }
            else if (version && version.currentVersion === "v2") {
                fieldsToRemove.add("isOwner");
            }

            if (version) {
                // Remove fields from all mappings
                for (const field of fieldsToRemove) {
                    delete columnsMapping.commonBase[String(field)];
                    delete filterFieldMapping.commonBase[String(field)];
                    delete facetQueryMapping[String(field)];
                }

                sectionWiseFacets.commonBase = sectionWiseFacets.commonBase.filter(field => !fieldsToRemove.has(field));
            }

            // Step 2: If we are getting skipToken in request then just fetch the requested page and return the response
            const { search, filter, sort, skipToken } = req.query;

            if (skipToken) {
                const response: PageResponseObj | null = await getPage(req, PaginationEntity.LIST_ORGANIZATION_USERS);
                return res.sendResponse(response ?? {}, {});
            }

            // Step 3: Check organization is valid or not, if not then throw an error
            const { organizationId } = req.params;
            const organization = await OrganizationV2.findById(organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const sectionWiseActions: { name: string, path: any, action?: string }[] = [
                {
                    name: "users",
                    path: authenticationFields.organizationUsersV2.commonBase
                },
                {
                    name: "modifiedAt",
                    path: authenticationFields.modifiedAt
                }
            ];

            // Step 4: Fetch the fields by permissions assigned
            const { userColumns, userFacets, userFilters } = ListAPIHelper.prepareUserColumnsFiltersAndFacetsBasedOnPermissions({
                assignedActions: new Set(),
                sectionWiseActions,
                columnsMapping,
                filterFieldMapping,
                facets: sectionWiseFacets
            });

            // Step 5: Prepare the sort queries
            const { atlasSort, regularSort, sortProcessingStages } = ListAPIHelper.prepareUserSortQuery({ path: authenticationFields.usersV2, sort: sort as string, userColumns });

            // Step 6: Prepare filters based on the filter reqest
            const { mustFilters, mustFiltersForFacets, facetFilter, mustAppliedFilters, matchFilters, facetMatchFilters } = ListAPIHelper.filterParserMust({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { mustNotFilters, mustNotFiltersForFacets, mustNotAppliedFilters, matchNotFilters, facetMatchNotFilters } = ListAPIHelper.filterParserMustNot({
                filter: filter as string, fieldMapping: userFilters, facetFilterMapping: userFacets
            });
            const { blankFilters, blankAppliedFilters } = ListAPIHelper.filterParserBlank({ filter: filter as string, fieldMapping: userFilters });

            // Step 7: Prepare applied filters
            const appliedFilters = { ...mustAppliedFilters, ...mustNotAppliedFilters, ...blankAppliedFilters };

            let matchQuery: any = null;
            let facetMatchQuery: any = null;
            if (blankFilters.length || matchFilters.length || matchNotFilters.length) {
                matchQuery = { $and: [...blankFilters, ...matchFilters, ...matchNotFilters] };
            }
            if (blankFilters.length || facetMatchFilters.length || facetMatchNotFilters.length) {
                facetMatchQuery = { $and: [...blankFilters, ...facetMatchFilters, ...facetMatchNotFilters] };
            }

            const commonMust: any[] = [
                {
                    in: {
                        path: "organization.id",
                        value: convertToObjectId(organizationId)
                    }
                }
            ];

            if (search) {
                commonMust.push({
                    text: {
                        query: search,
                        path: "keys"
                    }
                });
            }

            // Step 8: Prepare Data query using fields, filters and sortings
            const dataQuery = ListAPIHelper.prepareDataQuery({
                collection: UserV2,
                searchIndex: SearchIndex.USERS_DEFAULT,
                must: [...commonMust, ...mustFilters],
                mustNot: mustNotFilters,
                matchQuery,
                projection: userColumns,
                atlasSort,
                regularSort,
                sortProcessingStages
            });

            // Step 9: Prepare facet queries to get counts for all the flexible type fields
            const facetsQuery = ListAPIHelper.prepareFacetQuery({
                collection: UserV2,
                searchIndex: SearchIndex.USERS_DEFAULT,
                must: [...commonMust, ...mustFiltersForFacets],
                mustNot: mustNotFiltersForFacets,
                facetMatchQuery,
                facetFilter,
                userFacets,
                facetQueryMapping
            });

            // Step 10: Get all the values for flexible type fields
            const fieldPromise = fetchModuleFilterFieldsBasedOnPermissionV2({
                sections: sectionWiseActions,
                assignedActions: new Set()
            });

            const [data, facets, fields] = await Promise.all([dataQuery, facetsQuery, fieldPromise]);

            let updatedFields: FilterFields[] = [];
            updatedFields = fields.filter(field => !fieldsToRemove.has(field.name));

            // Step 11: Prepare the flexible field value counts and quick filters
            ListAPIHelper.prepareQuickFilters({ facetsWithFields: facets[0], fields: updatedFields, appliedFilters });

            // Step 12: For applied filters get the selected values
            ListAPIHelper.prepareAppliedFiltersWithValues({ appliedFilters, fields: updatedFields });

            // Step 13: Create pagination and send the response
            const response: PageResponseObj | null = await createPagination(req,
                PaginationEntity.LIST_ORGANIZATION_USERS,
                updatedFields,
                appliedFilters,
                data
            );

            if (response) {
                return res.sendResponse(response, {});
            }
            return res.sendResponse({}, {});
        }
        catch (error) {
            console.error("Resilience.getOrganizationUsersRouterV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as getOrganizationUsersRouterV2 };
