import express, { NextFunction, Request, Response } from "express";
import { UserAzureDetails } from "../../../interfaces";
import { UserV2 } from "../../../models/v2/users-v2";
import {
    UserAzureUpdates,
    UserAzureUpdatesDoc,
} from "../../../models/user-azure-updates";
import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { getUserName, intersectTwoObjects } from "../../../util";
import { userUpdatedPublisherV2Wrapper } from "../../../util/v2/user-updated-publisher-wrapper";
import { mapUserDetailsV2 } from "../../../util/v2/mapUserDetailsV2";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { userV2ToV1Sync } from "../../../util/user-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../enums/operation-types.enum";

interface userDetailsObj {
  data: UserAzureDetails;
}

const router = express.Router();

router.post(
    "/v2/authentication/userupdate/callback",
    async (req: Request, res: Response, next: NextFunction):Promise<any> => {
        let mongoTransaction: MongoTransaction | null = null;

        try {
            req.action = "AzureUsersUpdatesWebhook";

            const { validationToken } = req.query;
            const { value } = req.body;

            if (validationToken) {
                return res.set("content-type", "text/plain").send(validationToken);
            }

            if (Array.isArray(value) && value.length) {
                // Send 202 (Accepted) response to acknowledge microsoft azure
                res.status(202).send();

                // Start Mongo Transaction
                mongoTransaction = new MongoTransaction();
                mongoTransaction.startTransaction();

                // Prepare the data and save it in the db
                const preparedObj: any = [];
                value.map((item: any) => {
                    if (
                        !process.env.CLIENT_STATE || item.clientState === process.env.CLIENT_STATE
                    ) {
                        preparedObj.push({
                            userId: item?.resourceData["id"],
                            changeType: item?.changeType,
                            status: "Not Started",
                        });
                    }
                });

                await UserAzureUpdates.insertMany(preparedObj);
                const notStartedUsers = await UserAzureUpdates.find({
                    status: "Not Started",
                });

                // Get unique records to process them
                const uniqueRecords: UserAzureUpdatesDoc[] = [];
                notStartedUsers.forEach((user) => {
                    if (
                        !uniqueRecords.find(
                            (record: UserAzureUpdatesDoc) => record.userId === user.userId && record.changeType === user.changeType
                        )
                    ) {
                        uniqueRecords.push(user);
                    }
                });

                // Change the started records to in progress
                await Promise.all(
                    notStartedUsers.map(async (user) => {
                        user.status = "In Progress";
                        await user.save();
                    })
                );

                await Promise.all(
                    uniqueRecords.map(async (user) => {
                        try {
                            // Check user exist in our system, if user don't exists then delete the user azure updates colleciton details
                            const oldUserDetails = await UserV2.findOne({
                                azureId: user.userId,
                            }).session(mongoTransaction?.session ?? null);
                            if (!oldUserDetails) {
                                await UserAzureUpdates.deleteMany({
                                    userId: user.userId,
                                    status: "In Progress",
                                });
                                return false;
                            }

                            if (user.changeType === "updated") {
                                // If user is valid then update user details
                                const accessToken = await microsoftGraphAPI.getAccessToken();
                                const newUserDetails: userDetailsObj = await microsoftGraphAPI.getAzureUserDetailsById({
                                    accessToken,
                                    userId: user.userId,
                                });

                                const mappedUserDetails: any = mapUserDetailsV2(newUserDetails.data);
                                // if (mappedUserDetails.userLocation.country) {
                                //     const countryDetails = await Country.findOne({ name: { $regex: `^${mappedUserDetails.userLocation.country}$`, $options: "i" } }).lean().exec();
                                //     mappedUserDetails.userLocation.country = countryDetails ? String(countryDetails.name) : null;
                                // }

                                // if (mappedUserDetails.userLocation.state) {
                                //     const query = mappedUserDetails.userLocation.country
                                //         ? { name: { $regex: `^${mappedUserDetails.userLocation.state}$`, $options: "i" }, countryId: mappedUserDetails.userLocation.country }
                                //         : { name: { $regex: `^${mappedUserDetails.userLocation.state}$`, $options: "i" } };
                                //     const stateDetails = await State.findOne(query).lean().exec();
                                //     mappedUserDetails.userLocation.state = stateDetails ? String(stateDetails.name) : null;
                                // }

                                // if (mappedUserDetails.userLocation.city) {
                                //     const query = mappedUserDetails.userLocation.state
                                //         ? { name: { $regex: `^${mappedUserDetails.userLocation.city}$`, $options: "i" }, stateId: mappedUserDetails.userLocation.state }
                                //         : { name: { $regex: `^${mappedUserDetails.userLocation.city}$`, $options: "i" } };
                                //     const cityDetails = await City.findOne(query).lean().exec();
                                //     mappedUserDetails.userLocation.city = cityDetails ? String(cityDetails.name) : null;
                                // }

                                // if (!oldUserDetails.userLocation && (mappedUserDetails.userLocation.addressline1 || mappedUserDetails.userLocation.country ||
                                //     mappedUserDetails.userLocation.city || mappedUserDetails.userLocation.state || mappedUserDetails.userLocation.zip)) {
                                //     oldUserDetails.userLocation = {
                                //         addressline1: null,
                                //         addressline2: null,
                                //         country: null,
                                //         countryShortName: null,
                                //         state: null,
                                //         stateShortName: null,
                                //         city: null,
                                //         cityShortName: null,
                                //         zip: null,
                                //         latitude: null,
                                //         longitude: null
                                //     }
                                // }
                                // else {
                                //     mappedUserDetails.userLocation = null;
                                // }

                                const updatedData = intersectTwoObjects(
                                    oldUserDetails,
                                    mappedUserDetails
                                );

                                if (Object.keys(updatedData).length) {
                                    // If firstName, lastName or displayName updated then update the name
                                    if (updatedData.firstName || updatedData.lastName || updatedData.displayName) {
                                        updatedData.name = getUserName({
                                            firstName: updatedData.firstName ?? oldUserDetails.firstName,
                                            lastName: updatedData.lastName ?? oldUserDetails.lastName,
                                            displayName: updatedData.displayName ?? oldUserDetails.displayName,
                                        });
                                    }

                                    Object.assign(oldUserDetails!, updatedData);
                                    await oldUserDetails?.save({ session: mongoTransaction?.session ?? null });

                                    // Sync in User V1
                                    await userV2ToV1Sync({
                                        user: oldUserDetails.toObject(),
                                        operationType: OperationTypesEnums.UPDATE,
                                        session: mongoTransaction?.session ?? null
                                    });

                                    // Commit transaction
                                    await mongoTransaction?.commitTransaction();

                                    await userUpdatedPublisherV2Wrapper(oldUserDetails!);
                                }
                            }
                            else {
                                // If user is deleted from azure then disable the user in our system
                                oldUserDetails.isEnabled = false;
                                await oldUserDetails.save({ session: mongoTransaction?.session ?? null });

                                // Sync in User V1
                                await userV2ToV1Sync({
                                    user: oldUserDetails.toObject(),
                                    operationType: OperationTypesEnums.UPDATE,
                                    session: mongoTransaction?.session ?? null
                                });

                                // Commit transaction
                                await mongoTransaction?.commitTransaction();

                                await userUpdatedPublisherV2Wrapper(oldUserDetails!);
                            }

                            // Delete the records after update
                            await UserAzureUpdates.deleteMany({
                                userId: user.userId,
                                status: "In Progress",
                            });
                        }
                        catch (err) {
                            // Abort transaction
                            if(mongoTransaction) {
                                await mongoTransaction.abortTransaction();
                            }

                            if (err instanceof Error) {
                                await UserAzureUpdates.updateMany(
                                    { userId: user.userId, status: "In Progress" },
                                    { $set: { errorMessage: err.message, status: "Not Started" } }
                                );
                            }

                            // Send an email notification
                        }
                    })
                );
            }
        }
        catch (error) {
            // Abort transaction
            if(mongoTransaction) {
                await mongoTransaction.abortTransaction();
            }

            console.error("Authetication.UserUpdateCallbackV2");
            console.error(error);
            next(error);
        }
    }
);

export { router as userUpdateCallbackV2Router };
