import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest,
    File
} from "@moxfive-llc/common";
import { getSpecificOrganizationValidation } from "./get-specific-organization.validation";
import { unSanitizeEmail, unSanitizeURI } from "../../../util";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { authenticationFields } from "../../../util/authentication-fields";
import { processSingleSelectFieldsV2 } from  "../../../util/v2/process-single-select-fields-v2";
import { Connections } from "../../../models/connections";

const router = express.Router();

router.get("/v2/authentication/organizations/:organizationId",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    getSpecificOrganizationValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Step 1: Check user has permission to get specific organization details
            const hasPermission = await hasGlobalAction(req, "GetOrganizationDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 2: Check for the organization, if it doesn't exist throw an error
            const organization = await OrganizationV2.findById(organizationId, { azureId: 0, version: 0 }).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3: UnSanitize the website, hotlineEmail & billingContactEmail
            const { owner, member, ...org } = organization;
            if (org.website) {
                org.website = unSanitizeURI(org.website);
            }
            if (org.hotlineEmail) {
                org.hotlineEmail = unSanitizeEmail(org.hotlineEmail);
            }
            if (org.billingContactEmail) {
                org.billingContactEmail = unSanitizeEmail(org.billingContactEmail);
            }

            // Step 4: Set profile and favicon
            let profile: {
                id: string,
                fileName: string | null
            } | null = null;

            let favicon: {
                id: string,
                fileName: string | null
            } | null = null;

            if (org.profile) {
                const file = await File.findById(org.profile).lean().exec();
                profile = {
                    id: org.profile,
                    fileName: file?.originalFileName ?? null
                };
            }
            if (org.favicon) {
                const file = await File.findById(org.favicon).lean().exec();

                favicon = {
                    id: org.favicon,
                    fileName: file?.originalFileName ?? null
                };
            }

            // Check whether organization has connection enabled or not
            const connection = await Connections.findOne({ "organization.id": organizationId }, { _id: 1 }).lean().exec();

            let response = {
                ...org,
                id: String(org._id),
                owners: owner.length,
                members: member.length,
                profile,
                favicon,
                connectionId: connection?._id ?? null
            };

            response = processSingleSelectFieldsV2(authenticationFields.organizations, response);

            // Step 5: Send Response
            res.sendResponse(response, {});
        }
        catch (error) {
            console.error("Authentication.GetSpecificOrganizationV2");
            console.error(error);
            next(error);
        }
    });

export { router as getSpecificOrganizationV2Router };
