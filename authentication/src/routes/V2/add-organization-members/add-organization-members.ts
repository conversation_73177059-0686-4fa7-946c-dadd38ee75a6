/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidMembersBadRequestError,
    MembersAlreadyExistBadRequestError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    SucceededPartially,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { UserAzureDetails } from "../../../interfaces";
import { microsoftGraphAPI } from "../../../services/microsoft-graph-api";
import { addOrganizationMembersValidation } from "./add-organization-members.validation";
import { OrganizationV2 } from "../../../models/v2/oragnizations-v2";
import { OrganizationUpdatedPublisherWrapperV2 } from "../../../util/v2/organization-updated-publisher-wrapper";
import { UserV2 } from "../../../models/v2/users-v2";
import { MongoTransaction } from "../../../services/mongo-transaction";
import { organizationV2ToV1Sync } from "../../../util/organization-v2-to-v1-sync";
import { OperationTypesEnums } from "../../../enums/operation-types.enum";
import { userCreatedPublisherV2Wrapper } from "../../../util/v2/user-created-publisher-wrapper-v2";

const router = express.Router();

router.post("/v2/authentication/organizations/:organizationId/members",
    responseHandler,
    currentUser,
    requireAuth,
    addOrganizationMembersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        // Start Mongo Transaction
        const mongoTransaction = new MongoTransaction();
        mongoTransaction.startTransaction();

        try {
            // req.currentUser = {
            //     id: "62397c3b020359bf2f08426a",
            //     email: "<EMAIL>",
            //     organizationId: "62305427f5c54aaa68d33901",
            //     userName: "Romit Gandhi",
            //     isSystemUser: false
            // };
            // Check user has permission to add users of organization
            const hasPermission = await hasGlobalAction(req, "AddMemberToOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { members }: { members: UserAzureDetails[] } = req.body;
            const { organizationId } = req.params;

            // Step 1: Find organization and if it's not present then throw NotFoundError
            const organization = await OrganizationV2.findById(organizationId).session(mongoTransaction.session);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Find which members are part of same org and which aren't
            const usersInSameOrg: string[] = [];
            const usersNotInSameOrg: string[] = [];
            const usersNotInSameOrgDetails: UserAzureDetails[] = [];
            const usersInOtherOrg: string[] = [];
            const usersInSameOrgEmails: string[] = [];
            const usersInOtherOrgEmails: string[] = [];
            const targetedUsers: {type: TargetType, details: {id: string, name: string, email: string, azureId: string}}[] = [];

            members.forEach(member => {
                if (organization.member.includes(member.id) || organization.owner.includes(member.id)) {
                    usersInSameOrg.push(member.id);
                }
                else {
                    usersNotInSameOrg.push(member.id);
                    usersNotInSameOrgDetails.push(member);
                }
            });

            if (usersInSameOrg.length) {
                usersInSameOrg.forEach(azureId => {
                    usersInSameOrgEmails.push(members.find(member => member.id === azureId)?.mail!);
                });
            }

            if (members.length === usersInSameOrgEmails.length) {
                throw new MembersAlreadyExistBadRequestError(usersInSameOrgEmails);
            }

            if (usersNotInSameOrg.length) {
                // Step 3: Check provided owners are already associated with other Org as owner or member
                if (usersNotInSameOrg.length > 0) {
                    const existingUsersOrgs = await OrganizationV2.findUserAlreadyInOtherOrganization(usersNotInSameOrg);

                    if (existingUsersOrgs && existingUsersOrgs.length) {
                        existingUsersOrgs.forEach(org => {
                            org.member.forEach(memberId => {
                                if (usersNotInSameOrg.includes(memberId)) {
                                    usersInOtherOrg.push(memberId);
                                }
                            });

                            org.owner.forEach(ownerId => {
                                if (usersNotInSameOrg.includes(ownerId)) {
                                    usersInOtherOrg.push(ownerId);
                                }
                            });
                        });
                    }

                    if (usersInOtherOrg.length) {
                        usersInOtherOrg.forEach(azureId => {
                            usersInOtherOrgEmails.push(members.find(member => member.id === azureId)?.mail!);
                        });
                    }

                    if (members.length === usersInOtherOrgEmails.length) {
                        throw new InvalidMembersBadRequestError(usersInOtherOrgEmails);
                    }
                }

                if (usersInOtherOrg.length) {
                    usersInOtherOrg.forEach(userId => {
                        const index = usersNotInSameOrg.indexOf(userId);
                        if (index !== -1) {
                            usersNotInSameOrg.splice(index, 1);
                            usersNotInSameOrgDetails.splice(index, 1);
                        }
                    });
                }

                if(usersNotInSameOrg.length) {
                    // Step 4: Get the authorization token
                    const token = await microsoftGraphAPI.getAccessToken();

                    // Step 5 : Add users in the group microsoft azure
                    await microsoftGraphAPI.addMembersInOrganization(
                        {
                            token,
                            orgId: organization.azureId,
                            members: [...usersNotInSameOrg]
                        });

                    // Step 6 : Add users in group DB
                    organization.member = organization.member.concat([...usersNotInSameOrg]);
                    await organization.save({ session: mongoTransaction.session });

                    // Sync in Organization V1
                    await organizationV2ToV1Sync({
                        organization: organization.toObject(),
                        operationType: OperationTypesEnums.UPDATE,
                        session: mongoTransaction.session
                    });

                    // Step 7: Insert users if not exist
                    const addedUsers = await UserV2.insertUsersIfNotExistV2(usersNotInSameOrgDetails, organization, mongoTransaction.session);

                    // Commit transaction
                    await mongoTransaction.commitTransaction();

                    // Publish NATS Events
                    await OrganizationUpdatedPublisherWrapperV2(organization);

                    await Promise.all(addedUsers.map(async user => {
                        targetedUsers.push({
                            type: TargetType.USER,
                            details: {
                                id: user.id,
                                name: user.name,
                                email: user.email,
                                azureId: user.azureId
                            }
                        });

                        await userCreatedPublisherV2Wrapper(user);
                    }));
                }
            }

            const errors = [];
            if (usersInSameOrgEmails.length) {
                errors.push({
                    attributes: usersInSameOrgEmails,
                    message: "These members are already exist in the specified organization."
                });
            }

            if (usersInOtherOrgEmails.length) {
                errors.push({
                    attributes: usersInOtherOrgEmails,
                    message: "Disallowed memebers. These members are already part of other organization."
                });
            }

            if (errors.length) {
                const finalErrors = [{
                    parameters: errors
                }];
                throw new SucceededPartially(finalErrors, "One or more users are failed to add in the specified organization.");
            }

            // Step 8: Send Response
            res.sendResponse({
                meta: {
                    message: "Organization members added successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    },
                    ...targetedUsers
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            // Abort transaction
            await mongoTransaction.abortTransaction();

            console.error("Authentication.AddOrganizationMembersV2");
            console.error(error);
            next(error);
        }

    });

export { router as addOrganizationMembersV2Router };
