import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilages<PERSON><PERSON>r,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import mongoose from "mongoose";
import {
    FilterQuery,
    GetOrganizationUsersQueryResponse,
    GetOrganizationUsersResponse,
    SortQuery
} from "../../interfaces";
import { Organization } from "../../models/organization";
import { UserFlexibleField } from "../../models/user-flexible-fields";
import { fetchDefaultSortObject, hasWhiteSpace } from "../../util";
import { getOrganizationUsersValidation } from "./get-organization-users.validation";
import { authenticationFields } from "../../util/authentication-fields";
import {
    fetchFiltersFromMultipleSectionsBasedOnPermission
} from "../../util/fetch-filters-from-multiple-sections-based-on-permission";
import {
    fetchSortFromMultipleSectionsBasedOnPermission
} from "../../util/fetch-sort-from-multiple-sections-based-on-permission";
import { UserFlexibleFields } from "../../enums/user-flexible-field.enum";

const router = express.Router();

router.get("/v1/organizations/:organizationId/users",
    responseHandler,
    currentUser,
    requireAuth,
    getOrganizationUsersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to read users of organization
            const hasPermission = await hasGlobalAction(req, "ListOrganizationUsers");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const defaultLimit = 100;
            const searchQuery: { "users.keys"?: string | { $in: string[] } } = {};

            const { search, page = 1, limit = defaultLimit, filter, sort  } = req.query;
            const { organizationId } = req.params;

            const skip = (page as number - 1) * (limit as number);

            // Step 1: Make search query
            if (search) {
                const searchTerm = search as string;
                searchQuery["users.keys"] = hasWhiteSpace(searchTerm) ? { $in: searchTerm.split(" ") } : searchTerm;
            }

            // Step 2: If organization not found then throw NotFoundError
            const organization = await Organization.findById(organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authenticationFields.organizationUsers,
                    prefix: "users"
                },
                {
                    path: authenticationFields.modifiedAt,
                    prefix: "users"
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
            const filtersQuery: FilterQuery = {};

            // If filter is provided then prepare filter query
            if(filter) {
                const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
                    filter: filter as string,
                    sections
                });
                Object.assign(filtersQuery, filters);
            }

            // If sort is provided then prepare sort query
            if(sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });
                Object.assign(sortQuery, sortObj);
            }

            // Step 3: Fetch all organization users with filters(If applied)
            let aggregateQuery:any = [
                { $match: { _id: new mongoose.Types.ObjectId(organizationId) } },
                {
                    $lookup: {
                        from: "users",
                        localField: "owner",
                        foreignField: "azureId",
                        as: "owner"
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "member",
                        foreignField: "azureId",
                        as: "member"
                    },
                },
                {
                    $addFields: {
                        "owner.isOwner": true,
                        "member.isOwner": false
                    },
                },
                {
                    $addFields: {
                        "users": { $concatArrays: ["$owner", "$member"] },
                    }
                },
                {
                    $unwind: "$users",
                },
                { $match: searchQuery },
                { $match: filtersQuery },
            ];

            if(Object.keys(sortQuery).length) {
                aggregateQuery.push({ $sort: sortQuery });
            }

            aggregateQuery = aggregateQuery.concat([
                {
                    $project: {
                        "users.name": 0,
                        "users.azureId": 0,
                        "users.userLocation": 0,
                        "users.organizationId": 0,
                        "users.version": 0,
                        "users.policyIds": 0,
                        "users.applicationPolicyIds": 0,
                        _id: 0
                    }
                },
                {
                    $facet: { totalRows: [{ $count: "users" }], data: [{ $skip: skip }, { $limit: (limit as number) }] }
                }
            ]);
            const result: GetOrganizationUsersQueryResponse[] = await Organization.aggregate(aggregateQuery, {
                collation: {
                    locale: "en",
                    numericOrdering: true
                }
            });

            const users: GetOrganizationUsersResponse = {
                totalRows: 0,
                data: []
            };

            if (result[0]) {
                users.totalRows = result[0].totalRows[0] ? result[0].totalRows[0].users : 0;

                const roles = await UserFlexibleField.findOne({ "key": UserFlexibleFields.Role }, { values: 1 });
                const rolesMap: Map<string, string> = new Map();
                if(roles) {
                    roles.values.forEach(value => {
                        rolesMap.set(String(value._id), value.value);
                    });
                }

                users.data = await Promise.all(result[0].data.map(async data => {
                    const user = { ...data.users };

                    if (user.role) {
                        user.role = {
                            id: String(user.role),
                            value: rolesMap.get(String(user.role)) || ""
                        };
                    }
                    else {
                        user.role = null;
                    }

                    user.id = user._id;
                    delete user._id;
                    delete user.keys;
                    return user;
                }));
            }

            res.sendResponse(users, {});
        }
        catch (error) {
            console.error("Authentication.GetOrganizationUsers");
            console.error(error);
            next(error);
        }
    });

export { router as getOrganizationUsersRouter };
