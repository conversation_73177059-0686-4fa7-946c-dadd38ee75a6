import express, { NextFunction, Request, Response } from "express";
import {
    BasicResourceValueUnacceptableConflictError, ConflictErrorCodes, currentUser,
    deleteFile, File, hasGlobalAction, InsufficientPrivilagesError, uploadFile,
    NotFoundCode, requireAuth, ResourceNotFoundError, SucceededPartially, validateRequest, TargetType, responseHandler,
    validateFileMagicBytes
} from "@moxfive-llc/common";
import { configureMulterForFileUpload } from "../../util/configure-multer-for-file-upload";
import { logoAndFaviconParamValidation } from "./upload-logo-add-favicon.validation";
import { Organization } from "../../models/organization";
import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
import { AuditLog } from "../../services/audit-log";

// Configure multer
const upload = configureMulterForFileUpload();

const router = express.Router();

const filesInformation = [{ name: "profile", maxCount: 1 }, { name: "favicon", maxCount: 1 }];

router.post("/v1/authentication/organizations/:organizationId/upload/logo",
    responseHandler,
    currentUser,
    requireAuth,
    logoAndFaviconParamValidation,
    validateRequest,
    upload.fields(filesInformation),
    validateFileMagicBytes(filesInformation),
    // eslint-disable-next-line max-statements
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // TODO Changes According to requirements
            const hasPermission = await hasGlobalAction(req, "UpdateProfileIcon");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Check whether the organization exists or not
            const organization = await Organization.findById(organizationId);
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            const { imageAndLogoSame } = req.query;

            // Check if more than 2 files are uploaded or not, if yes then throw an error
            const files: any = req.files;
            if(typeof files !== "object" || Array.isArray(files)) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.MAX_UPLOAD_FILES,
                    `At a time only ${filesInformation[0].maxCount} files can be uploaded.`
                );
            }
            if (files?.profile?.length > filesInformation[0].maxCount) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.MAX_UPLOAD_FILES,
                    `At a time only ${filesInformation[0].maxCount} files can be uploaded.`
                );
            }
            if (files?.favicon?.length > filesInformation[1].maxCount) {
                throw new BasicResourceValueUnacceptableConflictError(
                    ConflictErrorCodes.MAX_UPLOAD_FILES,
                    `At a time only ${filesInformation[1].maxCount} files can be uploaded.`
                );
            }

            // Fetch profile image file and favicon logo file
            const failedToUpload: string[] = [];

            const profileImageFiles = organization.profile ? await File.findById(organization.profile).lean().exec() : null;
            const faviconLogoFiles = organization.favicon ? await File.findOne({
                parent: organization.bucketName,
                parentId: organizationId,
                entity: "favicon",
                entityId: organizationId
            }).lean().exec() : null;

            // Save data and oldData
            const data: Record<string, string> = {};
            const oldData = {
                profile: profileImageFiles ? profileImageFiles.originalFileName : null,
                favicon: faviconLogoFiles ? faviconLogoFiles.originalFileName : null
            };

            // If imageAndLogoSame is passed, and it is false then set organization favicon to null and imageAndLogoSame to false
            if (req.query.hasOwnProperty("imageAndLogoSame") && !imageAndLogoSame) {
                organization.favicon = null;
                organization.imageAndLogoSame = false;
            }

            // CASE: profile pic uploaded
            if (files?.profile?.length) {
                /*
                 * CASE: If new profile uploaded and already another profile pic there along with imageAndLogoSame and imageAndLogoSame is passed as false
                 * So, here need to update file's entity to favicon only and will set organization profile to null and imageAndLogoSame to false
                 * */
                if (organization.imageAndLogoSame && !imageAndLogoSame) {
                    await File.findByIdAndUpdate(profileImageFiles?._id, { entity: "favicon" });
                    organization.profile = null;
                    organization.imageAndLogoSame = false;
                }
                /*
                 * CASE: Already existing file entry is there for profile
                 *  So, need to delete that file from files collection and set organization profile to null
                 *  Now, if imageAndLogoSame then set organization favicon to null and imageAndLogoSame to false
                 * */
                else if (profileImageFiles) {
                    await deleteFile({
                        parent: organization.bucketName!,
                        parentId: organizationId,
                        entity: profileImageFiles.entity.toLowerCase(),
                        entityId: organizationId,
                        fileId: profileImageFiles._id as string,
                        AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                    });

                    organization.profile = null;

                    if(organization.imageAndLogoSame) {
                        organization.favicon = null;
                        organization.imageAndLogoSame = false;
                    }
                }

                // Uploading profile and getting its id to be used in next steps
                const profileUploaded = await uploadFile({
                    parent: organization.bucketName!,
                    parentId: organizationId,
                    entity: "profile",
                    entityId: organizationId,
                    filePath: files.profile[0].path,
                    fileName: files.profile[0].originalname,
                    userId: req.currentUser?.id as string,
                    mimeType: files.profile[0].mimetype,
                    AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                });

                // If profile is not uploaded then add entry in the failedToUpload
                if (!profileUploaded) {
                    failedToUpload.push(files.profile[0].originalname);
                }
                else {
                    // Save file id to profile and in the audit log data
                    organization.profile = profileUploaded.id;

                    data.profile =  profileUploaded.id;
                }
            }

            // CASE: favicon uploaded
            if (files?.favicon?.length) {
                /*
                 * CASE: Already existing file entry is there for favicon
                 *  So, need to delete that file from files collection and set organization favicon to null
                 * */
                if (faviconLogoFiles) {
                    await deleteFile({
                        parent: organization.bucketName!,
                        parentId: organizationId,
                        entity: faviconLogoFiles.entity.toLowerCase(),
                        entityId: organizationId,
                        fileId: faviconLogoFiles._id as string,
                        AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                    });

                    organization.favicon = null;
                }

                // Upload the favicon file and getting its id to be used in next steps
                const faviconUploaded = await uploadFile({
                    parent: organization.bucketName!,
                    parentId: organizationId,
                    entity: "favicon",
                    entityId: organizationId,
                    filePath: files.favicon[0].path,
                    fileName: files.favicon[0].originalname,
                    userId: req.currentUser?.id as string,
                    mimeType: files.favicon[0].mimetype,
                    AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                });

                // If favicon is not uploaded then add entry in the failedToUpload
                if (!faviconUploaded) {
                    failedToUpload.push(files.favicon[0].originalname);
                }
                else {
                    // Set favicon, imageAndLogoSame and also set it in the audit log data
                    organization.favicon = faviconUploaded.id;
                    organization.imageAndLogoSame = !!imageAndLogoSame;

                    data.favicon = faviconUploaded.id;
                }
            }

            // CASE: imageAndLogoSame is true and profile pic is uploaded
            if (imageAndLogoSame && organization.profile) {
                /*
                 * CASE: Already existing file entry is there for favicon
                 *  So, need to delete that file from files collection and set organization favicon to null
                 * */
                if (organization.favicon && faviconLogoFiles) {
                    await deleteFile({
                        parent: organization.bucketName!,
                        parentId: organizationId,
                        entity: faviconLogoFiles.entity.toLowerCase(),
                        entityId: organizationId,
                        fileId: faviconLogoFiles._id as string,
                        AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_ASSETS_STORAGE_CONNECTION_STRING
                    });

                    organization.favicon = null;
                }

                // Set favicon as profile pic and set imageAndLogoSame as true also set audit log data
                organization.favicon = organization.profile;
                organization.imageAndLogoSame = true;

                data.favicon = organization.favicon;
            }

            await organization.save();
            await OrganizationUpdatedPublisherWrapper(organization);

            if (failedToUpload.length) {
                throw new SucceededPartially([{
                    parameters: [{
                        attributes: failedToUpload,
                        message: "The files failed to upload."
                    }]
                }], "One or more files failed to update.");
            }
            const modifiedProperties = AuditLog.prepareModifiedProperties({
                data,
                oldData,
                req,
                flexibleFieldsNameKey: [],
                target: TargetType.ORGANIZATION
            });

            res.sendResponse({
                meta: {
                    message: "Files uploaded successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: organizationId,
                            name: organization.name
                        }
                    }
                ],
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authentication.UploadOrganizationProfileImage");
            console.error(error);
            next(error);
        }
    });

export { router as uplaodLogoAndFavicon };
