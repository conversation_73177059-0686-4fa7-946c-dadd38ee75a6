import {
    currentUser,
    hasG<PERSON><PERSON>Action,
    InsufficientPrivilagesError,
    requireA<PERSON>,
    responseHandler,
    validateRequest
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { FilterQuery, GetAllUsersResponse, SortQuery } from "../../interfaces";

import { User } from "../../models/user";
import { UserFlexibleField } from "../../models/user-flexible-fields";
import { fetchDefaultSortObject, hasWhiteSpace } from "../../util";
import { getAllUsersValidation } from "./get-all-users.validation";
import { authenticationFields } from "../../util/authentication-fields";
import {
    fetchFiltersFromMultipleSectionsBasedOnPermission
} from "../../util/fetch-filters-from-multiple-sections-based-on-permission";
import {
    fetchSortFromMultipleSectionsBasedOnPermission
} from "../../util/fetch-sort-from-multiple-sections-based-on-permission";
import { UserFlexibleFields } from "../../enums/user-flexible-field.enum";

const router = express.Router();

router.get(
    "/v1/users",
    responseHandler,
    currentUser,
    requireAuth,
    getAllUsersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction):Promise<any> => {
        try {
            // Check user has permission to read all users
            const hasPermission = await hasGlobalAction(req, "ListUsers");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const defaultLimit = 100;
            const searchQuery: { keys?: string | { $in: string[] } } = {};

            const { search, page = 1, limit = defaultLimit, filter, sort  } = req.query;

            const skip = (page as number - 1) * (limit as number);

            // Step 1: Make search query
            if (search) {
                const searchTerm = search as string;
                searchQuery.keys = hasWhiteSpace(searchTerm) ? { $in: searchTerm.split(" ") } : searchTerm;
            }

            // Define sections and assigned actions set
            const sections = [
                {
                    path: authenticationFields.users
                },
                {
                    path: authenticationFields.modifiedAt
                }
            ];

            const sortQuery: SortQuery = fetchDefaultSortObject(sort as string);
            const filtersQuery: FilterQuery = {};

            // If filter is provided then prepare filter query
            if(filter) {
                const filters = fetchFiltersFromMultipleSectionsBasedOnPermission({
                    filter: filter as string,
                    sections
                });
                Object.assign(filtersQuery, filters);

                if(filtersQuery["organization"]) {
                    filtersQuery["organization._id"] = filtersQuery["organization"];
                    delete filtersQuery["organization"];
                }

                if(filtersQuery["organizationTypes"]) {
                    filtersQuery["organizationTypes._id"] = filtersQuery["organizationTypes"];
                    delete filtersQuery["organizationTypes"];
                }
            }

            // If sort is provided then prepare sort query
            if(sort) {
                const sortObj = fetchSortFromMultipleSectionsBasedOnPermission({
                    sort: sort as string,
                    sections
                });
                Object.assign(sortQuery, sortObj);
            }

            // Step 3: Fetch all organizations
            let aggregateQuery:any = [
                { $match: { ...searchQuery } },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "organizationId",
                        foreignField: "_id",
                        as: "organization"
                    }
                },
                {
                    $unwind: {
                        path: "$organization"
                    }
                },
                {
                    $addFields: {
                        isOwner: { $cond: [{ $in: ["$azureId", "$organization.owner"] }, true, false] }
                    }
                },
                {
                    $lookup: {
                        from: "organizationtypes",
                        localField: "organization.organizationTypeIds",
                        foreignField: "_id",
                        as: "organizationTypes"
                    }
                },
                { $match: { ...filtersQuery } },

            ];

            if(Object.keys(sortQuery).length) {
                aggregateQuery.push({ $sort: sortQuery });
            }

            aggregateQuery = aggregateQuery.concat([
                {
                    $project: {
                        "_id": 1,
                        "firstName": 1,
                        "lastName": 1,
                        "displayName": 1,
                        "name": 1,
                        "email": 1,
                        "organization._id": 1,
                        "organization.name": 1,
                        "organizationTypes": 1,
                        "role": 1,
                        "isOwner": 1,
                        "jobTitle": 1,
                        "companyName": 1,
                        "isEnabled": 1,
                        "officePhone": 1,
                        "alternateEmail": 1,
                        "userLocation": 1,
                        "createdAt": 1,
                        "updatedAt": 1,
                    }
                },
                {
                    $facet: { totalRows: [{ $count: "count" }], data: [{ $skip: skip }, { $limit: (limit as number) }] }
                }
            ]);

            const usersDetails: {totalRows: {count: number}[], data: GetAllUsersResponse[]}[] = await User.aggregate(aggregateQuery, {
                collation: {
                    locale: "en",
                    numericOrdering: true
                }
            });

            if(!usersDetails || !usersDetails.length || !usersDetails[0].totalRows ||
                !usersDetails[0].totalRows.length || !usersDetails[0].totalRows[0].count || !usersDetails[0].data) {
                return res.json({
                    totalRows: 0,
                    data: []
                });
            }

            const usersCount = usersDetails[0].totalRows[0].count;
            const users = usersDetails[0].data;

            const roles = await UserFlexibleField.findOne({ "key": UserFlexibleFields.Role }, { values: 1 });
            const rolesMap: Map<string, string> = new Map();
            if(roles) {
                roles.values.forEach(value => {
                    rolesMap.set(String(value._id), value.value);
                });
            }

            const usersInfo: GetAllUsersResponse[] = await Promise.all(users.map(async (userInfo) => {
                // Format the organization
                if (userInfo.organization) {
                    userInfo.organization = {
                        id: String(userInfo.organization._id),
                        name: userInfo.organization.name,
                        organizationTypes: (userInfo.organizationTypes || []).map(type => ({
                            id: String(type._id),
                            name: type.name
                        }))
                    };
                }

                // Replace role with its value
                if (userInfo.role) {
                    userInfo.role = {
                        id: String(userInfo.role),
                        value: rolesMap.get(String(userInfo.role)) || ""
                    };
                }
                else {
                    userInfo.role = null;
                }

                userInfo.id = String(userInfo._id);

                delete userInfo._id;
                delete userInfo.organizationTypes;

                return userInfo;
            }));

            // Step 3: Send response
            res.sendResponse({
                totalRows: usersCount,
                data: usersInfo
            }, {});
        }
        catch (error) {
            console.error("Authentication.GetAllUsers");
            console.error(error);
            next(error);
        }
    }
);

export { router as getAllUsersRouter };
