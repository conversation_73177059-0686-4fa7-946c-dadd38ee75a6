/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    ExternalServerError,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    OwnerMustExistBadRequestError,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    SucceededPartially, TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { User, UserDoc } from "../../models/user";
import { Organization } from "../../models/organization";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";
import { removeOrganizationOwnersValidation } from "./remove-organization-owners.validation";
import { getUserName } from "../../util";

const router = express.Router();

router.delete("/v1/organizations/:organizationId/owners",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    removeOrganizationOwnersValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to remove owners from organization
            const hasPermission = await hasGlobalAction(req, "RemoveOwnersOfOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { owners }: { owners: string[] } = req.body;
            const { organizationId } = req.params;

            if (owners.includes(req.currentUser?.id!)) {
                throw new InvalidActionError("You cannot remove yourself from the organization.");
            }

            // Step 1: Find organization and if it's not present then throw NotFoundError
            const organization = await Organization.findById(organizationId);

            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 2: Fetch user details from user ids, if any id is not valid throw new error
            const ownersDetails = await User.find({
                _id: { $in: owners }
            });

            if (!ownersDetails || (ownersDetails && !ownersDetails.length)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Prepare user details map
            const userDetailsMap = new Map();
            const userIds: string[] = [];

            ownersDetails.forEach(user => {
                userIds.push(String(user._id));

                userDetailsMap.set(String(user.azureId), {
                    id: String(user._id),
                    name: getUserName({
                        firstName: user.firstName,
                        lastName: user.lastName,
                        displayName: user.displayName,
                    }),
                    email: user.email,
                    azureId: user.azureId
                });
            });

            const validUsers: UserDoc[] = [];
            const inValidUserIds: string[] = [];

            owners.forEach(userId => {
                if (userIds.includes(userId)) {
                    validUsers.push(ownersDetails.find(user => user.id === userId)!);
                }
                else {
                    inValidUserIds.push(userId);
                }
            });

            // Make azure Ids
            const ownerAzureIds = validUsers.map(owner => {
                return owner.azureId;
            });

            // Step 3: Check whether this owners are there as owners in that org
            const org = await Organization.findOne({
                _id: organizationId,
                owner: { $all: ownerAzureIds }
            }).lean().exec();

            if (!org) {
                // throw new BadRequestError("All the owners provided are not in organization");
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            if (organization.owner.length - ownerAzureIds.length <= 0) {
                throw new OwnerMustExistBadRequestError();
                // throw new BadRequestError("Organization must have at least one owner after removing specified owners");
            }

            // Step 3: Get the authorization token
            const token = await microsoftGraphAPI.getAccessToken();

            // Step 4 : Remove owners from organization in azure
            const removeUsersResponse = await microsoftGraphAPI.removeOwnersFromOrganization({ token, orgId: organization.azureId, owners: ownerAzureIds });

            // Step 5: Filter out  removed owners azureIds and not removed owners
            const removedOwnersAzureIds: string[] = [];
            const notRemovedOwners: any = [];

            ownersDetails.forEach(user => {
                if (removeUsersResponse[user.azureId].status) {
                    removedOwnersAzureIds.push(user.azureId);
                }
                else {
                    notRemovedOwners.push(removeUsersResponse[user.azureId].body?.error);
                }
            });

            if (notRemovedOwners.length === owners.length) {
                throw new ExternalServerError(notRemovedOwners);
            }

            // Step 6: Remove owners from organization
            organization.owner = organization.owner.filter(owner => {
                return !removedOwnersAzureIds.includes(owner);
            });

            await organization.save();
            await OrganizationUpdatedPublisherWrapper(organization);

            await Promise.all(removedOwnersAzureIds.map(async azureId => {
                const user = await User.getUserByAzureId({ azureId });
                if (user) {
                    // user.organizationId = null;
                    // await user.save();
                    // await userUpdatedPublisherWrapper(user);
                    // const data = {
                    //     id: user.id
                    // };
                    await user.deleteOne();
                    // await userDeletedPublisherWrapper(data);
                }
            }));

            // Step 7: Send Response
            const errors = [];
            if (inValidUserIds.length) {
                errors.push({
                    attributes: inValidUserIds,
                    message: "Users do not exist."
                });
            }

            if (notRemovedOwners.length) {
                notRemovedOwners.forEach((err: any) => {
                    errors.push(err);
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more owners are failed to remove from the specified organization.");
            }

            res.sendResponse({
                meta: {
                    message: "Organization owners removed successfully"
                }
            }, {
                targets: removedOwnersAzureIds.map(azureId => {
                    const user = userDetailsMap.get(azureId);

                    return {
                        type: TargetType.USER,
                        details: user || {}
                    };
                }),
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId
            });
        }
        catch (error) {
            console.error("Authentication.RemoveOrganizationOwners");
            console.error(error);
            next(error);
        }
    });

export { router as removeOrganizationOwnersRouter };
