/* eslint-disable max-statements */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    generateSearch<PERSON><PERSON>s,
    InsufficientPrivilagesError,
    InvalidActionError,
    InvalidResourceIdBadRequestError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";

import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { UpdateUserDetailsParams } from "../../interfaces";
import { User, UserDoc } from "../../models/user";
import { getSearchFields, getUserName, intersectTwoObjects, isMOXFIVEUser, pickFromObject } from "../../util";
import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
import { OrganizationDoc } from "../../models/organization";
import { updateUserDetailsValidation } from "./update-user-details.validation";
import { UserFlexibleField } from "../../models/user-flexible-fields";
import { AuditLog } from "../../services/audit-log";

const router = express.Router();

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace Express {
        interface Request {
            organization?: OrganizationDoc,
            user?: UserDoc
        }
    }
}

router.put(
    "/v1/users/:userId",
    responseHandler,
    currentUser,
    requireAuth,
    updateUserDetailsValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "UpdateUserDetails";

            const { userId } = req.params;
            const isMoxfiveUser = isMOXFIVEUser({
                req,
                throwError: false
            });

            // Insufficient privilages
            if (req.currentUser?.id !== userId) {
                throw new InsufficientPrivilagesError();
            }

            if (req.body.hasOwnProperty("role") && !isMoxfiveUser) {
                throw new InsufficientPrivilagesError();
            }

            // if (req.body.hasOwnProperty("jobTitle") || req.body.hasOwnProperty("companyName")) {
            //     await canUpdateUserSpecificField(req.currentUser?.organizationId!, req.currentUser?.id!);
            // }

            const user = await User.getUserById({ id: userId });
            if (!user) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "User not found.");
            }

            // Check if provided user is moxfive user, if it is then throw error
            const isProvidedUserMoxfiveUser = isMOXFIVEUser({
                req,
                throwError: false,
                organizationId: String(user.organizationId)
            });

            if(isProvidedUserMoxfiveUser) {
                throw new InvalidActionError("This operation is not allowed.");
            }

            const userData: UpdateUserDetailsParams = pickFromObject(
                user,
                ["firstName", "lastName", "displayName", "userLocation", "officePhone", "jobTitle", "role"]
            );

            const updatedData: UpdateUserDetailsParams = intersectTwoObjects(userData, req.body);

            const roleValues = {
                newValue: "",
                oldValue: "",
                roleId: ""
            };

            // If updated data has role property
            if (updatedData.hasOwnProperty("role")) {
                // Fetch role flexible field
                const isFieldExist = await UserFlexibleField.findById(updatedData.role!.id);
                if (!isFieldExist) {
                    throw new InvalidResourceIdBadRequestError([{ name: "role.id", value: updatedData.role!.id, message: "Role flexible field id not found." }]);
                }
                roleValues.roleId = updatedData.role?.id || "";

                // If role valueIds are provided
                if(updatedData.role?.valueIds.length) {
                    // If already assigned role is not same as provided role
                    if((String(user.role) !== String(updatedData.role!.valueIds[0]))) {
                        // Check field value exist
                        const isFieldValueExist = isFieldExist.values.find(value => String(value.id) === String(updatedData.role!.valueIds[0]));
                        // eslint-disable-next-line max-depth
                        if (!isFieldValueExist) {
                            throw new InvalidResourceIdBadRequestError([{ name: "role.valueIds", value: updatedData.role!.valueIds, message: "Value is invalid." }]);
                        }

                        user.role = updatedData.role!.valueIds[0];
                        updatedData.roleId = updatedData.role!.valueIds[0];

                        roleValues.newValue = isFieldValueExist.value;
                    }
                    else {
                        delete updatedData.role;
                    }
                }
                // If user clears the role field
                else {
                    user.role = null;
                    updatedData.roleId = null;
                }

                if(userData.role) {
                    const isFieldValueExist = isFieldExist.values.find(value => String(value.id) === String(userData.role));
                    roleValues.oldValue = isFieldValueExist?.value || "";
                }
            }

            if (Object.keys(updatedData).length === 0) {
                return res.sendResponse({
                    meta: {
                        message: "User updated successfully"
                    }
                }, {});
            }

            const accessToken = await microsoftGraphAPI.getAccessToken();
            await microsoftGraphAPI.updateUserDetails({ accessToken, azureId: user.azureId, ...updatedData });

            // If firstName, lastName or displayName updated then update the name
            if(updatedData.firstName || updatedData.lastName || updatedData.displayName) {
                updatedData.name = getUserName({
                    firstName: updatedData.firstName ?? user.firstName,
                    lastName: updatedData.lastName ?? user.lastName,
                    displayName: updatedData.displayName ?? user.displayName,
                });
            }
            if (updatedData.hasOwnProperty("role")) {
                delete updatedData.role;
            }
            Object.assign(user, updatedData);

            if (updatedData.firstName || updatedData.lastName || updatedData.displayName) {
                const searchFields = getSearchFields(
                    updatedData.firstName || user.firstName,
                    updatedData.lastName || user.lastName,
                    updatedData.displayName || user.displayName,
                    user.email
                );
                const searchKeys = generateSearchKeys(searchFields);
                user.keys = searchKeys as string[];
            }

            await user.save();
            await userUpdatedPublisherWrapper(user);

            // Fetch modified properties for audit log
            const data: any = { ...updatedData };
            const oldData: any = { ...userData };

            if(data.hasOwnProperty("roleId")) {
                data.role = data.roleId ? [
                    {
                        id: roleValues.roleId,
                        value: roleValues.newValue
                    }
                ] : [];

                delete data.roleId;

                if(oldData.role) {
                    oldData.role = [
                        {
                            id: String(oldData.role),
                            value: roleValues.oldValue
                        }
                    ];
                }
            }

            const modifiedProperties = AuditLog.prepareModifiedProperties({
                data,
                flexibleFieldsNameKey: [],
                req,
                oldData,
                target: TargetType.USER
            });

            res.sendResponse({
                meta: {
                    message: "User updated successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.USER,
                        details: {
                            id: String(user.id),
                            name: getUserName({
                                firstName: user.firstName,
                                lastName: user.lastName,
                                displayName: user.displayName
                            }),
                            email: user.email,
                            azureId: user.azureId
                        }
                    }
                ],
                correlation: TargetType.ORGANIZATION,
                correlationId: String(user.organizationId),
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authentication.UpdateUserDetails");
            console.error(error);
            next(error);
        }
    }
);

export { router as updateUserDetailsRouter };
