import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    requireAuth,
    response<PERSON>andler,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { Organization, OrganizationAttrs } from "../../models/organization";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { OrganizationFields } from "../../services/organization-fields";
import { OrganizationType } from "../../models/organization-type";
import {
    CreateOrganizationAzureData,
    CreateOrganizationAzureResponse,
    CreateUpdateOrganizationRequestData
} from "../../interfaces";
import { getUsersByIds, pickFromObject, sanitizeEmail, sanitizeURI } from "../../util";
import { createOrganizationValidation } from "./create-organization.validation";
import { OrganizationCreatedPublisherWrapper } from "../../util/organization-created-publisher-wrapper";
import { AuditLog } from "../../services/audit-log";
import { authenticationFields } from "../../util/authentication-fields";
import { flexibleFieldValidation } from "../../util/flexible-field-validation-middleware";
import { MakeSingleSectionFieldsFlexibleFields } from "../../util/make-single-section-fields-flexible-fields";

const router = express.Router();

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      organizationFields?: Set<string>;
      organizationFlexibleFields?: {
        mandatory: Set<string>,
        validateFields: Set<string>
      }
    }
  }
}

const { flexibleFieldsNameKey } = MakeSingleSectionFieldsFlexibleFields(authenticationFields.organizations);

router.post("/v1/organizations",
    responseHandler,
    currentUser,
    requireAuth,
    OrganizationFields.checkOrganizationTypesMiddleware,
    createOrganizationValidation,
    validateRequest,
    flexibleFieldValidation(flexibleFieldsNameKey),
    OrganizationFields.moxfiveSponsorsMiddleware,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to create organization
            const hasPermission = await hasGlobalAction(req, "CreateOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            // Destructure the request body
            const { name, description, organizationTypes: organizationTypeIds }: CreateUpdateOrganizationRequestData = req.body;

            // Step 1: Check organization type is valid or not, if not then throw BadReqestError
            const organizationTypes = await OrganizationType.find({ _id: { $in: organizationTypeIds } }).select("name");

            const orgTypesMap: Map<string, string> = new Map();
            organizationTypes.forEach(type => {
                orgTypesMap.set(String(type._id), type.name);
            });

            // Step 2: Get the authorization token
            const token = await microsoftGraphAPI.getAccessToken();

            // Step 3: Create Org in Microsoft Azure
            const organizationData: CreateOrganizationAzureData = {
                displayName: name as string,
                description: description
            };

            const createOrgResponse: CreateOrganizationAzureResponse = await microsoftGraphAPI.createOrganization({ token, data: organizationData });
            const orgAzureId = createOrgResponse.id;

            // Step 4: Add Org to Microsoft Azure Enterprise application
            await microsoftGraphAPI.addOrganizationInApplication({ token, orgId: orgAzureId });

            // Step 5: Create Org in DB
            const orgDetails = pickFromObject(req.body, Array.from(req.organizationFields!));

            // unSanitized values
            const unSanitizedValues = {
                website: orgDetails.website,
                hotlineEmail: orgDetails.hotlineEmail,
                billingContactEmail: orgDetails.billingContactEmail,
            };

            // Sanitize values
            if (orgDetails.website) {
                orgDetails.website = sanitizeURI(orgDetails.website);
            }
            if (orgDetails.hotlineEmail) {
                orgDetails.hotlineEmail = sanitizeEmail(orgDetails.hotlineEmail);
            }
            if (orgDetails.billingContactEmail) {
                orgDetails.billingContactEmail = sanitizeEmail(orgDetails.billingContactEmail);
            }
            // CASE: This name may only contain lowercase letters, numbers, and hyphens, and must begin with a letter or a number. Each hyphen must be preceded and followed by a non-hyphen character. The name must also be between 3 and 63 characters long.
            // For this case replacing all special characters with blank string and if length is less than 3 then making name of length 3
            let bucketName = orgDetails.name.replace(/[^\w]/gi, "").slice(0, 62).toLowerCase() ?? "default";
            if (bucketName && bucketName.length < 3) {
                bucketName = `${bucketName}-${bucketName}`;
            }

            const orgData: OrganizationAttrs = {
                ...orgDetails,
                bucketName: bucketName,
                azureId: orgAzureId,
                organizationTypeIds: organizationTypeIds
            };

            const organization = Organization.build(orgData);
            await organization.save();
            await OrganizationCreatedPublisherWrapper(organization);

            // Step 6: Prepare data for audit log
            const data = { ...orgDetails };

            // Populate un-sanitized values
            if (data.website) {
                data.website = unSanitizedValues.website;
            }
            if (data.hotlineEmail) {
                data.hotlineEmail = unSanitizedValues.hotlineEmail;
            }
            if (data.billingContactEmail) {
                data.billingContactEmail = unSanitizedValues.billingContactEmail;
            }

            // Populate organization type ids
            data.organizationTypeIds = organizationTypeIds.map((type: string) => {
                return {
                    id: String(type),
                    name: orgTypesMap.get(String(type)) || ""
                };
            });

            // Populate MOXFIVE PM, TA and Sales sponsors
            if(data.moxfivePMSponsor || data.moxfiveTASponsor || data.moxfiveSalesSponsor) {
                const userIds:string[] = [];
                if(data.moxfivePMSponsor) {
                    userIds.push(data.moxfivePMSponsor);
                }
                if(data.moxfiveTASponsor) {
                    userIds.push(data.moxfiveTASponsor);
                }
                if(data.moxfiveSalesSponsor) {
                    userIds.push(data.moxfiveSalesSponsor);
                }

                const usersMap = await getUsersByIds(userIds);
                if(data.moxfivePMSponsor) {
                    data.moxfivePMSponsor = {
                        id: String(data.moxfivePMSponsor),
                        name: usersMap.get(String(data.moxfivePMSponsor)) || ""
                    };
                }
                if(data.moxfiveTASponsor) {
                    data.moxfiveTASponsor = {
                        id: String(data.moxfiveTASponsor),
                        name: usersMap.get(String(data.moxfiveTASponsor)) || ""
                    };
                }
                if(data.moxfiveSalesSponsor) {
                    data.moxfiveSalesSponsor = {
                        id: String(data.moxfiveSalesSponsor),
                        name: usersMap.get(String(data.moxfiveSalesSponsor)) || ""
                    };
                }
            }

            const modifiedProperties = AuditLog.prepareModifiedProperties({
                data,
                req,
                flexibleFieldsNameKey: [{
                    name: "industry",
                    key: ""
                }, {
                    name: "partnerType",
                    key: ""
                }, {
                    name: "coverageStates",
                    key: ""
                }, {
                    name: "offerings",
                    key: ""
                }, {
                    name: "languages",
                    key: ""
                }, {
                    name: "serviceLines",
                    key: ""
                }],
                target: TargetType.ORGANIZATION
            });

            res.sendResponse({
                id: String(organization._id),
                meta: {
                    message: "Organization created successfully."
                }
            }, {
                targets: [
                    {
                        type: TargetType.ORGANIZATION,
                        details: {
                            id: String(organization._id),
                            name: organization.name
                        }
                    }
                ],
                modifiedProperties
            });
        }
        catch (error) {
            console.error("Authentication.CreateOrganization");
            console.error(error);
            next(error);
        }
    });

export { router as createOrganizationRouter };
