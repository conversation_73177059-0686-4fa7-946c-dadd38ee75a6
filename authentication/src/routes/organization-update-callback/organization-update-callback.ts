import express, { NextFunction, Request, Response } from "express";
import { Organization } from "../../models/organization";
import {
    OrganizationAzureUpdates,
    OrganizationAzureUpdatesDoc,
} from "../../models/organization-azure-updates";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { OrganizationUpdatedPublisherWrapper } from "../../util/organization-updated-publisher-wrapper";

interface orgDetailsObj {
  data: {
    displayName: string;
  };
}

const router = express.Router();

router.post(
    "/v1/authentication/groupupdate/callback",
    async (req: Request, res: Response, next: NextFunction):Promise<any> => {
        try {
            req.action = "AzureGroupsUpdatesWebhook";

            const { validationToken } = req.query;
            const { value } = req.body;

            if (validationToken) {
                return res.set("content-type", "text/plain").send(validationToken);
            }

            if (Array.isArray(value) && value.length) {
                // Send 202 (Accepted) response to acknowledge microsoft azure
                res.status(202).send();

                // Prepare the data and save it in the db
                const preparedObj: any = [];
                value.map((item: any) => {
                    if (
                        !process.env.CLIENT_STATE || item.clientState === process.env.CLIENT_STATE
                    ) {
                        preparedObj.push({
                            organizationId: item?.resourceData["id"],
                            changeType: item?.changeType,
                            status: "Not Started",
                        });
                    }
                });

                await OrganizationAzureUpdates.insertMany(preparedObj);

                // Once data is inserted, get organization which are yet to be updated
                const notStartedOrganization = await OrganizationAzureUpdates.find({
                    status: "Not Started",
                });
                // Get unique records to process them
                const uniqueRecords: OrganizationAzureUpdatesDoc[] = [];
                notStartedOrganization.forEach((organization) => {
                    if (
                        !uniqueRecords.find(
                            (record: OrganizationAzureUpdatesDoc) =>
                                record.organizationId === organization.organizationId
                        )
                    ) {
                        uniqueRecords.push(organization);
                    }
                });

                // Change the started records to in progress
                await Promise.all(
                    notStartedOrganization.map(async (organization) => {
                        organization.status = "In Progress";
                        await organization.save();
                    })
                );

                await Promise.all(
                    uniqueRecords.map(async (organization) => {
                        try {
                            // Check user exist in our system, if user don't exists then delete the user azure updates colleciton details
                            const oldOrgDetails = await Organization.findOne({
                                azureId: organization.organizationId,
                            });
                            if (!oldOrgDetails) {
                                await OrganizationAzureUpdates.deleteMany({
                                    organizationId: organization.organizationId,
                                    status: "In Progress",
                                });
                                return false;
                            }

                            // If user is valid then update user details
                            const accessToken = await microsoftGraphAPI.getAccessToken();
                            const newOrganizationDetails: orgDetailsObj = await microsoftGraphAPI.getAzureOrganizationDetailsById({
                                accessToken,
                                organizationId: organization.organizationId,
                            });

                            if (
                                newOrganizationDetails.data.displayName && oldOrgDetails.name !== newOrganizationDetails.data.displayName
                            ) {
                                oldOrgDetails.name = newOrganizationDetails.data.displayName;
                                await oldOrgDetails?.save();
                                await OrganizationUpdatedPublisherWrapper(oldOrgDetails!);
                            }

                            // Delete the records after update
                            await OrganizationAzureUpdates.deleteMany({
                                organizationId: organization.organizationId,
                                status: "In Progress",
                            });
                        }
                        catch (err) {
                            if (err instanceof Error) {
                                await OrganizationAzureUpdates.updateMany(
                                    {
                                        organizationId: organization.organizationId,
                                        status: "In Progress",
                                    },
                                    { $set: { errorMessage: err.message, status: "Not Started" } }
                                );
                            }

                            // Send an email notification
                        }
                    })
                );
            }
        }
        catch (error) {
            console.error("Authetication.OrganizationUpdateCallback");
            console.error(error);
            next(error);
        }
    }
);

export { router as organizationUpdateCallbackRouter };
