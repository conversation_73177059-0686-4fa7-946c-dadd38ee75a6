import { currentUser, InvalidTokenError, responseHandler } from "@moxfive-llc/common";
import express, { NextFunction, Request, Response } from "express";
import { GetUserDetailsResponse, VerifyCodeResponse } from "../../interfaces";
import { jsonWebToken } from "../../services/json-web-token";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { User } from "../../models/user";
import { Organization } from "../../models/organization";
import app from "../../app";

const router = express.Router();

router.get(
    "/v1/refreshToken",
    responseHandler,
    currentUser,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            req.action = "RefreshToken";

            const signedRefreshToken = req.signedCookies.Token2;
            if (!signedRefreshToken) {
                console.error("!signedRefreshToken");
                throw new InvalidTokenError();
            }

            const data = jsonWebToken.getRefreshTokenDetails({ refreshToken: signedRefreshToken });
            const refreshTokenResp: VerifyCodeResponse = await microsoftGraphAPI.refreshToken({ refreshToken: data.refreshToken, pkceEnabled: data.pkceEnabled });
            const userAzureDetails: GetUserDetailsResponse = await microsoftGraphAPI.getUserDetails({ accessToken: refreshTokenResp.data.access_token });

            const userDetails = await User.getUserByAzureId({ azureId: userAzureDetails.data.id });
            if (!userDetails || (userDetails && !userDetails.isEnabled)) {
                console.error("user details missing");
                throw new InvalidTokenError();
            }

            const orgDetails = await Organization.findUserAlreadyInAnyOrganization([userAzureDetails.data.id]);
            if (!orgDetails || (orgDetails && !orgDetails.isEnabled)) {
                console.error("org missing");
                throw new InvalidTokenError();
            }

            const { accessToken } = await jsonWebToken.getAccessToken({
                id: userDetails.id,
                email: userDetails.email,
                organizationId: String(orgDetails._id),
                accessToken: refreshTokenResp.data.access_token
            });
            const { refreshToken } = await jsonWebToken.getRefreshToken({
                refreshToken: refreshTokenResp.data.refresh_token,
                pkceEnabled: data.pkceEnabled
            });

            res.cookie("Token1", accessToken, app.locals.cookieOptions);
            res.cookie("Token2", refreshToken, app.locals.cookieOptions);

            res.sendResponse({
                meta: {
                    message: "Access token has been refreshed successfully."
                }
            }, {});
        }
        catch (error) {
            console.error("Authentication.RefreshToken");
            console.error(error);
            next(error);
        }
    }
);
export { router as refreshTokenRouter };
