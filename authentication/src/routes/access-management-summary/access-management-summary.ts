import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    requireAuth,
    responseHand<PERSON>
} from "@moxfive-llc/common";
import express, { Request, Response, NextFunction } from "express";
import { Organization } from "../../models/organization";
import { Policy } from "../../models/policy";
import { User } from "../../models/user";

const router = express.Router();

router.get(
    "/v1/am/summary",
    responseH<PERSON><PERSON>,
    currentUser,
    requireAuth,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check if user has a ability to get access management summary
            const hasPermission = await hasGlobalAction(req, "GetAccessManagementSummary");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const organizationsCount = await Organization.countDocuments();
            const usersCount = await User.countDocuments();
            const policiesCount = await Policy.countDocuments();

            res.sendResponse({
                organizations: organizationsCount,
                users: usersCount,
                policies: policiesCount
            }, {});
        }
        catch(error) {
            next(error);
        }
    }
);

export { router as accessManagementSummaryRouter };
