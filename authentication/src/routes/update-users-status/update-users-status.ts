/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    ExternalServerError,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    SucceededPartially,
    TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { User, UserDoc } from "../../models/user";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
import { UpdateUsersStatusValidation } from "./update-users-status.validation";
import { getUserName } from "../../util";

const router = express.Router();

router.put("/v1/users/status",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    UpdateUsersStatusValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { isEnabled, users: userIds }: { isEnabled: boolean, users: string[] } = req.body;

            // Step 1: Check user has permission to update users status
            const hasPermission = await hasGlobalAction(req, "UpdateUsersStatus");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            if (userIds.includes(req.currentUser?.id!)) {
                throw new InvalidActionError("You cannot change status of your own account.");
            }

            // Step 2: If any user is not found then throw NotFoundError
            const users = await User.find({ _id: { $in: userIds } });
            if (!users || (users && !users.length)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Prepare user details map
            const userDetailsMap = new Map();
            users.forEach(user => {
                userDetailsMap.set(String(user._id), {
                    id: String(user._id),
                    name: getUserName({
                        firstName: user.firstName,
                        lastName: user.lastName,
                        displayName: user.displayName,
                    }),
                    email: user.email,
                    azureId: user.azureId
                });
            });

            const validUsers: UserDoc[] = [];
            const inValidUserIds: string[] = [];
            const usersIds = users.map(user => user.id);

            userIds.forEach(userId => {
                if (usersIds.includes(userId)) {
                    validUsers.push(users.find(user => user.id === userId)!);
                }
                else {
                    inValidUserIds.push(userId);
                }
            });

            // Step 3: Check which users needs to be updated
            const usersToChange = validUsers.filter(user => {
                return user.isEnabled !== isEnabled;
            });

            const userAzureIds = usersToChange.map(user => user.azureId);

            const updatedUsers: UserDoc[] = [];
            const failedUsers: any = [];

            if (userAzureIds.length) {

                // Step 4: Update user status in azure
                const accessToken = await microsoftGraphAPI.getAccessToken();
                const updateStatusResponse = await microsoftGraphAPI.updateMultipleUsersStatus({ accessToken, users: userAzureIds, status: isEnabled });

                // Step 5: Filter out which user's status updated and which didn't
                usersToChange.forEach(user => {
                    if (updateStatusResponse[user.azureId].status) {
                        updatedUsers.push(user);
                    }
                    else {
                        failedUsers.push(updateStatusResponse[user.azureId].body?.error);
                    }
                });

                // Step 6: If no user is updated then throw internal server error
                if (failedUsers.length === userIds.length) {
                    throw new ExternalServerError(failedUsers);
                }

                // Step 7: Update users status
                await Promise.all(updatedUsers.map(async user => {
                    user.isEnabled = isEnabled;
                    await user.save();
                    await userUpdatedPublisherWrapper(user);
                }));
            }

            // Step 8: If partial users got updated then throw error with email ids
            const errors = [];
            if (inValidUserIds.length) {
                errors.push({
                    attributes: inValidUserIds,
                    message: "Users do not exist."
                });
            }

            if (failedUsers.length) {
                failedUsers.forEach((failure: any) => errors.push(failure));
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users status failed to update.");
            }

            // Step 9: Send Response
            res.sendResponse({
                meta: {
                    message: "Users status updated successfully"
                }
            }, updatedUsers.length ? {
                targets: updatedUsers.map(user => {
                    const userDetails = userDetailsMap.get(user.id);
                    return {
                        type: TargetType.USER,
                        details: userDetails || {}
                    };
                }),
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "isEnabled",
                    oldValue: JSON.stringify(!isEnabled),
                    newValue: JSON.stringify(isEnabled)
                }]
            } : {});
        }
        catch (error) {
            next(error);
        }
    });

export { router as updateUsersStatusRouter };
