/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    ExternalServerError,
    hasGlobalAction,
    InsufficientPrivilagesError,
    InvalidActionError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    response<PERSON><PERSON><PERSON>,
    SucceededPartially, TargetType,
    validateRequest
} from "@moxfive-llc/common";
import { User, UserDoc } from "../../models/user";
import { microsoftGraphAPI } from "../../services/microsoft-graph-api";
import { Organization } from "../../models/organization";
import { userUpdatedPublisherWrapper } from "../../util/user-updated-publisher-wrapper";
import { updateOrganizationSpecificUserStatusValidation } from "./update-organization-users-status.validation";
import { getUserName } from "../../util";

const router = express.Router();

router.put("/v1/organizations/:organizationId/users/status",
    response<PERSON><PERSON><PERSON>,
    currentUser,
    requireAuth,
    updateOrganizationSpecificUserStatusValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to update user status of organization
            const hasPermission = await hasGlobalAction(req, "UpdateUsersStatusOfAnOrganization");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { isEnabled, users: userIds }: { isEnabled: boolean, users: string[] } = req.body;
            const { organizationId } = req.params;

            // Step 1: If organization not found then throw NotFoundError
            const organization = await Organization.findById(organizationId).lean().exec();
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            if (userIds.includes(req.currentUser?.id!)) {
                throw new InvalidActionError("You cannot change status of your own account");
            }

            // Step 2: If any user is not found then throw NotFoundError
            const users = await User.find({ _id: { $in: userIds } });
            if (!users || (users && !users.length)) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            // Prepare user details map
            const userDetailsMap = new Map();
            users.forEach(user => {
                userDetailsMap.set(String(user._id), {
                    id: String(user._id),
                    name: getUserName({
                        firstName: user.firstName,
                        lastName: user.lastName,
                        displayName: user.displayName,
                    }),
                    email: user.email,
                    azureId: user.azureId
                });
            });

            // Step 3: Check user has been in the organization or not and filter out users whose status is not same as provided
            const inValidUsers: string[] = [];
            const validUsers = users.filter(user => {
                const isMember = organization.owner.includes(user.azureId) || organization.member.includes(user.azureId);
                if (!isMember) {
                    inValidUsers.push(user.id);
                }
                if (user.isEnabled !== isEnabled) {
                    return user;
                }
            });

            if (inValidUsers.length === userIds.length) {
                throw new ResourceNotFoundError(NotFoundCode.USER_NOT_FOUND, "Users not found.");
            }

            const updatedUsers: UserDoc[] = [];
            const failedUsers: any = [];

            if (validUsers.length > 0) {
                const validUsersAzureIds = validUsers.map(user => user.azureId);

                // Step 4: Update user status in azure
                const accessToken = await microsoftGraphAPI.getAccessToken();
                const updateStatusResponse = await microsoftGraphAPI.updateMultipleUsersStatus({ accessToken, users: validUsersAzureIds, status: isEnabled });

                // Step 5: Filter out which user's status updated and which didn't
                validUsers.forEach(user => {
                    if (updateStatusResponse[user.azureId].status) {
                        updatedUsers.push(user);
                    }
                    else {
                        failedUsers.push(updateStatusResponse[user.azureId].body?.error);
                    }
                });

                // Step 6: If no user is updated then throw internal server error
                if (failedUsers.length === userIds.length) {
                    throw new ExternalServerError(failedUsers);
                }

                // Step 7: Update users status
                await Promise.all(updatedUsers.map(async user => {
                    user.isEnabled = isEnabled;
                    await user.save();
                    await userUpdatedPublisherWrapper(user);
                }));
            }

            // Step 7: If partial users got updated then throw error with email ids
            const errors = [];
            if (inValidUsers.length) {
                errors.push({
                    attributes: inValidUsers,
                    message: "Users do not exist."
                });
            }

            if (failedUsers.length) {
                failedUsers.forEach((err: any) => {
                    errors.push(err);
                });
            }

            if (errors.length) {
                throw new SucceededPartially([{
                    parameters: errors
                }], "One or more users status failed to update.");
            }

            // Step 8: Send Response
            res.sendResponse({
                meta: {
                    message: "Users status updated successfully"
                }
            }, updatedUsers.length ? {
                targets: updatedUsers.map(user => {
                    const userDetails = userDetailsMap.get(user.id);
                    return {
                        type: TargetType.USER,
                        details: userDetails || {}
                    };
                }),
                correlation: TargetType.ORGANIZATION,
                correlationId: organizationId,
                modifiedProperties: [{
                    target: TargetType.USER,
                    propertyName: "isEnabled",
                    oldValue: JSON.stringify(!isEnabled),
                    newValue: JSON.stringify(isEnabled)
                }]
            } : {});
        }
        catch (error) {
            console.error("Authentication.UpdateOrganizationSpecificUserStatus");
            console.error(error);
            next(error);
        }
    });

export { router as updateOrganizationUsersStatusRouter };
