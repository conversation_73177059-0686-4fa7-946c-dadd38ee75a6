import express, { NextFunction, Request, Response } from "express";
import {
    currentUser,
    hasGlobalAction,
    InsufficientPrivilagesError,
    NotFoundCode,
    requireAuth,
    ResourceNotFoundError,
    responseHandler,
    validateRequest,
    File
} from "@moxfive-llc/common";

import { OrganizationFlexibleField } from "../../models/organization-flexible-fields";
import { OrganizationFields } from "../../services/organization-fields";
import { Organization } from "../../models/organization";
import { getSpecificOrganizationValidation } from "./get-specific-organization.validation";
import { unSanitizeEmail, unSanitizeURI } from "../../util";

const router = express.Router();

router.get("/v1/organizations/:organizationId",
    responseHand<PERSON>,
    currentUser,
    requireAuth,
    getSpecificOrganizationValidation,
    validateRequest,
    async (req: Request, res: Response, next: NextFunction) => {
        try {
            // Check user has permission to get specific organization details
            const hasPermission = await hasGlobalAction(req, "GetOrganizationDetail");
            if (!hasPermission) {
                throw new InsufficientPrivilagesError();
            }

            const { organizationId } = req.params;

            // Step 1: Find Organization
            const organization = await Organization.findById(organizationId)
                .populate([
                    { path: "organizationTypeIds", select: "name" },
                    { path: "moxfivePMSponsor", select: "firstName lastName displayName email" },
                    { path: "moxfiveTASponsor", select: "firstName lastName displayName email" },
                    { path: "moxfiveSalesSponsor", select: "firstName lastName displayName email" }
                ])
                .select("-azureId -version");

            // Step 2: If organization not found then throw error
            if (!organization) {
                throw new ResourceNotFoundError(NotFoundCode.ORGANIZATION_NOT_FOUND, "Organization not found.");
            }

            // Step 3: Format the response
            let org: any = organization.toJSON();
            org.owners = org.owner?.length;
            org.members = org.member?.length;
            org.organizationTypes = org.organizationTypeIds;

            if (org.website) {
                org.website = unSanitizeURI(org.website);
            }
            if (org.hotlineEmail) {
                org.hotlineEmail = unSanitizeEmail(org.hotlineEmail);
            }
            if (org.billingContactEmail) {
                org.billingContactEmail = unSanitizeEmail(org.billingContactEmail);
            }

            // get all Flexible fields
            const flexibleFieldsList = await OrganizationFlexibleField.aggregate([
                {
                    $unwind: "$values"
                },
                {
                    $project: {
                        id: "$values._id",
                        name: "$values.value",
                        _id: 0
                    }
                }
            ]);

            org = await OrganizationFields.addFlexibleFieldsData(org, flexibleFieldsList);
            // formatting name for profile and favicon
            if (org.profile) {
                const file = await File.findById(org.profile).lean().exec();
                org.profile = {
                    id: org.profile,
                    fileName: file?.originalFileName ?? null
                };
            }
            if (org.favicon) {
                const file = await File.findById(org.favicon).lean().exec();

                org.favicon = {
                    id: org.favicon,
                    fileName: file?.originalFileName ?? null
                };
            }
            delete org.owner;
            delete org.member;
            delete org.organizationTypeIds;

            // Step 4: Send Response
            res.sendResponse(org, {});
        }
        catch (error) {
            console.error("Authentication.GetSpecificOrganization");
            console.error(error);
            next(error);
        }
    });

export { router as getSpecificOrganizationRouter };
