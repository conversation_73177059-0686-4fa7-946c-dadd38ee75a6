import express, { Request, Response } from "express";
import { currentUser, requireAuth, responseHandler } from "@moxfive-llc/common";

const router = express.Router();

router.get(
    "/v1/logout",
    responseHandler,
    currentUser,
    requireAuth,
    async (req: Request, res: Response) => {
        req.action = "LogOut";

        res.clearCookie("Token1");
        res.clearCookie("Token2");
        res.sendResponse({
            meta: {
                message: "User has been logged out successfully!"
            }
        }, {});
    }
);

export { router as logOutRouter };
