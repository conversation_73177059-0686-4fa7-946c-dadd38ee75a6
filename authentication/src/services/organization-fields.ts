/* eslint-disable max-depth */
/* eslint-disable no-await-in-loop */
/* eslint-disable max-len */
import {
    BodyInvalidBadRequestError,
    InvalidResourceIdBadRequestError,
    MissingParametersError,
} from "@moxfive-llc/common";
import { NextFunction, Request, Response } from "express";
import { Meta } from "express-validator";
import { OrganizationType } from "../models/organization-type";
import { isValidMongoObjectId } from "../util";
import { User } from "../models/user";
import { Organization } from "../models/organization";
import { OrganizationFieldsStructure } from "../interfaces";
import { OrganizationV2 } from "../models/v2/oragnizations-v2";
import { UserV2 } from "../models/v2/users-v2";

enum OrganizationTypes {
    MOXFIVE = "MOXFIVE",
    CLIENT = "Client",
    PRIVACY_COUNSEL = "Privacy Counsel",
    INSURANCE_CARRIER = "Insurance Carrier",
    MONITORING_COUNSEL = "Monitoring Counsel",
    FORENSICS = "Forensics",
    RECOVERY = "Recovery",
    NEGOTIATOR = "Negotiator",
    OTHER = "Other",
    INSURANCE_BROKER = "Insurance Broker",
    PAYMENT_FACILITATOR = "Payment Facilitator"
}

export class OrganizationFields {
    static fields: OrganizationFieldsStructure = {
        [OrganizationTypes.MOXFIVE]: [
            { name: "name", isRequired: true },
            { name: "website", isRequired: false },
        ],
        [OrganizationTypes.CLIENT]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
            { name: "highLevelCompanyInformation", isRequired: false },
            { name: "descriptionOfEnvironment", isRequired: false },
            { name: "officeLocations", isRequired: false },
            { name: "numberOfEmployees", isRequired: false },
            { name: "numberOfITStaff", isRequired: false },
            { name: "itStaffLocation", isRequired: false },
            { name: "msaSignatureDate", isRequired: false },
            { name: "billingContactName", isRequired: false },
            { name: "billingContactEmail", isRequired: false },
            { name: "billingContactPhone", isRequired: false },
            { name: "billingAddresses", isRequired: false },
            { name: "serviceLines", isRequired: false },
        ],
        [OrganizationTypes.PRIVACY_COUNSEL]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
        ],
        [OrganizationTypes.INSURANCE_CARRIER]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
            { name: "moxfiveHotline", isRequired: false },
            { name: "hotlineEmail", isRequired: false },
            { name: "hotlinePhoneNumber", isRequired: false },
        ],
        [OrganizationTypes.MONITORING_COUNSEL]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
        ],
        [OrganizationTypes.FORENSICS]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
            { name: "activePartner", isRequired: false },
            { name: "msaSignatureDate", isRequired: false },
            { name: "billingContactName", isRequired: false },
            { name: "billingContactEmail", isRequired: false },
            { name: "billingContactPhone", isRequired: false },
            { name: "billingAddresses", isRequired: false },
            { name: "partnerType", isRequired: false },
            { name: "onboardedDate", isRequired: false },
            { name: "coverageStates", isRequired: false },
            { name: "offerings", isRequired: false },
            { name: "partnerEula", isRequired: false },
            { name: "partnerTermsConditions", isRequired: false },
            { name: "inboundRequestInfo", isRequired: false },
            { name: "moxfivePMSponsor", isRequired: false },
            { name: "moxfiveTASponsor", isRequired: false },
            { name: "moxfiveSalesSponsor", isRequired: false },
            { name: "numberOfPMs", isRequired: false },
            { name: "numberOfLeads", isRequired: false },
            { name: "numberOfEngineers", isRequired: false },
            { name: "languages", isRequired: false },
            { name: "shortDescription", isRequired: false },
        ],
        [OrganizationTypes.RECOVERY]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
            { name: "activePartner", isRequired: false },
            { name: "msaSignatureDate", isRequired: false },
            { name: "billingContactName", isRequired: false },
            { name: "billingContactEmail", isRequired: false },
            { name: "billingContactPhone", isRequired: false },
            { name: "billingAddresses", isRequired: false },
            { name: "partnerType", isRequired: false },
            { name: "onboardedDate", isRequired: false },
            { name: "coverageStates", isRequired: false },
            { name: "offerings", isRequired: false },
            { name: "partnerEula", isRequired: false },
            { name: "partnerTermsConditions", isRequired: false },
            { name: "inboundRequestInfo", isRequired: false },
            { name: "moxfivePMSponsor", isRequired: false },
            { name: "moxfiveTASponsor", isRequired: false },
            { name: "moxfiveSalesSponsor", isRequired: false },
            { name: "numberOfPMs", isRequired: false },
            { name: "numberOfLeads", isRequired: false },
            { name: "numberOfEngineers", isRequired: false },
            { name: "languages", isRequired: false },
            { name: "shortDescription", isRequired: false },
        ],
        [OrganizationTypes.NEGOTIATOR]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
            { name: "activePartner", isRequired: false },
            { name: "msaSignatureDate", isRequired: false },
            { name: "billingContactName", isRequired: false },
            { name: "billingContactEmail", isRequired: false },
            { name: "billingContactPhone", isRequired: false },
            { name: "billingAddresses", isRequired: false },
            { name: "partnerType", isRequired: false },
            { name: "onboardedDate", isRequired: false },
            { name: "coverageStates", isRequired: false },
            { name: "offerings", isRequired: false },
            { name: "partnerEula", isRequired: false },
            { name: "partnerTermsConditions", isRequired: false },
            { name: "inboundRequestInfo", isRequired: false },
            { name: "moxfivePMSponsor", isRequired: false },
            { name: "moxfiveTASponsor", isRequired: false },
            { name: "moxfiveSalesSponsor", isRequired: false },
            { name: "numberOfPMs", isRequired: false },
            { name: "numberOfLeads", isRequired: false },
            { name: "numberOfEngineers", isRequired: false },
            { name: "languages", isRequired: false },
            { name: "shortDescription", isRequired: false },
        ],
        [OrganizationTypes.OTHER]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
        ],
        [OrganizationTypes.INSURANCE_BROKER]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
        ],
        [OrganizationTypes.PAYMENT_FACILITATOR]: [
            { name: "name", isRequired: true },
            { name: "industry", isRequired: false },
            { name: "website", isRequired: false },
        ],
    };

    static addFlexibleFieldsData = async (request: any, flexibleFieldsList: any) => {
        const arrayOfItems: string[] = ["industry", "partnerType", "coverageStates", "offerings", "languages", "serviceLines"];
        const flexibleFieldObject: { [key: string]: { id: string, name: string } } = {};
        flexibleFieldsList.map((element: any) => flexibleFieldObject[element.id.toString()] = { id: element.id.toString(), name: element.name });

        for (const key in request) {
            if (arrayOfItems.indexOf(key) !== -1) {
                const valueIds = request[String(key)];
                request[String(key)] = [];
                if (Array.isArray(valueIds) && valueIds.length > 0) {
                    for (let j = 0; j < valueIds.length; j++) {
                        if (flexibleFieldObject[valueIds[+j].toString()] !== undefined) {
                            request[String(key)].push(flexibleFieldObject[valueIds[+j].toString()]);
                        }
                    }
                }
            }
        }

        return request;
    };

    private currentFields: Set<string>;

    constructor(organizationTypes: string[]) {
        let fields: string[] = [];

        organizationTypes.forEach(organizationType => {
            if (OrganizationFields.fields[String(organizationType)]) {
                const orgTypeFields = OrganizationFields.fields[String(organizationType)].map((field) => field && field.name);
                fields = [...fields, ...orgTypeFields];
            }
        });
        this.currentFields = new Set(fields);
    }

    getCurrentFields() {
        return this.currentFields;
    }

    static fieldValidator = (_: any, meta: Meta): boolean => {
        return meta.req.organizationFields && meta.req.organizationFields.has(meta.path);
    };

    static isValidOrganizationType = (organizationType: string): boolean => {
        return this.fields.hasOwnProperty(organizationType);
    };

    static checkOrganizationTypesMiddleware = async (req: Request, _: Response, next: NextFunction) => {
        try {
            // Check organizationTypes is in valid format or not
            const organizationTypeIds: string[] = req.body.organizationTypes;
            console.info("checkOrganizationTypesMiddleware start");
            console.info(req.body);

            if(!organizationTypeIds) {
                throw new MissingParametersError(["organizationTypes"]);
            }
            else if(!Array.isArray(organizationTypeIds) || organizationTypeIds.length === 0) {
                throw new BodyInvalidBadRequestError([{ name: "organizationTypes", value: organizationTypeIds, message: "Organization cannot be created because of missing organization type(s)." }]);
            }

            const isValid = organizationTypeIds.every(orgType => {
                return isValidMongoObjectId(orgType);
            });
            if (!isValid) {
                throw new InvalidResourceIdBadRequestError([{ name: "organizationTypes", value: organizationTypeIds, message: "These organization type ids are invalid.1" }]);
            }

            // Check organization types exist in the database
            const organizationTypes = await OrganizationType.find({ _id: { $in: organizationTypeIds } }).lean().exec();
            console.info(organizationTypes, "organizationTypes");

            if (organizationTypes.length !== organizationTypeIds.length) {
                throw new InvalidResourceIdBadRequestError([{ name: "organizationTypes", value: organizationTypeIds, message: "These organization type ids are invalid.2" }]);
            }

            // Get only names of organization types
            const organizationTypesNames = organizationTypes.map(orgType => orgType.name);
            console.info(organizationTypesNames, "organizationTypesNames");

            // Validate organization type names exist int the static fields
            const isValidOrganizationTypes = organizationTypesNames.every(organizationType => {
                return this.isValidOrganizationType(organizationType);
            });
            console.info(isValidOrganizationTypes, "isValidOrganizationTypes");

            if (!isValidOrganizationTypes) {
                throw new InvalidResourceIdBadRequestError([{ name: "organizationTypes", value: organizationTypeIds, message: "These organization type ids are invalid.3" }]);
            }

            // Create organization type Id to name map, so further we can use it in the API
            const organizationIdToNameMap = new Map<string, string>();
            organizationTypes.forEach(item => organizationIdToNameMap.set(String(item._id), item.name));

            // Prepare current set of fields applicable for the request
            const organizationFields = new OrganizationFields(organizationTypesNames);
            req.organizationFields = organizationFields.getCurrentFields();
            req.organizationIdToNameMap = organizationIdToNameMap;
            console.info("checkOrganizationTypesMiddleware end");
            next();
        }
        catch (error) {
            next(error);
        }
    };

    static moxfiveSponsorsMiddleware = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const checkMOXFIVESponsors = ["moxfivePMSponsor", "moxfiveTASponsor", "moxfiveSalesSponsor"];
            const errors: any = [];
            for(const field of checkMOXFIVESponsors) {
                if(req.body.hasOwnProperty(field) && req.body[String(field)]) {
                    const userId = req.body[String(field)];
                    const user = await User.findById(String(userId));
                    if (!user) {
                        errors.push({ name: field, value: userId, message: "User not found" });
                        continue;
                    }

                    const organization = await Organization.findUserAlreadyInAnyOrganization([user.azureId]);
                    if (!organization || (organization && process.env.MOXFIVE_ID !== String(organization._id))) {
                        errors.push({ name: field, value: userId, message: "User not found" });
                    }
                }
                if(errors.length > 0) {
                    throw new InvalidResourceIdBadRequestError(errors);
                }
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };

    static moxfiveSponsorsMiddlewareV2 = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const checkMOXFIVESponsors = ["moxfivePMSponsor", "moxfiveTASponsor", "moxfiveSalesSponsor"];
            const errors: any = [];
            for(const field of checkMOXFIVESponsors) {
                if(req.body.hasOwnProperty(field) && req.body[String(field)]) {
                    const userId = req.body[String(field)];
                    const user = await UserV2.findById(String(userId));
                    if (!user) {
                        errors.push({ name: field, value: userId, message: "User not found" });
                        continue;
                    }

                    const organization = await OrganizationV2.findUserAlreadyInAnyOrganization([user.azureId]);
                    if (!organization || (organization && process.env.MOXFIVE_ID !== String(organization._id))) {
                        errors.push({ name: field, value: userId, message: "User not found" });
                    }

                    req.body[String(field)] = { id: userId, value: user.name };
                }
                if(errors.length > 0) {
                    throw new InvalidResourceIdBadRequestError(errors);
                }
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };

    static canSendPartnerDetailsField = (_: any, meta: Meta) => {
        const activePartner = meta.req.body.activePartner;
        if (!activePartner && !meta.req.activePartner) {
            delete meta.req.body[meta.path];
            return false;
        }
        return true;
    };
}
