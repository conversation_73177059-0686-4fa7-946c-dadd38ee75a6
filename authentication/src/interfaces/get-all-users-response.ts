export interface GetAllUsersResponse {
  id: string;
  _id?: string;
  azureId?: string,
  email: string,
  isEnabled: boolean,
  firstName: string,
  lastName: string,
  streetAddress: string,
  country: string,
  state: string,
  city: string,
  postalCode: string,
  officePhone: string,
  alternateEmail: string,
  role?: string | null | {
    id: string,
    value: string
  },
  createdAt: string,
  updatedAt: string,
  isOwner?: boolean | null,
  version: number,
  jobTitle: string,
  companyName: string,
  lastSignIn: string,
  organization?: {
    _id?: string
    id?: string,
    name: string,
    organizationTypes?: {id: string, name: string} [] | string[]
  } | null,
  organizationTypes?: {_id: string, name: string} []
  keys?: string[],
  policyIds?: string[] | null,
}

