import { ReferenceValueV2 } from "@moxfive-llc/common";

export interface UserDetailsResponse {
    id: string;
    azureId?: string,
    email: string,
    isEnabled: boolean,
    firstName: string,
    lastName: string,
    streetAddress?: string,
    country?: string,
    state?: string,
    city?: string,
    postalCode?: string,
    officePhone: string,
    policyIds?: string[] | null,
    createdAt: string,
    updatedAt: string,
    isOwner?: boolean,
    version?: number,
    jobTitle: string | null,
    lastSignIn: string,
    organizationId?: string | null,
    role: string | {
        id: string,
        value: string
    } | null,
    organization?: {
        id: string,
        name: string,
        organizationTypes: string[] | null
    },
    connection?: {
        id: string,
        connectionType: ReferenceValueV2
    } | null,
    keys?: string[],
    passwordAuthentication?: boolean,
    externalAuth0LoginUserId?: string | null
}
