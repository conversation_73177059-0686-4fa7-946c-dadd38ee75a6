export interface GetUsersByPolicy {
  _id?: string;
  id?: string;
  azureId?: string,
  email: string,
  isEnabled: boolean,
  firstName: string,
  lastName: string,
  streetAddress: string,
  country: string,
  state: string,
  city: string,
  postalCode: string,
  officePhone: string,
  alternateEmail: string,
  policyIds?: string[] | null,
  createdAt: string,
  updatedAt: string,
  isOwner?: boolean,
  version?: number,
  jobTitle: string,
  companyName: string,
  lastSignIn: string
}
