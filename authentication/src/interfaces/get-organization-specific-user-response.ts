export interface GetOrganizationSpecificUserResponse {
  id?: string;
  azureId?: string,
  email?: string,
  isEnabled?: boolean,
  firstName?: string,
  lastName?: string,
  streetAddress?: string,
  country?: string,
  state?: string,
  city?: string,
  postalCode?: string,
  officePhone?: string,
  alternateEmail?: string,
  policyIds?: string[] | null,
  createdAt?: string,
  updatedAt?: string,
  jobTitle?: string,
  companyName?: string,
  lastSignIn?: string,
  role: string | {
    id: string,
    value: string
  } | null,
  isOwner?: boolean,
  version?: number,
  _id?: string,
  organization?: {
    id: string,
    name: string,
    favicon: string | null
  },
  keys?: string[]
  passwordAuthentication?: boolean,
  externalAuth0LoginUserId?: string | null
}

