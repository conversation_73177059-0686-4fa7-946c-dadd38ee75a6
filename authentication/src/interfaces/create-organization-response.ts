import { OrganizationLocation } from "./organization-location";
import { OrganizationTypeDoc } from "../models/organization-type";
import { OrganizationBillingAddress } from "./organization-billing-address";

export interface CreateOrganizationResponse {
	organizationTypes?: OrganizationTypeDoc[],
	owners?: number,
  members?: number,
  owner?: string[],
  member?: string[],
  id?: string,
  _id?: string,
  version?: number,
  azureId?: string,

  // Company Information
  name: string,
  organizationTypeIds?: string[] | OrganizationTypeDoc[],
  industry?: string[],
  website?: string,
  highLevelCompanyInformation?: string,
  descriptionOfEnvironment?: string,
  officeLocations?: OrganizationLocation[],
  numberOfEmployees?: number,
  numberOfITStaff?: number,
  itStaffLocation?: OrganizationLocation[],
  executiveSponsor?: string,
  technicalSponsor?: string,
  salesSponsor?: string,
  activePartner?: boolean,

  // Hotline details
  moxfiveHotline?: boolean,
  hotlineEmail?: string,
  hotlinePhoneNumber?: string,

  // Contact details
  msaSignatureDate?: string,
  billingContactName?: string,
  billingContactEmail?: string,
  billingContactPhone?: string,
  billingAddresses?: OrganizationBillingAddress[]

  // Partner details
  partnerType?: string[],
  onboardedDate?: string,
  coverageStates?: string[],
  offerings?: string[],
  partnerEula?: string,
  partnerTermsConditions?: string,
  inboundRequestInfo?: string,
  numberOfPMs?: number,
  numberOfLeads?: number,
  numberOfEngineers?: number,
  moxfivePMSponsor?: string,
  moxfiveTASponsor?: string,
  moxfiveSalesSponsor?: string,
  languages?: string[]
}
