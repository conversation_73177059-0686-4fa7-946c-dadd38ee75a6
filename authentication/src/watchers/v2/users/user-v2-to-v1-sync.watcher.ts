import { ChangeStreamDocument } from "mongodb";
import { User } from "../../../models/user";
import { UserV2 } from "../../../models/v2/users-v2";
import { UsersDeletedByWatcher } from "../../../models/users-deleted-by-watcher";

export const userV2ToV1SyncWatcher = async (doc: ChangeStreamDocument<any>) => {
    if(doc.operationType === "insert" || doc.operationType === "update") {
        // Find user
        const user = doc.fullDocument ?? (await UserV2.findById(doc.documentKey._id).lean().exec());
        if(user) {
            const data: Record<string, any> = { ...user };

            // Transofrm role, add organizationId
            if(data.role) {
                data.role = [data.role.id];
            }

            data.organizationId = data.organization.id;

            // Delete organization, organizationTypes, isOwner
            delete data.organization;
            delete data.organizationTypes;
            delete data.isOwner;

            // Update user data in v1
            await User.findByIdAndUpdate(doc.documentKey._id, data, { upsert: true });
        }
    }
    else if (doc.operationType === "delete") {
        const user = await User.findById(doc.documentKey._id).lean().exec();
        if(user) {
            await UsersDeletedByWatcher.build({
                ...user,
                _id: user._id as string,
                collectionName: "usersV2"
            }).save();
        }

        await User.findByIdAndDelete(doc.documentKey._id);
    }
};
